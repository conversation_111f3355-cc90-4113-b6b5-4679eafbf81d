{"name": "@turing-college/client", "version": "1.0.0", "private": true, "scripts": {"build:dev": "VITE_ENVIRONMENT=development npm run build", "build:prod": "VITE_ENVIRONMENT=production npm run build", "build:staging": "VITE_ENVIRONMENT=staging npm run build", "build": "vite build", "lint:fix": "npm run lint -- --fix", "lint": "eslint . --ext .ts,.tsx,.js", "local": "VITE_ENVIRONMENT=local VITE_IGNORE_MIXPANEL=true tsx ./localProxyServer.ts", "local-backend": "VITE_ENVIRONMENT=local-backend VITE_IGNORE_MIXPANEL=true vite", "preview": "VITE_ENVIRONMENT=development VITE_IGNORE_MIXPANEL=true vite build && vite preview --port 3000 --host localhost", "qa": "npm run lint && npm run type-check && npm run test", "start": "npm run local", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch", "test": "vitest run", "e2e": "TS_NODE_PROJECT=./e2e/tsconfig.e2e.json TS_NODE_TYPE_CHECK=true wdio run ./e2e/checks.config.ts", "type-check": "tsc", "build-gallery": "tsx ./src/stories/utils/generateGallery.ts", "storybook": "npm run build-gallery && storybook dev -p 6006", "build-storybook": "npm run build-gallery && storybook build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=20.15.0"}, "type": "module", "dependencies": {"@ant-design/icons": "^5.6.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/interaction": "^5.10.1", "@fullcalendar/react": "^5.10.1", "@fullcalendar/rrule": "^5.10.1", "@fullcalendar/timegrid": "^5.10.1", "@googlemaps/js-api-loader": "^1.16.8", "@growthbook/growthbook-react": "^1.2.1", "@reduxjs/toolkit": "^2.6.1", "@sentry/react": "^9.20.0", "@sentry/vite-plugin": "^3.4.0", "@tanstack/react-query": "5.68.0", "antd": "^5.24.9", "axios": "^1.8.2", "cloudinary-core": "^2.13.1", "cloudinary-react": "^1.8.1", "dayjs": "^1.11.13", "diacritics": "^1.3.0", "dompurify": "^3.2.6", "emotion-theming": "^10.0.27", "fast-memoize": "^2.5.2", "hamburger-react": "^2.5.0", "history": "^5.3.0", "language-list": "github:pandeysoni/language-list", "libphonenumber-js": "^1.12.9", "linkify-react": "^4.1.3", "linkifyjs": "^4.2.0", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "marked": "15.0.12", "mixpanel-browser": "^2.45.0", "prismjs": "^1.30.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-helmet": "^6.1.0", "react-highlight-words": "^0.21.0", "react-hotjar": "^6.3.1", "react-hotkeys": "^2.0.0", "react-password-checklist": "^1.8.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router": "^7.5.2", "react-svg": "^16.2.0", "react-use-intercom": "^5.4.3", "rebass": "^4.0.7", "rrule": "^2.8.1", "satismeter-loader": "^1.1.0", "typescript": "^5.8.2", "use-query-params": "^2.2.1", "vite": "^6.3.5", "web-vitals": "^4.2.4"}, "devDependencies": {"@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/blocks": "^8.6.14", "@storybook/builder-vite": "^8.6.12", "@storybook/manager-api": "^8.6.14", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/theming": "^8.6.12", "@storybook/types": "^8.6.14", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.2", "@types/diacritics": "^1.3.3", "@types/dompurify": "^3.2.0", "@types/express": "^5.0.2", "@types/google.maps": "^3.58.1", "@types/jsdom": "^21.1.7", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.15", "@types/mixpanel-browser": "^2.38.0", "@types/node": "^22.4.1", "@types/prismjs": "^1.26.0", "@types/react": "^18.3.18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.1.11", "@types/react-highlight-words": "^0.20.0", "@types/rebass": "^4.0.15", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.2", "@vitest/ui": "^3.2.2", "@wdio/cli": "^9.15.0", "@wdio/junit-reporter": "^9.14.0", "@wdio/local-runner": "^9.7.2", "@wdio/mocha-framework": "^9.0.6", "@wdio/spec-reporter": "^9.15.0", "confusing-browser-globals": "^1.0.11", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-unused-imports": "^4.1.4", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.0.0", "less": "^4.2.1", "prettier": "^3.5.3", "redux-mock-store": "^1.5.4", "storybook": "^8.6.12", "tsx": "^4.19.3", "typescript-eslint": "^8.32.1", "vite-plugin-prismjs": "^0.0.11", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4", "vitest-canvas-mock": "^0.3.3", "wdio-video-reporter": "^6.1.1", "wdio-wait-for": "^3.1.0"}}