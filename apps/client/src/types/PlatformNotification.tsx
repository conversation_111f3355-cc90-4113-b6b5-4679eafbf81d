import { UserSuspensionReason } from './User';

export enum PlatformNotificationType {
  CORRECTIONS_INTRO = 'corrections_intro',
  ENDORSEMENT_STAGE_STARTED = 'endorsement_stage_started',
  DEADLINE_FOUR_DAYS_REMAINING = 'deadline_four_days_remaining',
  DEADLINE_EXTENSION_REQUEST_CLOSED = 'deadline_extension_request_closed',
  LEARNING_SUSPENDED = 'learning_suspended',
  UNMET_STANDUP_ATTENDANCE_REQUIREMENT = 'unmet_standup_attendance_requirement',
  STANDUP_ATTENDANCE_REQUIREMENT_CHANGED = 'standup_attendance_requirement_changed',
  LEARNING_UPCOMING_SUSPENSION = 'learning_upcomming_suspension',
  MENTOR_UNSATISFIED_PRECONDITION = 'mentor_unsatisfied_precondition',
  WORKLOG_ENTRY_LOG_TIME_PROMPT = 'cost-tracking.worklog-entry-log-time-prompt',
  UZT_SCHEDULE_PENDING_APPROVAL = 'uzt_schedule.pending_approval',
  SCHEDULE_MISSING = 'schedule.missing',
  UZT_INFO_MODAL = 'uzt_info_modal',
  UZT_CONTRACT_TERM_MISSING = 'uzt_contract_term.missing',
  FIRST_LOGIN_WELCOME = 'first_login_welcome',
  GENERIC = 'generic',
  AI_CONSENT = 'privacy.ai_consent',
  AUTHORIZE_DISCORD = 'authorize_discord',
}

export type WorklogEntryLogTimePromptPayload = {
  type: 'review' | 'review-no-show';
  entryId: number;
  worklogId: number;
  activityStartedAt: Date;
  activityEndedAt: Date;
};

export type UnmetStandupAttendanceRequirementPayload = {
  streak: number;
  lastWeek: {
    target: number;
    milestoneId: number;
  };
};

export type StandupAttendanceRequirementChangedPayload = {
  newRequirement: number;
};

export type LearningSuspendedPayload = {
  reason: UserSuspensionReason;
};

export type LearningUpcomingSuspension = {
  suspensionDate: string;
};

export type MissingSchedulePayload = { groups: { name: string; from: string; to: string }[] };

export type NotificationPayload =
  | UnmetStandupAttendanceRequirementPayload
  | StandupAttendanceRequirementChangedPayload
  | LearningSuspendedPayload
  | LearningUpcomingSuspension
  | WorklogEntryLogTimePromptPayload
  | MissingSchedulePayload
  | null;

export interface PlatformNotification<PayloadType = NotificationPayload> {
  id: number;
  type: PlatformNotificationType;
  title: string;
  description: string;
  isAck: boolean;
  isSnoozed: boolean;
  snoozeCounter: number;
  snoozeLimit: number;
  createdAt: Date;
  payload: PayloadType;
}

// Util to narrow down the type of a notification to avoid casting in components
export const matchesNotificationType = <T extends NotificationPayload>(
  n: PlatformNotification | undefined,
  type: PlatformNotificationType,
): n is PlatformNotification<T> => n?.type === type;

export const isLearningSuspendedNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<LearningSuspendedPayload> =>
  matchesNotificationType<LearningSuspendedPayload>(n, PlatformNotificationType.LEARNING_SUSPENDED);

export const isWorklogEntryPromptNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<WorklogEntryLogTimePromptPayload> =>
  matchesNotificationType<WorklogEntryLogTimePromptPayload>(
    n,
    PlatformNotificationType.WORKLOG_ENTRY_LOG_TIME_PROMPT,
  );

export const isMentorUnsatisfiedPreconditionNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.MENTOR_UNSATISFIED_PRECONDITION);

export const isDeadlineFourDaysRemainingNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.DEADLINE_FOUR_DAYS_REMAINING);

export const isStandupRequirementChangedNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<StandupAttendanceRequirementChangedPayload> =>
  matchesNotificationType<StandupAttendanceRequirementChangedPayload>(
    n,
    PlatformNotificationType.STANDUP_ATTENDANCE_REQUIREMENT_CHANGED,
  );

export const isUnmetStandupsMissedNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<UnmetStandupAttendanceRequirementPayload> =>
  matchesNotificationType<UnmetStandupAttendanceRequirementPayload>(
    n,
    PlatformNotificationType.UNMET_STANDUP_ATTENDANCE_REQUIREMENT,
  );

export const isLearningUpcomingSuspensionNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<LearningUpcomingSuspension> =>
  matchesNotificationType<LearningUpcomingSuspension>(
    n,
    PlatformNotificationType.LEARNING_UPCOMING_SUSPENSION,
  );

export const isUztSchedulePendingApprovalNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.UZT_SCHEDULE_PENDING_APPROVAL);

export const isScheduleMissingNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<MissingSchedulePayload> =>
  matchesNotificationType<null>(n, PlatformNotificationType.SCHEDULE_MISSING);

export const isUztInfoModalNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.UZT_INFO_MODAL);

export const isUztContractTermMissingNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.UZT_CONTRACT_TERM_MISSING);

export const isFirstLoginWelcomeNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.FIRST_LOGIN_WELCOME);

export const isAiConsentNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.AI_CONSENT);

export const isAuthorizeDiscordNotification = (
  n: PlatformNotification<NotificationPayload> | undefined,
): n is PlatformNotification<null> =>
  matchesNotificationType<null>(n, PlatformNotificationType.AUTHORIZE_DISCORD);
