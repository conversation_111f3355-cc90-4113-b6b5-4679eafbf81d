export enum CorrectionStatus {
  EARN_POINTS = 'earn_points', // Slot is active, but user has not enough points to schedule it
  INACTIVE = 'inactive', // Correction cannot be scheduled (project not completed) - local status only
  ACTIVE = 'active', // Correction is open for scheduling - local status only
  PENDING = 'pending', // Correction is scheduled
  IN_PROGRESS = 'in_progress', // Correction is in progress
  PENDING_STUDENT_FEEDBACK = 'pending_student_feedback',
  SUCCESS = 'success',
  FAIL = 'fail',
  EVALUATOR_CANCELED = 'evaluator_canceled',
  STUDENT_CANCELED = 'student_canceled',
  EVALUATOR_NO_SHOW = 'evaluator_no_show',
  STUDENT_NO_SHOW = 'student_no_show',
}

export const cancelOrNoShowCorrectionStatuses = [
  CorrectionStatus.EVALUATOR_CANCELED,
  CorrectionStatus.EVALUATOR_NO_SHOW,
  CorrectionStatus.STUDENT_CANCELED,
  CorrectionStatus.STUDENT_NO_SHOW,
];

export enum CorrectionType {
  MENTOR = 'mentor',
  STUDENT = 'student',
}

export interface ReviewQuestion {
  id: number;
  sprintPartId: number;
  text: string;
  image?: string;
  evaluation?: number;
  weight: number;
  position: number;
}

export interface CorrectionAvailability {
  id: number;
  startsAt: Date;
  endsAt: Date;
}

export interface CorrectionBooking {
  id: number;
  start: Date;
  end: Date;
  status: CorrectionStatus;
  moduleName: string;
  projectName: string;
  eventId: number;
  isPeerCorrection: boolean;
  type: CorrectionType;
}

export enum ReviewEvaluatorFuturePreference {
  LESS_OFTEN = 'less_often',
  MORE_OFTEN = 'more_often',
  NO_PREFERENCE = 'no_preference',
}

export const reviewEvaluatorFuturePreferenceOptions: Array<{
  label: string;
  value: ReviewEvaluatorFuturePreference;
}> = [
  { label: 'Less often', value: ReviewEvaluatorFuturePreference.LESS_OFTEN },
  { label: 'No preference', value: ReviewEvaluatorFuturePreference.NO_PREFERENCE },
  { label: 'More often', value: ReviewEvaluatorFuturePreference.MORE_OFTEN },
];
