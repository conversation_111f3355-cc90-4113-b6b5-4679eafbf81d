import { useMutation } from '@tanstack/react-query';

import { ReviewEvaluatorFuturePreference } from '../../types/Correction';
import { createRequestHandler } from '../utils/createRequestHandler';

type Params = {
  reviewId: number;
};

type Payload = {
  feedback: string;
  internalFeedback?: string;
  evaluatorFutureReviewPreference: ReviewEvaluatorFuturePreference;
};

type Options = {
  onSuccess?: (data: void, variables: Params & Payload, context: unknown) => void;
  onError?: (error: Error, variables: Params & Payload, context: unknown) => void;
};

export const useCompleteReviewMutation = ({ onSuccess, onError }: Options) =>
  useMutation({
    mutationFn: ({
      reviewId,
      feedback,
      internalFeedback,
      evaluatorFutureReviewPreference,
    }: Params & Payload) => {
      const handleRequest = createRequestHandler<Payload, void>({
        endpoint: `/reviews/${reviewId}/complete`,
        method: 'put',
        data: { feedback, internalFeedback, evaluatorFutureReviewPreference },
      });
      return handleRequest();
    },
    onSuccess,
    onError,
  });
