@import '/node_modules/antd/dist/reset.css';
@import './vars.module.less';
@import './dark.module.less';
@import './prism.less';
@import './notification.less';
@import './satismeter.less';

* {
  font-feature-settings: 'tnum' 0 !important;
}

body.noOverflow {
  overflow: hidden;
}

html,
html body {
  background-color: @colorBgLayout;
  color-scheme: dark;
}

body {
  -webkit-font-feature-settings: 'tnum', 'tnum';
  font-feature-settings: 'tnum', 'tnum';
  background-color: #000;
  color: @colorText;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Helvetica Neue,
    Arial,
    Noto Sans,
    sans-serif,
    Apple Color Emoji,
    Segoe UI Emoji,
    Segoe UI Symbol,
    Noto Color Emoji;
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  position: relative;
  z-index: 0;
}

p {
  word-wrap: break-word;
}

a {
  -webkit-text-decoration-skip: objects;
  background-color: transparent;
  color: @colorPrimary;
  cursor: pointer;
  outline: none;
  text-decoration: none;
  transition: color 0.3s;
}

// Radio
// ---

.ant-radio-wrapper {
  &:hover {
    .ant-radio {
      .ant-radio-inner {
        border-color: @colorPrimary;
      }
    }
  }

  .ant-radio {
    .ant-radio-inner {
      background-color: @colorInputBg;
      border-color: @colorPrimaryBg;

      &::after {
        transform: scale(0.55);
        background-color: @colorPrimary;
      }
    }
  }
}

// Tooltip
// ---

// TODO (Justas) - tooltip css should be removed once we start tooltips from storybook
.ant-tooltip:not(.t-tooltip) {
  .ant-tooltip-inner {
    white-space: pre-wrap;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.35);
  }
  &.noTooltipPadding {
    .ant-tooltip-inner {
      padding: 0;
    }
  }
}

.hiring-profile-help {
  width: 400px;
  max-width: 400px;
}

.batchMentorsTooltip {
  .ant-tooltip-inner {
    width: 320px;
    background-color: @colorFillHover;
    padding: 10px;
  }
}

.tooltipWrap {
  width: fit-content;
}

// Calendar
// ---
.correctionTimeslotsModal,
.correctionScheduleModal,
.adminCalendar,
.mentorLandingCalendar {
  .ant-modal {
    .ant-modal-content {
      .ant-modal-body {
        padding: 0;
      }
      .ant-modal-close-x {
        display: flex;
        height: 51px;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .fc-theme-standard {
    height: 850px;
    max-height: calc(100vh - 130px);
    min-height: 400px;
    td {
      border: 0;
      border-right: 1px solid @colorBgLayout;
    }
    th {
      border: 1px solid @colorBgLayout;
    }
  }

  .calendar-loader-container {
    max-height: calc(100vh - 130px);
    height: 850px;
  }

  .fc-view {
    overflow: hidden;
  }

  //Shift form to the right, for the first calendar column, to avoid out of bounds presentation
  table .fc-col-header-cell:nth-child(2) .limit-form-wrapper {
    left: 0;
    right: auto;
  }

  .limit-form {
    .ant-btn.ant-btn-default.ant-btn-background-ghost {
      display: block;
      width: 28px;
      flex-shrink: 0;
    }
  }

  .ant-modal {
    width: 1300px !important;
    max-width: calc(100% - 30px);

    .ant-modal-close {
      top: 12px;
      right: 12px;
      height: 36px;
      width: 36px;
    }

    .ant-btn-link,
    .ant-modal-close {
      &:hover {
        background: transparent;
      }

      .icon-svg {
        background: @colorTextQuaternary;
        transition: color 0.2s;
      }

      &:hover .icon-svg {
        background: @colorText;
      }
    }
  }

  //Fix for calendar maximizing when using mouse with the scroll
  //For some reason FC calendar appends pseudo element with height in such cases
  //Applicable for chromium browsers only
  &.ant-modal-centered:has(.ant-modal-maximized):before {
    display: none;
  }

  .ant-modal.ant-modal-maximized {
    //We are using !important here, because FC calendar sets inline styles that we need to override
    width: 100vw !important;
    height: 100vh;
    max-width: initial;
    max-height: initial;

    .calendar-loader-container {
      height: 100dvh;
      max-height: initial;
    }

    .fc-theme-standard {
      max-height: initial;
      height: 100dvh !important;
    }
    th {
      border: 1px solid @colorBgLayout;
    }
  }

  .fc-view {
    overflow: hidden;
  }

  //Shift form to the right, for the first calendar column, to avoid out of bounds presentation
  table .fc-col-header-cell:nth-child(2) .limit-form-wrapper {
    left: 0;
    right: auto;
  }

  .calendar-filter {
    position: absolute;
    top: 115px;
    right: 8px;
    z-index: 1;

    @media (max-width: 768px) {
      top: 168px;
    }

    label {
      width: 75px;
      text-align: center;
      font-size: 13px;
      font-weight: 400;
      color: @colorTextQuaternary;

      &:hover {
        color: @colorText;
      }

      &.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        background: @colorFill;
        border-color: @colorFill;

        &:before {
          background-color: @colorFillHover;
        }

        &:hover {
          background: @colorFillHover;
          border-color: @colorFillHover;
        }
      }
    }
  }

  .ant-modal-body {
    padding: 0;

    @media (max-width: 414px) {
      .points-label {
        display: none;
      }
    }
  }

  .ant-modal-footer {
    display: none;
  }

  //FC CALENDAR STYLES
  .fc-header-toolbar {
    padding: 9px 20px 10px 20px;
    min-height: 65px;
    margin-bottom: 0 !important;

    .fc-toolbar-chunk {
      display: flex;

      .fc-button {
        border: 0;
        background: @colorFill;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @colorText;
        padding: 0 12px;
        height: 38px;
        font-family: 'HelveticaNowText-Medium', sans-serif;

        &:hover {
          background: @colorFillHover;
          color: @colorTextSecondary;
        }

        &:focus {
          box-shadow: none;
        }

        &:disabled {
          opacity: 0.5;
          background: @colorFill!important;
          color: @colorTextQuaternary!important;
          cursor: not-allowed !important;
          span {
            color: @colorTextQuaternary!important;
          }
        }

        &.fc-prev-button,
        &.fc-next-button {
          background: none;
          margin: 0;
          width: 20px;
          height: 38px;
          width: 38px;

          &:hover {
            background: @colorFillHover;
            color: @colorTextSecondary;
          }

          &:focus {
            box-shadow: none;
          }

          @media (max-width: 768px) {
            &:hover {
              background: transparent;
              color: @colorText;
            }
          }
        }

        &.fc-currentWeek-button {
          width: 62px;
          height: 37px;
          font-family: 'HelveticaNowText-Regular', sans-serif;
          font-size: 14px;
          line-height: 1;
          color: @colorText;
          text-transform: capitalize;
          cursor: pointer;
          margin: 0 6px 0 0;
        }
      }

      //Currently displayed title
      &:nth-of-type(2) {
        margin: 0 auto 0 15px;
        .fc-toolbar-title {
          color: @colorText;
          font-family: 'HelveticaNowDisplay-Md', sans-serif;
          font-size: 16px;
          line-height: 1.5;
        }
      }

      @media (max-width: 510px) {
        .fc-toolbar-title {
          display: none;
        }
      }
    }
  }

  //TEMP fix (will be refactored in next stage of release)
  .fc-timegrid-event .fc-event-main {
    overflow: hidden;
  }

  .fc-theme-standard {
    max-height: calc(100vh - 130px);
    min-height: 400px;

    td {
      border: 0;
      border-right: 1px solid @colorBgLayout;
    }

    th {
      border: 1px solid @colorBgLayout;
    }
  }

  //Event block backdrop
  .fc-timegrid-event-harness {
    &:before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: @colorBgDropped;
      z-index: -1;
      border-radius: 4px;
    }
  }

  //Event blocks
  .fc-timegrid-event {
    border-radius: 4px;
    position: relative;
    background-color: @colorCalendarEvent;
    border: 1px solid @colorBgDropped;
    transition: background-color 0.2s ease-in-out;
    cursor: default;
    overflow: hidden;
    margin: 0;

    &:hover {
      &.fc-timegrid-event {
        background-color: @colorCalendarEventHover;
        cursor: pointer;
      }
    }

    &.open_session {
      background: @colorCalendarEventSession;
      border: 1px solid @colorCalendarEvent;

      &:hover {
        background: @colorCalendarEventSessionHover;
      }

      .calendar-event-name {
        color: @colorCalendarBase;
      }

      .calendar-event-time {
        color: @colorCalendarBase;
        opacity: 0.7;
      }
    }

    &.timeslot {
      background: fade(@colorPrimaryText, 15);
      border: none;

      &:hover {
        background: fade(@colorPrimaryText, 20);
      }

      &.schedule:hover {
        background-color: @colorPrimary;
      }

      .fc-event-time {
        color: @colorPrimaryText;
      }
    }

    &.correction {
      background: fade(@colorPrimaryText, 60);
      border: none;

      &:hover {
        background: fade(@colorPrimaryText, 65);
      }

      .fc-event-time {
        color: @colorPrimaryText;
      }
    }

    .fc-event-main {
      height: fit-content;
      padding: 6px 6px 3px 6px;
      height: 100%;
    }

    .calendar-event-time {
      font-family: 'HelveticaNowMicro-Regular', sans-serif;
      font-size: 11px;
      line-height: 1.25;
      color: @colorText;
      opacity: 0.5;
    }
  }

  .fc-scroller {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .fc-scroller::-webkit-scrollbar {
    width: 0px !important; // Remove scrollbar
  }

  //Firefox calendar header override to show Review limit dropdown
  .fc-scrollgrid-section-header {
    .fc-scroller-harness,
    .fc-scroller {
      overflow: visible !important;
    }
  }

  .fc thead .fc-scroller-harness {
    overflow: visible;
  }

  .fc thead .fc-scroller {
    overflow: visible !important;
  }

  table {
    border: 0 !important;

    .fc-col-header {
      background-color: @colorBgLayout;
    }

    .fc-timegrid-axis {
      border: 0;
    }

    .fc-col-header-cell {
      border: 0;

      .fc-col-header-cell-cushion {
        color: @colorTextQuaternary;
        font-family: HelveticaNowText-Regular;
        font-size: 13px;
        padding: 8px 0;
        cursor: default;
        user-select: none;
        &:focus {
          cursor: default;
        }
      }
    }

    tbody {
      .fc-scrollgrid-section {
        background-color: @colorBgDropped;
        > td {
          border: 0;
        }
      }

      .fc-timegrid-slots {
        tr {
          border: 0;
          &:nth-of-type(2n) .fc-timegrid-slot-lane {
            border-bottom: 1px solid @colorTagBg;
          }
          &:nth-of-type(4n) .fc-timegrid-slot-lane {
            border-bottom: 1px solid @colorBgLayout;
          }
        }

        .fc-timegrid-slot {
          height: 25px;
          border: 0;

          &.fc-timegrid-slot-label {
            margin-top: -11px;
            width: 61px;
            display: block;
            .fc-timegrid-slot-label-frame {
              width: 61px;
            }
          }

          .fc-timegrid-slot-label-cushion {
            font-family: 'HelveticaNowMicro-Regular', sans-serif;
            font-size: 10px;
            line-height: 1.5;
            color: @colorTextQuaternary;
            padding: 0 9px;
            cursor: default;
          }
        }
      }

      .fc-timegrid-cols {
        > table {
          height: 100%;
        }

        .fc-timegrid-col {
          &.fc-timegrid-axis {
            border: 0;
          }

          &.fc-day-today {
            background-color: @colorBgDropped;
          }

          .fc-timegrid-now-indicator-arrow {
            display: none;
          }

          .fc-highlight {
            display: none;
          }

          .fc-timegrid-now-indicator-line {
            border-color: @colorAccent2;

            &::before {
              content: '';
              display: block;
              background-color: @colorAccent2;
              border-radius: 100%;
              height: 6px;
              width: 6px;
              margin-top: -3px;
            }
          }
        }

        .fc-timegrid-event-harness-inset[style*='z-index: 2'] {
          .calendar-event-name {
            -webkit-line-clamp: 1;
          }
        }

        .fc-timegrid-event-harness-inset[style*='z-index: 3'] {
          .calendar-event-name {
            -webkit-line-clamp: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .fc-event-main {
            > div > div {
              max-width: 100%;
            }
          }
        }

        .fc-timegrid-event-harness-inset .fc-timegrid-event,
        .fc-timegrid-event.fc-event-mirror {
          box-shadow: none;
        }
      }

      .fc-non-business {
        background-color: @colorInputBorder;
      }
    }
  }
}

.adminCalendar {
  .fc-theme-standard {
    height: calc(100vh - 130px) !important;
  }
}

.mentorLandingCalendar {
  .fc-timegrid-event {
    transition: background-color 0.2s ease-in-out;
  }

  .fc-theme-standard {
    height: calc(100vh - 65px) !important;
    max-height: calc(100vh - 65px) !important;
  }

  .calendar-filter {
    top: 178px;

    @media (max-width: 991px) {
      top: 174px;
    }
  }

  @media (max-width: 414px) {
    .fc-header-toolbar {
      position: relative;
      .fc-toolbar-title {
        color: @colorText;
        position: absolute;
        top: 9px;
        left: 20px;
      }
    }

    .limitExplainer {
      top: 155px !important;
    }
  }
}

.mentorLandingCalendar.impersonating {
  .fc-theme-standard {
    height: calc(100vh - 65px - 41px) !important;
    max-height: calc(100vh - 65px - 41px) !important;
  }
}

//Other modals [DIRTY]
.settingsModal {
  .ant-modal-body {
    padding: 0 !important;
    height: calc(100vh - 65px);
  }
  @media (max-width: 768px) {
    max-width: 100vw;
    margin: 0;
    .ant-modal-body {
      height: 100vh;
      max-height: 100vh;
    }
  }
}

.aiConsentModal {
  .ant-modal-body {
    padding: 0 !important;
  }
}

.reviewEvaluatorFeedbackModal .ant-modal-body {
  padding: 30px;
}

.correctionScheduleModal .ant-modal-body table tbody {
  .fc-timegrid-slots tr .fc-timegrid-slot-lane {
    border-bottom: 1px solid @colorTagBg;
  }

  .fc-timegrid-cols {
    // When there are >3 overlapped events
    .fc-timegrid-event-harness-inset[style*='z-index: 3'] {
      .fc-timegrid-event:not(.scheduled) {
        .fc-event-main {
          .eventDetails {
            > div:nth-of-type(1) {
              font-size: 12px;
            }
            > div:nth-of-type(2) {
              font-size: 10px;
            }
            transform: translateY(-80px);
          }
        }
      }
    }

    .fc-timegrid-event-harness-inset[style*='z-index: 4'] {
      .fc-timegrid-event:not(.scheduled) {
        .fc-event-main {
          .eventDetails {
            transform: translateY(-100px);
            > div:nth-of-type(1) {
              font-size: 12px;
            }
            > div:nth-of-type(2) {
              font-size: 10px;
            }
          }
        }
      }
    }

    .fc-timegrid-event-harness {
      border-radius: 4px;

      .fc-timegrid-event:not(.scheduled),
      .fc-timegrid-event:not(.scheduled).fc-event-dragging,
      .fc-timegrid-event:not(.scheduled).fc-event-dragging:not(.fc-event-selected) {
        transition: all 0.2s ease-in-out;
        height: 23px;
        margin: 1px 0 0;

        .fc-event-main {
          padding: 0;

          .eventDetails {
            transform: translateY(-50px);
            transition: all 0.2s ease-in-out;
          }

          .svg {
            display: flex;
            border-radius: 2px;
            height: 100%;
            width: 100%;
            align-items: center;
            justify-content: center;
            position: absolute;
          }
        }
      }

      &:hover {
        z-index: 999 !important;
        .fc-timegrid-event:not(.scheduled),
        .fc-timegrid-event:not(.scheduled).fc-event-dragging,
        .fc-timegrid-event:not(.scheduled).fc-event-dragging:not(.fc-event-selected) {
          height: 75px; // 25px is 15min
          .fc-event-main {
            .eventDetails {
              transform: translateY(0);
            }
            .svg {
              opacity: 0;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .correctionTimeslotsModal,
  .correctionScheduleModal {
    overflow: hidden;
    z-index: 1000;
    .ant-modal {
      vertical-align: top;
      max-width: 100vw;
      margin: 0;
      overflow: hidden;
    }
    .fc-theme-standard {
      height: 100vh !important;
      max-height: calc(100vh - 150px);
    }
  }
}

// Drawer
// ---
.ant-drawer-left {
  &.menuDrawer {
    // This is to make sure drawer is not visible above mobile resolution
    @media (min-width: 992px) {
      display: none;
    }

    .ant-drawer-wrapper-body {
      .ant-drawer-body {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &.ant-drawer-open .ant-drawer-content-wrapper {
    box-shadow: none;
  }

  .ant-drawer-content-wrapper {
    box-shadow: none;

    .ant-drawer-body {
      padding: 0;
    }

    .ant-drawer-content {
      background-color: @colorBgLayout;
    }
  }
}

.notificationsDrawer {
  .ant-drawer-content {
    overflow: hidden;
    background-color: @colorBgElevated;
    .ant-drawer-body {
      padding: 0;
      overflow: hidden;
      background-color: @colorBgElevated;
      > div {
        position: absolute;
        top: 0;
      }
    }
  }
}

@media (max-width: 414px) {
  .notificationsDrawer {
    .ant-drawer-content-wrapper {
      width: 100% !important;
    }
  }
}

.settingsNavigationDrawer {
  z-index: 1009;
  @media (min-width: 992px) {
    .ant-drawer-mask {
      display: none;
    }
  }
}

// Input / Picker / Select
// ---
textarea {
  ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.ant-input,
.ant-picker,
.ant-input-affix-wrapper {
  padding: 7px 9px 5px 9px;
  font-family: 'HelveticaNowText-Regular';
  line-height: 1.5;
  font-size: 14px;
  color: @colorText;
  border: 1px solid @colorInputBorder;
  background-color: @colorInputBg;

  &::placeholder {
    color: @colorTextQuaternary;
  }

  &.ant-input-affix-wrapper-focused,
  &:focus,
  &:focus-within {
    box-shadow: none;
  }

  &.ant-input-affix-wrapper-disabled {
    &:hover {
      background-color: @colorInputBg;
    }
    &.ant-input-affix-wrapper-focused,
    &:focus,
    &:focus-within {
      border-color: @colorInputBorder !important;
    }
  }
}

.ant-input-number-affix-wrapper {
  background-color: @colorInputBg !important;
}

.ant-input-number {
  color: @colorText;
  border: 1px solid @colorInputBorder;
  background-color: @colorInputBg;
  box-shadow: none;

  &:focus,
  &.ant-input-number-focused {
    border-color: @colorPrimary;
    box-shadow: none;
    outline: none;
  }
  &.ant-input-number-disabled {
    background-color: @colorInputBg !important;
  }
}

.ant-picker-focused {
  box-shadow: none;
}

.ant-picker-dropdown {
  .ant-picker-cell-in-view.ant-picker-cell-range-hover,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start,
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end {
    &::after,
    &::before,
    .ant-picker-cell-inner::after,
    .ant-picker-cell-inner::before {
      border-top: unset !important;
      border-bottom: unset !important;
      background: @colorPrimaryBgHover !important;
    }
  }
}

.ant-input:focus,
.ant-input-focused {
  box-shadow: none;
}

.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: @colorPrimary!important;
  box-shadow: none;
}

//Input disabled custom styles
.ant-select-disabled .ant-select-selector,
.ant-input[disabled],
.ant-input-number-input[disabled],
.ant-picker[disabled],
.ant-picker-disabled,
.ant-picker-input > input[disabled],
.ant-input-affix-wrapper[disabled] {
  color: @colorText !important;
  opacity: 0.6;
  background-color: @colorInputBg !important;
  &::placeholder {
    color: @colorTextQuaternary !important;
  }
}

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: @colorInputBg;
}

.ant-select:not(.ant-select-customize-input).ant-select-disabled .ant-select-selector {
  color: @colorText;
}

.ant-select {
  &.ant-select-focused:not(.ant-select-disabled):not(.ant-select-customize-input):not(
      .ant-pagination-size-changer
    )
    .ant-select-selector {
    box-shadow: none;
  }
}

.ant-input-affix-wrapper {
  padding: 0;

  > input.ant-input {
    border-radius: inherit;
  }

  input,
  .ant-input-suffix {
    padding: 7px 9px 5px 9px !important;
  }
}

.ant-switch {
  &.danger {
    &:hover {
      background-color: @colorErrorHover;
    }

    &.ant-switch-checked {
      background-color: @colorError;

      &:hover {
        background-color: @colorErrorHover;
      }
    }
  }
}

.loginForm .ant-input-password {
  input[type='text'],
  input[type='password'] {
    font: inherit;
    transition: none;
  }

  input[type='password']:not(:placeholder-shown) {
    font:
      large Verdana,
      sans-serif;
  }
}

input[type='password']:not(:placeholder-shown) {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    segoe ui,
    Roboto,
    helvetica neue,
    Arial,
    noto sans,
    sans-serif,
    apple color emoji,
    segoe ui emoji,
    segoe ui symbol,
    noto color emoji;
}

.ant-form-item-explain-mixin {
  font-family: 'HelveticaNowMicro-Regular';
  color: @colorError;
  font-size: 11px;
  line-height: 1.25;
  text-align: left;
  margin: 2px 0 0;
}

.ant-form-item-has-error {
  .ant-input-affix-wrapper {
    border-color: @colorError !important;
  }

  .ant-input {
    border-color: @colorError !important;
  }

  .ant-form-item-explain {
    min-height: unset;
    div {
      .ant-form-item-explain-mixin();
    }
  }

  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector {
    border-color: @colorError !important;
  }
}

.ant-form-item-explain div,
.ant-form-item-extra {
  .ant-form-item-explain-mixin();
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  -webkit-text-fill-color: @colorText;
  background-color: @colorBgDropped;
  -webkit-box-shadow: inset 0 0 0 30px @colorBgDropped !important;
  box-shadow: inset inset 0 0 0 30px @colorBgDropped !important;
}

.inputWithCopy {
  .ant-input-suffix {
    padding: 0 !important;
  }
  input {
    text-overflow: ellipsis;
  }
}

// Form
.ant-form-item {
  margin-bottom: 19px;
}

// Buttons
// ---
// TODO (Justas) - all this css can be removed once we start buttons from storybook
.ant-btn:not(.t-button) {
  font-family: 'HelveticaNowText-Regular', sans-serif;
  font-size: 14px;
  line-height: 1;
  height: 36px;
  color: @colorLight;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: none;

  &.ant-btn-primary {
    font-family: 'HelveticaNowText-Medium', sans-serif;

    &:hover,
    &:focus {
      background: @colorPrimaryHover;
      border-color: @colorPrimaryHover;
    }
    &:disabled {
      background: @colorPrimary;
      color: @colorText;
      opacity: 0.4;
    }
  }

  &.ant-btn-default.ant-btn-background-ghost,
  &.ant-btn-default.ant-btn-background-ghost:not(:disabled) {
    font-family: 'HelveticaNowText-Medium', sans-serif;
    background: @colorFill;
    color: @colorText;
    border: none;

    &:hover,
    &:focus {
      background: @colorFillHover;
      border: none;
    }
    &:disabled {
      background: @colorFill;
      color: @colorTextQuaternary;
      opacity: 0.5;
    }

    .text {
      font-family: 'HelveticaNowText-Medium', sans-serif;
    }
  }

  &.ant-btn-text {
    line-height: 1.5;
    font-family: 'HelveticaNowText-Regular', sans-serif;
    font-size: 14px;
    color: @colorTextQuaternary;
    padding: 0 12px;
    &:hover,
    &:focus {
      color: @colorTextSecondary;
      background: @colorFill;
    }
    &.ant-btn-lg {
      font-family: 'HelveticaNowText-Regular', sans-serif;
    }
  }

  &.ant-btn-warning {
    background: @colorAccentBg;
    color: @colorAccent;

    &:hover {
      background: @colorAccentBgHover;
    }
  }

  &.ant-btn-default.ant-btn-dangerous {
    font-family: 'HelveticaNowText-Medium', sans-serif;
    background: @colorError;
    border-color: @colorError;
    color: @colorText;

    &:hover:not(:disabled),
    &:focus:not(:disabled) {
      color: @colorLight;
      background: @colorErrorHover;
      border-color: @colorErrorHover;
    }

    &:disabled {
      opacity: 0.4;
    }
  }

  &.ant-btn-default.ant-btn-dangerous:not(:disabled) {
    color: @colorLight;
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 8px 12px;
  }
}

.fc-button-primary {
  font-family: 'HelveticaNowText-Medium', sans-serif;
}

// Table
// --
@table-header-bg: @colorBgElevated;

.ant-table {
  background-color: transparent !important;
  overflow: auto;
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid @colorBgElevated;
  }
  .ant-table-tbody > tr.ant-table-placeholder,
  .ant-table-tbody > tr.ant-table-placeholder:hover,
  .ant-table-tbody > tr.ant-table-placeholder:hover > td {
    background-color: transparent;
  }
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: transparent;
  }
  .ant-table-column-sorter-inner {
    svg {
      color: @colorText;
    }
    .ant-table-column-sorter-up.active svg,
    .ant-table-column-sorter-down.active svg {
      color: @colorPrimary;
    }
  }
  .ant-table-filter-trigger-container-open {
    .ant-table-filter-trigger {
      &.ant-dropdown-open {
        background-color: @colorBgLayout;
      }
      &.ant-dropdown-open svg {
        color: @colorPrimary;
      }
    }
  }
  .ant-table-filter-trigger {
    transition: background-color 0.2s ease-in-out;
    &:hover,
    &.active {
      background-color: @colorBgLayout;
    }
    svg {
      color: @colorText;
    }
    &.active svg {
      color: @colorPrimary;
    }
  }
  thead > tr > th {
    background-color: @colorBgElevated !important;
  }
  .ant-table-thead th {
    &.ant-table-column-sort {
      background-color: @colorBgContainer !important;
    }
    &.ant-table-column-has-sorters:hover {
      background-color: @colorBgContainer !important;
      .ant-table-filter-trigger-container {
        background-color: @colorBgContainer !important;
      }
    }
  }
  .ant-table-tbody td {
    &.ant-table-column-sort {
      background-color: transparent;
    }
  }
  &::-webkit-scrollbar {
    height: 9px;
    background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
    height: 9px;
    border-radius: 30px;
    -webkit-border-radius: 30px;
    background-color: @colorBgElevated;
  }
}
.ant-table-wrapper.new-table-design {
  .ant-table {
    .ant-table-thead th {
      padding: 0;
      background-color: transparent !important;
      border: none !important;
      &::before {
        background-color: transparent !important;
      }
    }
    .ant-table-tbody tr {
      td {
        padding: 8px 0 !important;
        border: none !important;
      }
      td:first-of-type {
        padding-left: 6px !important;
      }
      &.ant-table-row:hover > td {
        background: transparent !important;
      }
    }
    @media (max-width: 768px) {
      .ant-table-tbody .ant-table-row .ant-table-cell {
        &:nth-of-type(1) {
          width: 60%;
        }
        &:nth-of-type(2) {
          width: 40%;
        }
      }
    }
    @media (max-width: 414px) {
      .ant-table-tbody .ant-table-row .ant-table-cell {
        &:nth-of-type(1) {
          width: 50%;
        }
      }
    }
  }
}

tr.ant-table-expanded-row > td,
tr.ant-table-expanded-row:hover > td {
  background-color: transparent;
  .ant-table-thead > tr > th {
    background-color: transparent;
    border-bottom: 1px solid @colorBgElevated;
  }
}

.ant-table-expanded-row-level-1 > td > div {
  margin-bottom: 10px;
}

// Modal
@modal-header-bg: @colorBgLayout;
@modal-header-border-color-split: @colorBorderSecondary;
@modal-content-bg: @colorBgLayout;
@modal-footer-border-color-split: transparent;

.ant-modal-close {
  top: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ant-modal-close-x {
  color: @colorTextQuaternary;
  img {
    height: 20px;
    width: 20px;
  }
}

.ant-modal-mask,
.ant-drawer-mask {
  background-color: rgba(19, 19, 19, 0.7);
}

.ant-modal {
  color: @colorText;

  .ant-modal-title {
    color: @colorText;
  }

  .ant-modal-header {
    padding: 13px 18px;
    border-bottom: 0.5px solid @colorBorderSecondary;
    background-color: @colorBgLayout;
  }

  .ant-modal-content {
    box-shadow: @modalShadow;
    background-color: @colorBgLayout;
    border-radius: 6px;
    padding: 0;
    border: 1px solid @colorBgElevated;
    .ant-modal-body {
      padding: 44px 24px 40px 24px;
    }
    .ant-modal-footer {
      padding: 0 24px 24px 24px;
    }
  }
}

.modelModal {
  .ant-modal-body {
    .ant-select-selection-item {
      display: flex;
      align-items: center;
      > div {
        font-size: 16px; // Is hardcoded because cannot change styles of a selected el. This is essentially a <BodyText1>
      }
    }
  }
}

.manualAvailabilityModal {
  .ant-modal-content {
    .ant-modal-body {
      padding: 21px 18px 15px 18px;
    }
    .ant-modal-footer {
      padding: 14px;
      border-top: 0.5px solid @colorBorderSecondary;
    }
  }
}

.deadlinesModal {
  .ant-modal-close {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    background-color: @colorBgLayout;
    border-radius: 6px;
    z-index: 100;
  }

  .ant-modal {
    height: 750px;
    max-height: 95vh;
    .ant-modal-content,
    .ant-modal-body {
      height: 100%;
      overflow: auto;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
      ::-webkit-scrollbar {
        width: 0px !important; // Remove scrollbar
      }
    }
  }
}

// Menu
// ---
@menu-dark-bg: @colorBgElevated;
@menu-dark-submenu-bg: @colorBgElevated;
@menu-popup-bg: @colorBgElevated;

.ant-dropdown .ant-dropdown-menu {
  border: 1px solid @colorBorder;

  &.navigationMenu .ant-dropdown-menu-item {
    padding: 0;
  }

  .ant-dropdown-menu-item-group {
    margin: 8px 0;
  }

  .ant-dropdown-menu-item-group-title {
    padding: 0;
  }

  .ant-dropdown-menu-item-group:not(:first-child) {
    &:before {
      content: '';
      border-top: 1px solid @colorBorder;
      display: block;
      margin: 8px;
    }
  }
}

.ant-dropdown-menu {
  background-color: @colorBgElevated;

  .ant-dropdown-menu-item:hover,
  .ant-dropdown-menu-submenu-title:hover {
    background-color: @colorBgContainer;
  }

  .ant-dropdown-menu-item-selected {
    color: @colorText !important;
  }
}

.dropdownLightHover .ant-dropdown-menu {
  box-shadow: 0px 2px 10px rgba(24, 24, 24, 0.3);
  .ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled):hover {
    background-color: @colorPrimaryBg;
  }
  .ant-dropdown-menu-item-disabled:hover {
    background-color: @colorBgElevated;
  }
}

.cancelReportCorrection {
  .ant-dropdown-menu {
    width: 288px;
    .ant-dropdown-menu-item {
      padding: 6px 12px 8px 12px;
      .ant-dropdown-menu-title-content {
        display: flex;
        align-items: center;
      }
      // Parent of SVG
      .noShowButtonIcon,
      .cancelButtonIcon {
        > div {
          display: flex;
          align-items: center;
        }
        svg path {
          fill: @colorText;
        }
      }
      &.ant-dropdown-menu-item-disabled {
        * {
          color: @colorPrimaryBg;
        }
        svg path {
          fill: @colorPrimaryBg;
        }
      }
    }
  }
}

.navigationMenu {
  width: 288px;
  padding: 12px 6px;
  border-radius: 4px;
  box-shadow: @modalShadow;
  .navigationMenuGroup {
    &:not(:last-of-type) {
      padding-bottom: 8px;
      border-bottom: 1px solid @colorFill;
    }
    &:not(:first-of-type) {
      padding-top: 8px;
    }
  }
  .ant-dropdown-menu-item {
    padding: 6px;
    border-radius: 4px;
    &:hover {
      background-color: @colorPrimaryBg;
    }
  }
}

.desktopNavigation {
  .navItemCalendar {
    position: relative;
    margin-left: 26px;

    &:hover,
    &.active {
      path {
        fill: @colorText;
      }
    }

    &:before {
      content: '';
      border-left: 1px solid @colorBorderSecondary;
      position: absolute;
      left: -26px;
      top: 0;
      height: 100%;
    }
  }
}

.ant-dropdown-trigger {
  .arrow {
    transition: all 0.2s ease-in-out;
  }
  &.ant-dropdown-open .arrow {
    transform: rotate(180deg);
  }
}

// Pagination
// ---
@pagination-item-bg: @colorBgElevated;
@pagination-item-bg-active: @colorPrimary;
@pagination-item-link-bg: @colorBgElevated;

.ant-pagination {
  .ant-pagination-disabled a span {
    color: @colorInputBorder;
  }
  .ant-pagination-prev,
  .ant-pagination-next {
    button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:hover {
      a {
      }
    }
    &:hover a,
    a {
      color: @colorText;
      font-family: 'HelveticaNowText-Regular', sans-serif;
      font-size: 14px;
    }
  }
  .ant-pagination-item {
    border-radius: 3px;
    color: @colorText;
    a {
      color: @colorText;
      font-family: 'HelveticaNowText-Regular', sans-serif;
      font-size: 15px;
    }
    &.ant-pagination-item-active,
    &.ant-pagination-item-active a {
      color: @colorText;
    }
  }
  .ant-pagination-options {
    .ant-select {
      &.ant-select-single.ant-select-open .ant-select-selection-item {
        opacity: 1;
      }

      .ant-select-selector {
        background: @colorBgElevated;
        min-height: 32px;
        .ant-select-selection-item {
          color: @colorText;
          font-family: 'HelveticaNowText-Regular', sans-serif;
          font-size: 14px;
        }
        .ant-select-arrow {
          color: @colorText;
        }
      }
    }
  }
  &.mini {
    display: flex;
    > * {
      &:not(:last-of-type) {
        margin-right: 4px;
      }
    }
    .ant-pagination-item {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease-in-out;
      > a {
        color: @colorLight;
        font-family: 'HelveticaNowMicro-Regular', sans-serif;
        font-size: 11px;
        line-height: 1.25;
        @media (max-width: 1200px) {
          font-size: 12px;
        }
      }
      &:hover {
        background-color: @colorBgElevated;
        border-color: @colorBgElevated;
      }
      &.ant-pagination-item-active {
        background-color: @colorBgContainer;
        border-color: @colorBgContainer;
      }
    }
    .ant-pagination-item-link {
      color: @colorTextSecondary;
      svg {
        width: 10px;
        height: 10px;
      }
      &:disabled {
        color: @colorTextQuaternary;
      }
    }
  }
}

// Dropdown
// ---

.ant-select-dropdown {
  background-color: @colorBgElevated;
  border: 1px solid @colorBorder;

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: @colorPrimary;
    font-weight: 400;
  }
  .ant-select-item-option-active {
    background-color: @colorPrimaryBg;
  }
  .ant-select-item {
    color: @colorText;
    font-family: 'HelveticaNowText-Regular';
    font-weight: 400;
    font-size: 14px !important;
    line-height: 1.25;
    padding: 8px 12px;
  }
  .ant-select-item-group {
    color: @colorTextQuaternary;
    font-family: 'HelveticaNowText-Regular';
    font-size: 14px !important;
    line-height: 1.5;
    padding: 8px 12px 2px 12px;
  }
  .new-tag-item {
    .ant-select-item-option-content {
      text-transform: uppercase;
    }
  }
}

.ant-select-dropdown:has(.ant-menu) {
  padding: 0px;
}

// Select
// ---
@select-background: @colorBgDropped;
@select-dropdown-bg: @colorBgElevated;
@select-clear-background: @colorBgElevated;
@select-selection-item-bg: @colorBgLayout;
@select-selection-item-border-color: @colorTextQuaternary;

.ant-select-single .ant-select-selection-search-input {
  font-family: 'HelveticaNowText-Regular';
  font-size: 14px;
  line-height: 22.5px;
  color: @colorText;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: @colorBgLayout;
}

.ant-select-multiple .ant-select-selection-item {
  color: @colorText;
  background-color: @colorFill;
  border-radius: 2px;
  font-family: 'HelveticaNowText-Regular';
  font-size: 14px;
  @media (max-width: 768px) {
    font-size: 15px;
  }
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled)
  .ant-select-item-option-state {
  color: @colorText;
}

.batches-select-dropdown,
.project-evaluator-projects-select-dropdown {
  .ant-select-item-option-disabled {
    background-color: @colorPrimary;
    font-weight: 400;
  }
}

// Alert
// ---

.ant-alert-with-description .ant-alert-icon {
  top: 14px;
  left: 14px;
}

.ant-alert-error {
  border: 1px solid @colorErrorHover;
}
.ant-alert-description {
  font-family: 'HelveticaNowMicro-Regular', sans-serif;
  font-size: 11px;
  line-height: 14px;
  color: @colorTextSecondary;
  text-align: left;
}
.ant-alert-with-description {
  padding: 5px 39px 12px 48px;
}

// Breadcrumb
// ---

.ant-breadcrumb {
  user-select: none;
  display: flex;
  flex-direction: row;
  span {
    align-items: center;
  }
  .ant-breadcrumb-link {
    cursor: pointer;
    > div {
      transition: color 0.2s ease-in-out;
    }
    &:hover > div {
      transition: color 0.2s ease-in-out;
      color: @colorTextSecondary;
    }
  }
}

.fc .fc-scrollgrid-section-liquid {
  height: 1px !important;
}

// Tag
// ---

.ant-tag:not(.t-tag) {
  padding: 2px 4px;
  border-radius: 3px;
  border: 0;
  height: fit-content;
  width: fit-content;
  background-color: @colorPrimaryBgTransparent;

  &:hover {
    opacity: 1;
  }
}

.courseTag,
.courseTag.ant-select-multiple .ant-select-selection-item {
  background-color: @colorTagNeutralBg!important;
}

// Tabs
.ant-tabs {
  .ant-tabs-tab {
    margin: 0;
  }
  .ant-tabs-nav-list {
    width: 100%;
    justify-content: space-around;
  }

  .ant-tabs-nav {
    margin: 0 0 12px 0;

    &::before {
      border-bottom: 1px solid @colorBgElevated;
      bottom: 0.5px;
    }
  }

  .ant-tabs-ink-bar {
    background: @colorText;
  }
}

// Skeleton
// ---

.ant-skeleton.ant-skeleton-active {
  .ant-skeleton-input,
  .ant-skeleton-avatar {
    background: linear-gradient(
      90deg,
      @colorBgContainer 25%,
      @colorBgElevated 37%,
      @colorBgContainer 63%
    );
    background-size: 400% 100% !important;
    animation: ant-skeleton-loading 1.4s ease infinite !important;
    min-width: unset;
  }
  .ant-skeleton-input {
    border-radius: 2px;
  }
}

@keyframes ant-skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

// Has different background
.notebook-view-skeleton .ant-skeleton.ant-skeleton-active {
  .ant-skeleton-input,
  .ant-skeleton-avatar {
    background: linear-gradient(
      90deg,
      @colorBgElevated 25%,
      @colorBgElevated 15%,
      @colorBgElevated 63%
    );
  }
}

// Password checklist
.passwordChecklist {
  margin: -11px 0 18px 0 !important;

  li {
    margin: 0;
    &:not(:last-of-type) {
      margin-bottom: 5px;
    }
    img {
      margin-right: 8px;
    }
    span {
      font-family: 'HelveticaNowText-Regular';
      font-style: normal;
      font-weight: normal;
      font-size: 13px;
      line-height: 19px;
      opacity: 1;
      padding: 0;
    }
    &.invalid span {
      color: @colorTextQuaternary;
    }
    &.valid span {
      color: @colorSuccess;
    }
  }
}

.italic {
  font-style: italic;
}

// phone number input
input.form-control.phone-input {
  font-family: 'HelveticaNowText-Regular';
  font-size: 14px;
  line-height: 21px;
  color: @colorText !important;

  &:disabled {
    background-color: @colorBgContainer!important;
    color: @colorTextQuaternary!important;
  }
}

input.form-control.phone-input::placeholder {
  font-family: 'HelveticaNowText-Regular';
  font-size: 14px;
  line-height: 21px;
  color: @colorTextQuaternary!important;
}

.ant-form-item .react-tel-input:not(.disabled) input.form-control:hover,
.ant-form-item .react-tel-input:not(.disabled) input.form-control:focus {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.ant-form-item.ant-form-item-has-error {
  input.form-control.phone-input {
    border: 1px solid @colorError !important;
    border-left: none !important;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  div.flag-dropdown {
    border: 1px solid @colorError !important;
    border-right: none !important;
  }

  .react-tel-input div.flag-dropdown {
    &.open {
      div.selected-flag {
        border-radius: 4px;
        background-color: @colorInputBg;
        border: 1px solid transparent !important;
      }
    }

    &:not(.open) div.selected-flag {
      &:hover,
      &:focus {
        border: 1px solid transparent !important;
        background-color: @colorBgDropped;
      }
    }
  }
}

.react-tel-input {
  &:not(.disabled) {
    input.form-control {
      &:hover,
      &:focus {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      &:hover:not(:focus) {
        // border: 1px solid @colorPrimaryInactive;
      }
      &:focus {
        border: 1px solid @colorPrimary;
      }
    }

    div.flag-dropdown {
      &.open {
        background-color: @colorInputBg;

        div.selected-flag {
          border-radius: 4px;
          background-color: transparent;
          border: 1px solid @colorPrimary!important;
          background-color: transparent;
        }
      }

      &:not(.open) div.selected-flag {
        &:hover:not(:focus) {
          // border: 1px solid @colorPrimaryInactive!important;
          background-color: transparent;
        }
        &:focus {
          border: 1px solid @colorPrimary!important;
          background-color: transparent;
        }
      }
    }
  }

  &.disabled {
    div.flag-dropdown {
      background-color: @colorBgContainer!important;
      color: @colorTextQuaternary!important;
      border-right: 1px solid @colorPrimaryBg;
      border-radius: 0;

      div.selected-flag {
        border-radius: 0;
        &:hover,
        &:focus {
          border-right: 1px solid @colorPrimaryBg;
          cursor: not-allowed;
          background-color: @colorBgContainer!important;
          color: @colorTextQuaternary!important;
        }
      }
    }
  }

  input.form-control {
    width: calc(100% - 80px);
    background-color: @colorInputBg;
    border: @colorInputBorder;
    height: 38px;
    padding: 0 12px;
    color: @colorText!important;
    border: 1px solid transparent;
    margin-left: 80px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  }

  div.flag-dropdown {
    background-color: @colorInputBg;
    width: 80px;
    border: none;
    border-right: 1px solid @colorInputBorder;

    div.selected-flag {
      width: 80px;
      padding: 0 12px;
      border: 1px solid transparent;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      .flag {
        position: relative;
        top: unset;
        margin-top: 0;
        display: flex;
        align-items: center;
        margin-right: 15px;
        .arrow {
          margin-top: 0;
          top: unset;
          width: 8px;
          height: 8px;
          border: 0;
          border-left: 2px solid @colorTextQuaternary;
          border-top: 2px solid @colorTextQuaternary;
          transform: rotate(-135deg);
          left: 25px;
          margin-bottom: 3px;
        }
      }
    }
  }
  .country-list.phone-input-dropdown {
    background-color: @colorBgElevated;
    width: 340px;
    max-height: 316px;
    box-shadow: none;
    .search {
      background-color: @colorBgElevated;
      padding: 8px 12px;
      &::before {
        content: url(@searchImage);
        position: absolute;
        left: 25px;
        top: 18px;
      }
      input {
        width: 100%;
        margin: 0;
        height: 37px;
        background-color: @colorBgDropped;
        border: 0;
        border-radius: 4px;
        padding-left: 38px;
        font-family: 'HelveticaNowText-Regular';
        font-size: 14px;
        line-height: 21px;
        color: @colorText!important;
        &::placeholder {
          font-family: 'HelveticaNowText-Regular';
          font-size: 14px;
          line-height: 21px;
          color: @colorTextQuaternary!important;
        }
      }
    }
    .no-entries-message {
      font-family: 'HelveticaNowText-Regular';
      font-size: 14px;
      line-height: 21px;
      color: @colorText!important;
    }
    li.country {
      background-color: transparent;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      &.highlight {
        background-color: @colorPrimaryBg;
      }
      .flag {
        margin-top: 0;
      }
      .country-name,
      .dial-code {
        font-family: 'HelveticaNowText-Regular';
        font-size: 14px;
        line-height: 22.5px;
        color: @colorText;
      }
    }
  }
}

.deadlineExtensionForm {
  // This form is displayed above a drawer, thus needs to have a higher z-index
  z-index: 1010;
}

// Animations
@-moz-keyframes spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}
@-webkit-keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
