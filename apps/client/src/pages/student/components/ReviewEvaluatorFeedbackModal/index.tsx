import { ChangeEvent, ReactElement, useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Form } from 'antd';
import Modal from 'antd/es/modal/Modal';
import { Box, Flex, Image } from 'rebass';

import exit from '@images/svg/exit.svg';
import privateIcon from '@images/svg/private.svg';

import { queryKeys as reviewQueryKeys } from '../../../../api/reviews/queryKeys';
import { useCompleteReviewMutation } from '../../../../api/reviews/useCompleteReviewMutation';
import { useGetReviewByEventIdQuery } from '../../../../api/reviews/useGetReviewByEventIdQuery';
import { useGetReviewQuery } from '../../../../api/reviews/useGetReviewQuery';
import { queryKeys as roadmapQueryKeys } from '../../../../api/roadmap/queryKeys';
import CenteredIcon from '../../../../components/CenteredIcon';
import ErrorUIComponent from '../../../../components/ErrorUIComponent';
import { InputBox, InputType } from '../../../../components/inputs/InputBox';
import Loader from '../../../../components/Loader';
import RadioGroupInput from '../../../../components/RadioGroupInput';
import { TTooltip } from '../../../../components/TTooltip';
import { BodyText2, BodyText3, Heading3, Heading5 } from '../../../../components/typography';
import { invalidateCurrentDeadline } from '../../../../hooks/useCurrentDeadline';
import { useUser } from '../../../../hooks/useUser';
import {
  ReviewEvaluatorFuturePreference,
  reviewEvaluatorFuturePreferenceOptions,
} from '../../../../types/Correction';
import {
  MixpanelClosingEvent,
  MixpanelEvent,
  MixpanelLocation,
  MixpanelProperty,
} from '../../../../types/Mixpanel';
import { Review } from '../../../../types/Review';
import errorNotification from '../../../../utils/errorNotification';
import Mixpanel from '../../../../utils/Mixpanel';
import { ReviewFeedbackEvaluator } from './ReviewFeedbackEvaluator';
import { ReviewFeedbackQuestions } from './ReviewFeedbackQuestions';

type FormValues = {
  feedback: string;
  internalFeedback?: string;
  evaluatorFutureReviewPreference: ReviewEvaluatorFuturePreference;
};

type Props = {
  isOpen: boolean;
  handleCancel: () => void;
  reviewId?: number;
  reviewEventId?: number;
};

export const ReviewEvaluatorFeedbackModal = (props: Props): ReactElement => {
  const { isOpen, handleCancel, reviewId, reviewEventId } = props;

  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { user } = useUser();
  const [form] = Form.useForm<FormValues>();
  const evaluatorFutureReviewPreferenceValue = Form.useWatch<ReviewEvaluatorFuturePreference>(
    'evaluatorFutureReviewPreference',
    form,
  );
  const [areAllQuestionsAnswered, setAreAllQuestionsAnswered] = useState<boolean>(false);
  const [isFeedbackInputEmpty, setIsFeedbackInputEmpty] = useState<boolean>(true);

  const reviewByEventIdResult = useGetReviewByEventIdQuery(reviewEventId, {
    enabled: isOpen && !!reviewEventId,
  });
  const reviewByIdResult = useGetReviewQuery(reviewId, {
    enabled: isOpen && !!reviewId,
  });
  const isLoading = reviewByIdResult.isLoading || reviewByEventIdResult.isLoading;
  const isError = reviewByIdResult.isError || reviewByEventIdResult.isError;
  const review = reviewByIdResult.data || reviewByEventIdResult.data;
  const evaluatorName = review?.evaluator.name ?? 'Evaluator';

  const submitFeedback = useCompleteReviewMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roadmapQueryKeys.getRoadmap(user.id) });
      invalidateCurrentDeadline(queryClient, user.id);
      const slug = (review as Review)?.sprintPart?.slug;
      if (slug) {
        queryClient.invalidateQueries({ queryKey: ['sprint-parts', slug] });
        queryClient.invalidateQueries({ queryKey: ['sprint-parts', slug, 'evaluation'] });
      }
      if (review) {
        navigate(`/review/${review.id}`);
        queryClient.invalidateQueries({ queryKey: reviewQueryKeys.getReview(review.id) });
      }
      handleCancel();
    },
  });

  useEffect(() => {
    if (isOpen) {
      Mixpanel.track(MixpanelEvent.PAGE_VIEW, {
        [MixpanelProperty.LOCATION]: MixpanelLocation.REVIEW_EVALUATOR_FEEDBACK_MODAL,
      });
    }
  }, [isOpen]);

  const closeModal = (closeEvent: MixpanelClosingEvent) => {
    Mixpanel.track(MixpanelEvent.CLOSE_MODAL, {
      [MixpanelProperty.LOCATION]: MixpanelLocation.REVIEW_EVALUATOR_FEEDBACK_MODAL,
      [MixpanelProperty.CLOSING_EVENT]: closeEvent,
    });
    handleCancel();
  };

  const handleOk = () => {
    form.validateFields().then((values) => {
      const { feedback, internalFeedback, evaluatorFutureReviewPreference } = values;

      if (user && review) {
        submitFeedback.mutate({
          reviewId: review.id,
          feedback,
          internalFeedback,
          evaluatorFutureReviewPreference,
        });
      } else errorNotification();
    });
  };

  const onFeedbackInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIsFeedbackInputEmpty(!e.target.value.trim().length);
  };

  const feedback = (
    <Form form={form}>
      <Flex
        sx={{
          '.ant-form-item': {
            margin: 0,
          },
          flexDirection: 'column',
          gap: '24px',
        }}
      >
        <Flex flexDirection="column">
          <InputBox
            label={
              <Flex sx={{ alignItems: 'center', marginBottom: '6px' }}>
                <BodyText2 color="colorTextTertiary">
                  Feedback message for {evaluatorName}
                </BodyText2>
              </Flex>
            }
            name="feedback"
            type={InputType.TEXT_AREA}
            testId="feedback"
            rows={4}
            placeholder="What went well? Anything to improve?"
            onChange={onFeedbackInputChange}
          />
        </Flex>
        <Flex flexDirection="column">
          <InputBox
            label={
              <Flex sx={{ alignItems: 'center', gap: '8px', marginBottom: '6px' }}>
                <BodyText2 color="colorTextTertiary">Private note for staff (optional)</BodyText2>
                <TTooltip content="Not visible to STLs or peers">
                  <CenteredIcon
                    src={privateIcon}
                    fillColor="colorTextTertiary"
                    width="16px"
                    height="16px"
                  />
                </TTooltip>
              </Flex>
            }
            name="internalFeedback"
            type={InputType.TEXT_AREA}
            testId="internalFeedback"
            rows={2}
            placeholder={`${evaluatorName} won't see this message`}
          />
        </Flex>
        <Flex sx={{ gap: '6px', flexDirection: 'column' }}>
          <div>
            <RadioGroupInput
              name="evaluatorFutureReviewPreference"
              type="secondary"
              label={
                <Flex sx={{ alignItems: 'center', gap: '8px', marginBottom: '6px' }}>
                  <BodyText2 color="colorTextTertiary">
                    Prefer to be reviewed by {evaluatorName}
                  </BodyText2>
                  <TTooltip content="Not visible to STLs or peers">
                    <CenteredIcon
                      src={privateIcon}
                      fillColor="colorTextTertiary"
                      width="16px"
                      height="16px"
                    />
                  </TTooltip>
                </Flex>
              }
              options={reviewEvaluatorFuturePreferenceOptions}
            />
          </div>
          <BodyText3
            color="colorTextTertiary"
            visibility={
              evaluatorFutureReviewPreferenceValue === ReviewEvaluatorFuturePreference.LESS_OFTEN
                ? 'visible'
                : 'hidden'
            }
          >
            The preference won’t take effect right away, but our team’s on the case.
          </BodyText3>
        </Flex>
      </Flex>
    </Form>
  );

  const modalContent = review && (
    <>
      <Flex marginBottom="12px">
        <ReviewFeedbackEvaluator evaluator={review.evaluator} />
      </Flex>
      <BodyText2 color="colorTextQuaternary" margin="0 0 30px 0">
        Your feedback will be visible to the reviewer. This also plays key role in helping us ensure
        you have a high-quality learning experience.
      </BodyText2>
      <Heading3 margin="0 0 15px 0" testId="feedback-project-name">
        {review.sprintPart.name}
      </Heading3>
      <ReviewFeedbackQuestions
        reviewId={review.id}
        setAreAllQuestionsAnswered={setAreAllQuestionsAnswered}
      />
      <Box marginBottom="32px" />
      {feedback}
    </>
  );

  let content;

  if (isLoading) {
    content = <Loader />;
  } else if (isError) {
    content = <ErrorUIComponent />;
  } else {
    content = modalContent;
  }

  return (
    <Modal
      className="reviewEvaluatorFeedbackModal"
      title={<Heading5 align="center">Evaluate the reviewer</Heading5>}
      open={isOpen}
      onOk={handleOk}
      onCancel={() => closeModal(MixpanelClosingEvent.GENERAL_CLOSE)}
      closeIcon={<Image src={exit} onClick={handleCancel} />}
      width="632px"
      centered
      data-testid="feedback--modal"
      footer={[
        <Box
          key="box"
          sx={{
            button: {
              minWidth: '80px',
              '&:not(:first-of-type)': {
                marginLeft: '10px',
              },
            },
          }}
        >
          <Button
            type="text"
            size="middle"
            key="back"
            onClick={() => closeModal(MixpanelClosingEvent.CANCEL_BUTTON)}
            data-testid="feedback-cancel--button"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            size="middle"
            key="submit"
            onClick={handleOk}
            loading={submitFeedback.isPending}
            disabled={
              !review ||
              !areAllQuestionsAnswered ||
              isFeedbackInputEmpty ||
              !evaluatorFutureReviewPreferenceValue
            }
            data-testid="feedback-submit--button"
          >
            Submit feedback
          </Button>
        </Box>,
      ]}
    >
      {content}
    </Modal>
  );
};
