import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router';
import { captureException } from '@sentry/react';

import { TButton } from '../../../components/TButton';
import { useOnboardingSetupSource } from '../../../features/onboarding/hooks/useOnboardingSetupSource';
import { useFeatureFlag } from '../../../hooks/useFeatureFlag';
import { useUser } from '../../../hooks/useUser';
import {
  acknowledgeNotification,
  getNotifications,
} from '../../../redux/actions/notifications.actions';
import { AppState } from '../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../redux/store';
import NotificationType from '../../../types/NotificationType';
import {
  isAuthorizeDiscordNotification,
  isLearningSuspendedNotification,
  isScheduleMissingNotification,
  NotificationPayload,
  PlatformNotification,
  PlatformNotificationType,
} from '../../../types/PlatformNotification';
import { UserSuspensionReason } from '../../../types/User';
import notification from '../../../utils/tNotification';

// notification.destroy() does not work consistently
// so using a hacky way to close notification from the notification itself
const closeNotification = (event: React.MouseEvent) => {
  const notificationElement = (event.target as HTMLElement).closest(
    '.ant-notification-notice',
  ) as Element;

  const closeButton = notificationElement.querySelector(
    '.ant-notification-notice-close',
  ) as HTMLAnchorElement;

  if (closeButton) {
    closeButton.click();
  } else {
    captureException(new Error('Close button not found in notification element'));
  }
};

// BE notifications types that should be shown as toast notifications (as opposed to modals)
const displayedNotificationTypes = [
  PlatformNotificationType.DEADLINE_EXTENSION_REQUEST_CLOSED,
  PlatformNotificationType.GENERIC,
  PlatformNotificationType.SCHEDULE_MISSING,
  PlatformNotificationType.AUTHORIZE_DISCORD,
];

const getDisplayedNotifications = (notifications: PlatformNotification<NotificationPayload>[]) => {
  return notifications
    .map((n): PlatformNotification<NotificationPayload> | null => {
      if (n.isAck || n.isSnoozed) return null;

      // exception where it could be either modal or toast based on payload
      if (
        isLearningSuspendedNotification(n) &&
        n.payload.reason !== UserSuspensionReason.TERMINATED
      ) {
        return {
          ...n,
          title: 'Your learning has been restricted',
          description:
            'Now you are not able to progress in your course or endorsement. If you have any questions, please reach out to support.',
        };
      }

      if (displayedNotificationTypes.includes(n.type)) return n;

      return null;
    })
    .filter((item): item is PlatformNotification<NotificationPayload> => item !== null);
};

export const useStudentPlatformNotifications = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const notifications = useSelector((state: AppState) => state.notificationsReducer.notifications);
  const location = useLocation();
  const [onboardingSetupSource] = useOnboardingSetupSource();
  const isDfeDeadlinesPageEnabled = useFeatureFlag('dfe-deadlines-page');
  const { isDfeLearner } = useUser();

  useEffect(() => {
    // Only fetch notifications after onboarding is fully completed
    // This avoids duplicate modals / notifications (e.g. Onabording Successs + UZT info modal)
    if (!onboardingSetupSource) dispatch(getNotifications({ isAck: false, isSnoozed: false }));
  }, [location.pathname, onboardingSetupSource]);

  const getCta = (n: PlatformNotification<NotificationPayload>) => {
    if (isScheduleMissingNotification(n)) {
      const { from, to } = n.payload.groups[0];
      const handleClick = (e: React.MouseEvent) => {
        closeNotification(e);
        navigate(`/settings/deadlines?missingScheduleFrom=${from}&missingScheduleTo=${to}`);
      };
      return <TButton onClick={handleClick}>Update schedule</TButton>;
    }

    if (isAuthorizeDiscordNotification(n)) {
      const handleClick = (e: React.MouseEvent) => {
        closeNotification(e);
        navigate('/settings/profile');
      };
      return <TButton onClick={handleClick}>Authorize</TButton>;
    }
    return undefined;
  };

  const getSkippedPaths = (n: PlatformNotification<NotificationPayload>) => {
    // we navigate from notifs to these paths, so don't want to show them again
    switch (n.type) {
      case PlatformNotificationType.SCHEDULE_MISSING:
        return ['/settings/deadlines'];
      case PlatformNotificationType.AUTHORIZE_DISCORD:
        return ['/settings/profile'];
      default:
        return [];
    }
  };

  const getOnClose = (n: PlatformNotification<NotificationPayload>) => {
    switch (n.type) {
      // these notifications get acknowledged on the BE when user sets schedule or links discord
      case PlatformNotificationType.SCHEDULE_MISSING:
      case PlatformNotificationType.AUTHORIZE_DISCORD:
        return undefined;
      default:
        return () => dispatch(acknowledgeNotification(n.id));
    }
  };

  useEffect(() => {
    if (notifications) {
      const displayedNotifications = getDisplayedNotifications(notifications);
      displayedNotifications.forEach((n) => {
        // Skip schedule missing notification if user is DfE learner and feature flag is disabled
        if (isScheduleMissingNotification(n) && isDfeLearner && !isDfeDeadlinesPageEnabled) return;
        if (getSkippedPaths(n).includes(location.pathname)) return;

        notification({
          key: `${n.id}`,
          type: NotificationType.INFO,
          message: n.title,
          description: n.description,
          duration: 0,
          onClose: getOnClose(n),
          cta: getCta(n),
        });
      });
    }
  }, [notifications, isDfeDeadlinesPageEnabled, isDfeLearner]);
};
