import { ReactElement, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ReactSVG } from 'react-svg';
import { Button, Form, Tag } from 'antd';
import languages from 'language-list';
import debounce from 'lodash/debounce';
import { Box, Flex } from 'rebass';

import exit from '@images/svg/exit.svg';

import BackButton from '../../../../../components/BackButton';
import { InputBox, InputType, OptionType } from '../../../../../components/inputs/InputBox';
import { InputPhoneNumber } from '../../../../../components/inputs/InputPhoneNumber';
import Loader from '../../../../../components/Loader';
import SavingIndicator from '../../../../../components/SavingIndicator';
import { BodyText2, Heading1, Heading4, Subtitle1 } from '../../../../../components/typography';
import { learningRestrictedTooltipTitle } from '../../../../../Config';
import { useUser } from '../../../../../hooks/useUser';
import {
  getCities,
  getCountries,
  resetCities,
} from '../../../../../redux/actions/countryCity.actions';
import {
  getEndorsementRoles,
  getHiringProfileStage,
  patchHiringProfileStage,
  updateHiringProfileState,
} from '../../../../../redux/actions/endorsementHiringProfile.actions';
import { AppState } from '../../../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../../../redux/store';
import { Country } from '../../../../../types/CountryCity';
import {
  EndorsementStageStateAction,
  EndorsementStageStateValue,
  EndorsementStep,
  JobFlexibilityOption,
  RelocationOption,
} from '../../../../../types/Endorsement';
import { UserState } from '../../../../../types/User';
import capitalizeFirstLetter from '../../../../../utils/capitalizeFirstLetter';
import errorNotification from '../../../../../utils/errorNotification';
import jobFlexibilityTranslator from '../../../../../utils/jobFlexibilityTranslator';
import relocationTranslator from '../../../../../utils/relocationTranslator';
import tooltipWrap from '../../../../../utils/tooltipWrap';
import EndorsementStageHeader from '../components/EndorsementStageHeader';
import FlexibilityLabel from './FlexibilityLabel';
import RelocationLabel from './RelocationLabel';

const HiringProfileForm = (): ReactElement => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    roles: roleOptions,
    areEndorsementRolesLoading,
    isPatchHiringProfileLoading,
    isHiringProfileLoading,
    isUpdateHiringProfileStateLoading,
    hiringProfile,
  } = useSelector((state: AppState) => state.endorsementHiringProfileReducer);

  const { countries, isGetCountriesLoading, cities, isGetCitiesLoading } = useSelector(
    (state: AppState) => state.countryCityReducer,
  );
  const [form] = Form.useForm();
  const { user } = useUser();

  const [languageInput, setLanguageInput] = useState<string>('');
  const [isLoadingSpinning, setIsLoadingSpinning] = useState<boolean>(false);
  const [showSavingIndicator, setShowSavingIndicator] = useState<boolean>(false);
  const [hasTriedSubmitting, setHasTriedSubmitting] = useState<boolean>(false);
  const debounceLoading = useRef(debounce(() => setIsLoadingSpinning(false), 1000));

  useEffect(() => {
    // We make loading spin 1sec longer so that users could notice it;
    if (isPatchHiringProfileLoading) {
      setIsLoadingSpinning(true);
    } else {
      debounceLoading.current();
    }
  }, [isPatchHiringProfileLoading]);

  useEffect(() => {
    if (!roleOptions) {
      dispatch(getEndorsementRoles(user));
    }
  }, [roleOptions]);

  useEffect(() => {
    if (!countries) {
      dispatch(getCountries());
      dispatch(resetCities());
    }
  }, [countries]);

  const initialValues = {
    name: user.name,
    surname: user.surname,
    email: user.email,
    phoneNumber: hiringProfile?.phoneNumber,
    languages: (hiringProfile?.languages || []).map(
      (language: string) =>
        ({
          value: capitalizeFirstLetter(language),
          label: capitalizeFirstLetter(language),
        }) as OptionType,
    ),
    role: hiringProfile?.role?.id,
    comment: hiringProfile?.role?.comment,
    jobFlexibilityOption: hiringProfile?.jobFlexibilityOption,
    relocationOption: hiringProfile?.relocationOption,
    countryId: hiringProfile?.countryId,
    cityId: hiringProfile?.cityId,
  };

  useEffect(() => {
    if (hiringProfile) {
      form.resetFields();
    }
  }, [hiringProfile]);

  useEffect(() => {
    if (hiringProfile) {
      if (hiringProfile.countryId && countries && !cities) {
        // We must fetch the initial set of cities, if a country is selected
        const countryInUse = countries.find(
          (country: Country) => country.id === hiringProfile.countryId,
        );
        if (countryInUse) dispatch(getCities(countryInUse.code));
        else errorNotification();
      }
    } else dispatch(getHiringProfileStage(user.id));
  }, [hiringProfile, countries, cities]);

  const languageOptions: OptionType[] = languages()
    .getData()
    .map((lang: { language: string }) => ({ value: lang.language, label: lang.language }))
    .sort((a: OptionType, b: OptionType) => a.label.localeCompare(b.label));

  const isFormActive = hiringProfile?.state.value === EndorsementStageStateValue.IN_PROGRESS;

  // Update only 1second after the last change
  const debouncePatch = useRef(
    debounce(() => {
      form
        .validateFields()
        .then((values) => {
          const hiringProfileData = {
            phoneNumber: values.phoneNumber,
            languages: values.languages.map((lang: OptionType) => lang.value),
            role: {
              id: values.role,
              comment: values.comment && values.comment.length > 0 ? values.comment : null,
            },
            jobFlexibilityOption: values.jobFlexibilityOption,
            relocationOption: values.relocationOption,
            countryId: values.countryId,
            cityId: values.cityId || null, // Pass null to reset the city when country is changed
          };
          setShowSavingIndicator(true);
          dispatch(patchHiringProfileStage(user, hiringProfileData));
        })
        .catch((err) => console.log(err));
    }, 1000),
  );

  const submitStage = () => {
    setHasTriedSubmitting(true);
    form.validateFields().then(() => {
      dispatch(
        updateHiringProfileState({
          user,
          action: EndorsementStageStateAction.SUBMIT,
        }),
      );
    });
  };

  let submitButton = (
    <Button
      data-testid="hiring-profile-submit--button"
      type="primary"
      onClick={submitStage}
      disabled={user.state === UserState.SUSPENDED || isLoadingSpinning}
      loading={isUpdateHiringProfileStateLoading}
    >
      Submit my profile
    </Button>
  );

  if (user.state === UserState.SUSPENDED) {
    submitButton = tooltipWrap(submitButton, learningRestrictedTooltipTitle);
  }

  return (
    <Flex
      data-testid="hiring-profile-form--component"
      className="endorsementPage"
      sx={{
        padding: '32px 0 120px 0',
        width: '696px',
        flexDirection: 'column',
        margin: '0 auto',
        maxWidth: 'calc(100% - 30px)',
        '.ant-form-item': {
          marginBottom: '24px',
        },
        'a.link': {
          color: 'colorPrimaryText',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            color: 'colorLight',
          },
        },
        '.halfWidth': {
          maxWidth: ['100%', 'calc((100% - 16px) / 2)', 'calc((100% - 16px) / 2)'],
          width: '100%',
        },
      }}
    >
      <Flex sx={{ justifyContent: 'space-between' }}>
        <BackButton />
        {isFormActive && showSavingIndicator && (
          <SavingIndicator isLoadingSpinning={isLoadingSpinning} />
        )}
      </Flex>
      {isHiringProfileLoading ? (
        <Flex
          sx={{
            alignItems: 'center',
            justifyContent: 'center',
            maxHeight: '100%',
            height: '450px',
          }}
        >
          <Loader />
        </Flex>
      ) : (
        <Form form={form} onValuesChange={debouncePatch.current} initialValues={initialValues}>
          <Heading1 margin="48px 0 16px 0">1. Hiring profile</Heading1>
          <EndorsementStageHeader
            step={EndorsementStep.HIRING_PROFILE}
            stageState={hiringProfile?.state}
          />
          <Heading4 margin="0 0 12px 0">Key details</Heading4>
          <Flex
            sx={{
              width: '100%',
              justifyContent: 'space-between',
              flexDirection: ['column', 'row', 'row'],
            }}
          >
            <Box className="halfWidth">
              <InputBox
                testId="hiring-profile-first-name"
                name="name"
                type={InputType.TEXT}
                placeholder="First name"
                label="First name"
                disabled
              />
            </Box>
            <Box className="halfWidth">
              <InputBox
                testId="hiring-profile-last-name--input"
                name="surname"
                type={InputType.TEXT}
                placeholder="Last name"
                label="Last name"
                disabled
              />
            </Box>
          </Flex>
          <Flex
            sx={{
              width: '100%',
              justifyContent: 'space-between',
              flexDirection: ['column', 'row', 'row'],
            }}
          >
            <Box className="halfWidth">
              <InputBox
                testId="hiring-profile-email--input"
                name="email"
                type={InputType.TEXT}
                placeholder="Email"
                label="Email"
                disabled
              />
            </Box>
            <Box className="halfWidth">
              <InputPhoneNumber
                testId="hiring-profile"
                name="phoneNumber"
                disabled={!isFormActive}
              />
            </Box>
          </Flex>
          <InputBox
            testId="hiring-profile-languages--select"
            name="languages"
            type={InputType.SELECT_MULTIPLE}
            placeholder="Select languages"
            label="What language(s) do you speak?"
            rules={[
              { required: hasTriedSubmitting, message: 'Please enter at least one language' },
            ]}
            tagRender={(tagProps) => {
              const { label, onClose } = tagProps;
              return (
                <Tag
                  className="ant-select-selection-item"
                  data-testid="language-tag--component"
                  onMouseDown={(event) => {
                    // We're handling closing manually, thus need to prevent the default behavior
                    event.preventDefault();
                    event.stopPropagation();
                  }}
                  style={{ paddingRight: '8px' }}
                >
                  <Flex
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      div: {
                        display: 'flex',
                        alignItems: 'center',
                      },
                      svg: {
                        cursor: 'pointer',
                        marginLeft: '8px',
                        height: '12px',
                        width: '12px',
                        '&:hover': {
                          path: {
                            stroke: 'colorLight',
                          },
                        },
                        path: {
                          transition: 'all 0.2s ease-in-out',
                          stroke: 'colorTextQuaternary',
                        },
                      },
                    }}
                  >
                    <Subtitle1 testId="language-tag-label">{label}</Subtitle1>
                    {isFormActive && <ReactSVG src={exit} onClick={() => onClose()} />}
                  </Flex>
                </Tag>
              );
            }}
            inputValue={languageInput}
            searchValue={languageInput}
            onInputKeyDown={(val: string) => {
              if (val.trim().length > 0) setLanguageInput(val);
              else setLanguageInput('');
            }}
            onSelect={() => setLanguageInput('')}
            options={languageOptions}
            dropdownRender={(menu: ReactElement) => <Box>{menu}</Box>}
            disabled={!isFormActive}
            loading={areEndorsementRolesLoading}
          />
          <Subtitle1 color="colorTextTertiary" margin="-12px 0 48px 0">
            We recommend including languages from B1 (lower intermediate) to C2 (native) level.
          </Subtitle1>
          <Heading4 margin="0 0 12px 0">Job preferences</Heading4>
          <BodyText2 margin="0 0 16px 0">
            Focus your efforts on a narrowed-down job role and help companies get a faster idea of
            you as a ‘perfect match’.
          </BodyText2>
          {/* Todo: Options should be dynamic per course w. PLAT-2767 */}
          <Box
            sx={{
              '.ant-form-item': {
                marginBottom: '8px',
              },
              marginBottom: '24px',
            }}
          >
            <InputBox
              testId="main-area--select"
              name="role"
              type={InputType.SELECT}
              placeholder="Main area"
              label="Main area"
              options={roleOptions
                ?.map((role) => ({ value: role.id, label: role.name }))
                .sort((a: OptionType, b: OptionType) => a.label.localeCompare(b.label))}
              disabled={!isFormActive}
              rules={[{ required: hasTriedSubmitting, message: `Please select at least one role` }]}
            />
            <InputBox
              testId="main-area-comment--input"
              name="comment"
              type={InputType.TEXT_AREA}
              rows={3}
              placeholder="(Optional) If you would like to specify role, write a comment here..."
              disabled={!isFormActive}
              loading={areEndorsementRolesLoading}
            />
          </Box>
          <InputBox
            testId="flexibility--select"
            name="jobFlexibilityOption"
            className="halfWidth"
            type={InputType.SELECT}
            placeholder="Select"
            label={<FlexibilityLabel />}
            options={[
              {
                label: jobFlexibilityTranslator(JobFlexibilityOption.REMOTE),
                value: JobFlexibilityOption.REMOTE,
              },
              {
                label: jobFlexibilityTranslator(JobFlexibilityOption.ONSITE),
                value: JobFlexibilityOption.ONSITE,
              },
              {
                label: jobFlexibilityTranslator(JobFlexibilityOption.HYBRID),
                value: JobFlexibilityOption.HYBRID,
              },
              {
                label: jobFlexibilityTranslator(JobFlexibilityOption.ANY),
                tag: (
                  <Box
                    sx={{
                      '.ant-tag': {
                        backgroundColor: 'colorAccentBg',
                        color: 'colorAccent',
                        marginLeft: '4px',
                      },
                    }}
                  >
                    <Tag>Recommended</Tag>
                  </Box>
                ),
                value: JobFlexibilityOption.ANY,
              },
            ]}
            disabled={!isFormActive}
            rules={[{ required: hasTriedSubmitting, message: `Please choose a flexibility level` }]}
          />
          <InputBox
            testId="relocation--select"
            name="relocationOption"
            className="halfWidth"
            type={InputType.SELECT}
            placeholder="Select"
            label={<RelocationLabel />}
            options={[
              {
                label: relocationTranslator(RelocationOption.YES),
                value: RelocationOption.YES,
              },
              {
                label: relocationTranslator(RelocationOption.MAYBE),
                value: RelocationOption.MAYBE,
              },
              {
                label: relocationTranslator(RelocationOption.NO),
                value: RelocationOption.NO,
              },
            ]}
            disabled={!isFormActive}
            rules={[{ required: hasTriedSubmitting, message: `Please choose a relocation option` }]}
          />
          <Flex
            sx={{
              width: '100%',
              justifyContent: 'space-between',
              flexDirection: ['column', 'row', 'row'],
            }}
          >
            <Box className="halfWidth">
              <InputBox
                testId="country--select"
                name="countryId"
                type={InputType.SELECT}
                placeholder="Select"
                label="Country you currently live in"
                options={countries
                  ?.map((country) => ({ label: country.name, value: country.id }))
                  .sort((a: OptionType, b: OptionType) => a.label.localeCompare(b.label))}
                showSearch
                onChange={(value: number) => {
                  // Find country object so that we could get country code
                  const country = countries?.find((cntr) => cntr.id === value);
                  if (country) {
                    dispatch(getCities(country.code));
                    form.setFieldsValue({ cityId: undefined });
                  } else errorNotification();
                }}
                disabled={!isFormActive || !countries}
                optionFilterProp="label"
                loading={isGetCountriesLoading}
                rules={[{ required: hasTriedSubmitting, message: `Please select your country` }]}
              />
            </Box>
            <Box className="halfWidth">
              <InputBox
                testId="city--select"
                name="cityId"
                type={InputType.SELECT}
                placeholder="Select"
                label="City you currently live in"
                options={cities
                  ?.map((city) => ({ label: city.name, value: city.id }))
                  .sort((a: OptionType, b: OptionType) => a.label.localeCompare(b.label))}
                showSearch
                disabled={
                  !isFormActive ||
                  !(form.getFieldValue('countryId') || initialValues.countryId) ||
                  !cities
                }
                optionFilterProp="label"
                loading={isGetCitiesLoading}
                rules={[{ required: hasTriedSubmitting, message: `Please select your city` }]}
              />
            </Box>
          </Flex>
          <Subtitle1 color="colorTextTertiary" margin="-12px 0 64px 0">
            We will need this for your relocation preferences asked above.
          </Subtitle1>
          {isFormActive && submitButton}
        </Form>
      )}
    </Flex>
  );
};

export default HiringProfileForm;
