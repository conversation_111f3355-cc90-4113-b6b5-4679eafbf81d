import { ReactElement, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Flex } from 'rebass';

import { Heading1 } from '../../../components/typography';
import { minModulesCompletedForEndorsement } from '../../../Config';
import { useUser } from '../../../hooks/useUser';
import { getHiringProfileStage } from '../../../redux/actions/endorsementHiringProfile.actions';
import { getInterviewStage } from '../../../redux/actions/endorsementInterview.actions';
import { getSocialMediaStage } from '../../../redux/actions/endorsementSocialMedia.actions';
import { GET_HIRING_PROFILE_STAGE_ERROR } from '../../../redux/constants/endorsementHiringProfile.constants';
import { GET_INTERVIEW_STAGE_ERROR } from '../../../redux/constants/endorsementInterview.constants';
import { GET_SOCIAL_MEDIA_STAGE_ERROR } from '../../../redux/constants/endorsementSocialMedia.constants';
import { AppState } from '../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../redux/store';
import { RoadmapModuleSlotState } from '../../../types/RoadmapModuleSlot';
import { useRoadmap } from '../hooks/useRoadmap';
import ActiveEndorsement from './ActiveEndorsement';
import LockedEndorsement from './LockedEndorsement';
import OverviewSkeleton from './OverviewSkeleton';

const EndorsementOverview = (): ReactElement => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    hiringProfile,
    isHiringProfileLoading,
    action: lastHiringProfileAction,
  } = useSelector((state: AppState) => state.endorsementHiringProfileReducer);
  const {
    socialMedia,
    isSocialMediaLoading,
    action: lastSocialMediaAction,
  } = useSelector((state: AppState) => state.endorsementSocialMediaReducer);
  const {
    interview,
    isInterviewLoading,
    action: lastInterviewAction,
  } = useSelector((state: AppState) => state.endorsementInterviewReducer);

  const { roadmap, isRoadmapLoading, isRoadmapError } = useRoadmap();
  const { user } = useUser();

  const [isEndorsementUnlocked, setIsEndorsementUnlocked] = useState<boolean | undefined>();
  const [isLoading, setIsLoading] = useState<boolean>();

  useEffect(() => {
    if (
      lastHiringProfileAction === GET_HIRING_PROFILE_STAGE_ERROR ||
      lastSocialMediaAction === GET_SOCIAL_MEDIA_STAGE_ERROR ||
      lastInterviewAction === GET_INTERVIEW_STAGE_ERROR ||
      isRoadmapError
    ) {
      setIsLoading(false);
      // On error now we just show the locked screen to support the users with no hiring profiles. Solve w. PLAT-2832
      setIsEndorsementUnlocked(false);
    }
  }, [lastHiringProfileAction, lastSocialMediaAction, lastInterviewAction, isRoadmapError]);

  useEffect(() => {
    if (isEndorsementUnlocked) {
      if (isHiringProfileLoading || isSocialMediaLoading || isInterviewLoading) setIsLoading(true);
      if (!hiringProfile && !isHiringProfileLoading) dispatch(getHiringProfileStage(user.id));
      if (!socialMedia && !isSocialMediaLoading) dispatch(getSocialMediaStage(user.id));
      if (!interview && !isInterviewLoading) dispatch(getInterviewStage(user.id));
      if (socialMedia && hiringProfile && interview) setIsLoading(false);
    } else {
      // Endorsement is locked, we are not loading anymore
      setIsLoading(false);
    }
  }, [
    isEndorsementUnlocked,
    hiringProfile,
    isHiringProfileLoading,
    socialMedia,
    isSocialMediaLoading,
    interview,
    isInterviewLoading,
  ]);

  useEffect(() => {
    if (roadmap) {
      const numberOfCompletedModules = roadmap.slots.filter(
        (moduleSlot) => moduleSlot.state === RoadmapModuleSlotState.COMPLETED,
      ).length;
      setIsEndorsementUnlocked(numberOfCompletedModules >= minModulesCompletedForEndorsement);
    }
  }, [roadmap]);

  let content: ReactElement;

  if (isLoading || isRoadmapLoading) {
    content = <OverviewSkeleton />;
  } else if (isEndorsementUnlocked) {
    content = <ActiveEndorsement />;
  } else {
    content = <LockedEndorsement />;
  }

  return (
    <Flex
      data-testid="endorsement--component"
      className="endorsementPage"
      sx={{
        paddingTop: '70px',
        width: '696px',
        flexDirection: 'column',
        margin: '0 auto',
        maxWidth: 'calc(100% - 30px)',
      }}
    >
      <Flex
        sx={{
          '@media (max-width: 568px)': {
            flexDirection: 'column',
            alignItems: 'flex-start',
          },
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}
      >
        <Heading1>Endorsement</Heading1>
      </Flex>
      {content}
    </Flex>
  );
};

export default EndorsementOverview;
