import { Text } from 'rebass';

import { TypographyProps } from './types';

type Props = TypographyProps & {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  fontSize: string | string[];
  fontFamily: string;
};

export const BaseText = ({
  children,
  color = 'colorText',
  margin,
  padding,
  align = 'left',
  testId,
  textOverflow,
  whiteSpace,
  overflow,
  userSelect,
  maxWidth,
  fontSize,
  lineHeight,
  as,
  fontStyle,
  fontFamily,
  visibility,
}: Props) => (
  <Text
    as={as}
    data-testid={testId ? `${testId}--text` : undefined}
    fontFamily={fontFamily}
    color={color}
    margin={margin}
    padding={padding}
    textAlign={align}
    overflow={overflow}
    maxWidth={maxWidth}
    fontSize={fontSize}
    fontStyle={fontStyle}
    lineHeight={lineHeight}
    sx={{
      textOverflow,
      whiteSpace,
      userSelect,
      visibility,
    }}
  >
    {children}
  </Text>
);
