import React from 'react';

import FontWeight from '../../types/FontWeight';

type Overflow = 'visible' | 'hidden' | 'scroll' | 'auto' | 'initial' | 'inherit';
type TextAlign = 'start' | 'end' | 'left' | 'right' | 'center';
type TextOverflow = 'clip' | 'ellipsis' | 'inherit' | 'initial' | 'unset';
type WhiteSpace =
  | 'normal'
  | 'nowrap'
  | 'pre'
  | 'pre-line'
  | 'pre-wrap'
  | 'initial'
  | 'inherit'
  | 'break-spaces';
type FontStyle = 'normal' | 'italic' | 'oblique' | 'initial' | 'inherit';
type UserSelect = 'none' | 'text' | 'all' | 'auto';
type Visibility = 'visible' | 'hidden' | 'collapse';

export type TypographyProps = {
  align?: TextAlign;
  children: React.ReactNode;
  color?: string;
  margin?: string | string[];
  padding?: string | string[];
  maxWidth?: string | string[];
  overflow?: Overflow;
  testId?: string;
  textOverflow?: TextOverflow;
  weight?: FontWeight;
  whiteSpace?: WhiteSpace;
  fontStyle?: FontStyle;
  lineHeight?: 'min' | 'mid' | 'max';
  userSelect?: UserSelect;
  visibility?: Visibility;
};
