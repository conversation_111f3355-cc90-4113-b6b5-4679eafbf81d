import { ReactElement } from 'react';
import { Form, Radio } from 'antd';
import { Flex, SxStyleProp } from 'rebass';

import { BodyText3 } from './typography';

type Props = {
  options: {
    value: string | number | boolean;
    label: string;
  }[];
  label?: string | ReactElement;
  name?: string;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  marginBottom?: string | number;
  sx?: SxStyleProp;
  type?: 'primary' | 'secondary';
};

const RadioGroupInput = (props: Props): ReactElement => {
  const {
    options,
    label,
    name,
    disabled,
    onChange,
    value,
    marginBottom,
    sx,
    type = 'primary',
  } = props;

  return (
    <>
      {label && (
        <BodyText3 color="colorTextTertiary" margin="0 0 6px 0">
          {label}
        </BodyText3>
      )}
      <Flex
        sx={{
          '.ant-form-item': {
            width: '100%',
          },
          // removes strange 2px jump on input selection
          '.ant-form-item-has-success': {
            marginTop: '0px !important',
          },
          '.ant-radio-group': {
            display: 'flex',
            flexWrap: 'wrap',
            gap: '6px',
            '.ant-radio-button-wrapper': {
              minWidth: '80px',
              textAlign: 'center',
              borderRadius: '4px',
              borderStyle: 'solid',
              borderWidth: '1px',
              borderColor: type === 'primary' ? 'colorBorderSecondary' : 'colorPrimaryText20',
              color: type === 'primary' ? 'colorTextTertiary' : 'colorPrimaryText',
              backgroundColor: 'colorBgLayout',
              '::before': {
                display: 'none',
              },
              ':hover': {
                backgroundColor: type === 'primary' ? 'colorBgContainer' : 'colorPrimaryText15',
                ...(type === 'secondary' ? { borderColor: 'colorPrimaryText' } : undefined),
              },
            },
            '.ant-radio-button-wrapper-checked': {
              backgroundColor: type === 'primary' ? 'colorPrimaryBg' : 'colorPrimaryText15',
              color: type === 'primary' ? 'colorText' : 'colorPrimaryText',
              ...(type === 'secondary' ? { borderColor: 'colorPrimaryText' } : undefined),
            },
          },
          ...sx,
        }}
      >
        <Form.Item
          name={name}
          rules={[{ required: true, message: 'Please select an option' }]}
          style={{ marginBottom }}
        >
          <Radio.Group
            name={name}
            disabled={disabled}
            value={value}
            onChange={(e) => onChange?.(e.target.value)}
          >
            {options.map((option, index) => (
              <Radio.Button key={index} value={option.value}>
                {option.label}
              </Radio.Button>
            ))}
          </Radio.Group>
        </Form.Item>
      </Flex>
    </>
  );
};

export default RadioGroupInput;
