{"name": "ci-tools", "version": "1.0.0", "description": "Various Ci Tools such as changes detector for monorepo since last successful CircleCI build", "private": true, "main": "", "scripts": {"change-detector": "node ci-change-detector-cli.js", "version:get": "node -e \"console.log(JSON.stringify(require('./version-utils').getVersion(), null, 2))\"", "version:update": "node repo-version-cli.js update-version", "version:create-tag": "node repo-version-cli.js create-tag", "version:set-stable": "node repo-version-cli.js set-stable-version", "create-branch": "node create-release-branch-cli.js", "create-branch:release": "node create-release-branch-cli.js --type=release", "create-branch:patch": "node create-release-branch-cli.js --type=patch", "create-branch:hotfix": "node create-release-branch-cli.js --type=hotfix", "create-branch:release:develop": "node create-release-branch-cli.js --type=release --branch=develop", "create-branch:release:main": "node create-release-branch-cli.js --type=release --branch=main", "create-branch:patch:develop": "node create-release-branch-cli.js --type=patch --branch=develop", "create-branch:patch:main": "node create-release-branch-cli.js --type=patch --branch=main", "create-branch:hotfix:main": "node create-release-branch-cli.js --type=hotfix --branch=main"}, "keywords": ["<PERSON><PERSON>", "monorepo", "ci", "change-detection", "changes", "jira", "ticket"], "author": "<PERSON>imantas Merkys <<EMAIL>>", "license": "MIT", "dependencies": {"axios": "^1.10.0", "yargs": "^18.0.0"}, "engines": {"node": ">=22.13.0 <23.0.0", "npm": ">=10.9.2"}}