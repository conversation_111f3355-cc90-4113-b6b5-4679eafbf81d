import Transaction from '../../../core/infrastructure/Transaction';
import AssignedCorrectionQuestionPersistenceEntity from '../../../corrections/infrastructure/db/AssignedCorrectionQuestionPersistenceEntity';
import AssignedStudentCorrectionFeedbackQuestionPersistenceEntity from '../../../corrections/infrastructure/db/AssignedStudentCorrectionFeedbackQuestionPersistenceEntity';
import CorrectionPersistenceEntity from '../../../corrections/infrastructure/db/CorrectionPersistenceEntity';
import CorrectionService from '../../../corrections/services/CorrectionService';
import Id from '../../../core/domain/value-objects/Id';
import { CorrectionStatus } from '../../../corrections/domain/CorrectionStatus';
import { ReviewFeedback } from '../../../learning/submodules/evaluation/domain/ReviewFeedback';

export default class CorrectionServiceMock implements CorrectionService {
    getCorrectionMock = jest.fn();

    getCorrectionByEventMock = jest.fn();

    beginCorrectionMock = jest.fn();

    cancelCorrectionMock = jest.fn();

    cancelPendingCorrectionsMock = jest.fn();

    getCorrectionSummaryMock = jest.fn();

    getCorrectionSummaryByEventIdMock = jest.fn();

    reportNoShowMock = jest.fn();

    setHasCriticalErrorsMock = jest.fn();

    submitCorrectionMock = jest.fn();

    getCorrectionQuestionsMock = jest.fn();

    submitQuestionEvaluationMock = jest.fn();

    getCorrectionFeedbackQuestionsMock = jest.fn();

    submitFeedbackMock = jest.fn();

    submitFeedbackQuestionEvaluationMock = jest.fn();

    deleteCorrectionMock = jest.fn();

    findPartIdsOfPendingCorrectionsWithEvaluatorMock = jest.fn();

    findByUserIdAndModuleIdMock = jest.fn();

    getCorrection(correctionId: number): Promise<CorrectionPersistenceEntity> {
        return this.getCorrectionMock(correctionId);
    }

    getCorrectionByEvent(eventId: number): Promise<CorrectionPersistenceEntity> {
        return this.getCorrectionByEventMock(eventId);
    }

    beginCorrection(correctionId: number): Promise<void> {
        return this.beginCorrectionMock(correctionId);
    }

    cancelCorrection(
        correctionId: number,
        status: CorrectionStatus.EVALUATOR_CANCELED | CorrectionStatus.STUDENT_CANCELED,
        note: string,
    ): Promise<void> {
        return this.cancelCorrectionMock(correctionId, status, note);
    }

    cancelPendingCorrections(userId: number, note: string, tx?: Transaction): Promise<void> {
        return this.cancelPendingCorrectionsMock(userId, note, tx);
    }

    reportNoShow(
        correctionId: number,
        status: CorrectionStatus.EVALUATOR_NO_SHOW | CorrectionStatus.STUDENT_NO_SHOW,
        note: string,
    ): Promise<void> {
        return this.reportNoShowMock(correctionId, status, note);
    }

    setHasCriticalError(correctionId: number, hasCriticalError: boolean): Promise<void> {
        return this.setHasCriticalErrorsMock(correctionId, hasCriticalError);
    }

    submitCorrection(correctionId: number, evaluatorFeedback: string, note?: string): Promise<void> {
        return this.submitCorrectionMock(correctionId, evaluatorFeedback, note);
    }

    getCorrectionQuestions(correctionId: number): Promise<AssignedCorrectionQuestionPersistenceEntity[]> {
        return this.getCorrectionQuestionsMock(correctionId);
    }

    submitCorrectionQuestionEvaluation(correctionId: number, questionId: number, evaluation: number): Promise<void> {
        return this.submitQuestionEvaluationMock(correctionId, questionId, evaluation);
    }

    getCorrectionFeedbackQuestions(
        correctionId: number,
    ): Promise<AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[]> {
        return this.getCorrectionFeedbackQuestionsMock(correctionId);
    }

    submitFeedback(
        correctionId: number,
        feedback: ReviewFeedback,
        tx?: Transaction,
    ): Promise<CorrectionPersistenceEntity | undefined> {
        return this.submitFeedbackMock(correctionId, feedback, tx);
    }

    submitFeedbackQuestionEvaluation(correctionId: number, questionId: number, evaluation: number): Promise<void> {
        return this.submitFeedbackQuestionEvaluationMock(correctionId, questionId, evaluation);
    }

    deleteCorrection(correctionId: number): Promise<void> {
        return this.deleteCorrectionMock(correctionId);
    }

    findPartIdsOfPendingCorrectionsWithEvaluator(evaluatorId: Id): Promise<Id[]> {
        return this.findPartIdsOfPendingCorrectionsWithEvaluatorMock(evaluatorId);
    }

    findByUserIdAndModuleId(userId: Id, moduleId: Id): Promise<CorrectionPersistenceEntity[]> {
        return this.findByUserIdAndModuleIdMock(userId, moduleId);
    }
}
