import Batch from '../../../batches/domain/Batch';
import { BatchManagerToken } from '../../../batches/infrastructure/di/tokens';
import Container from 'typedi';
import { IntegrationTestEntity } from './IntegrationTestEntity';
import BatchCreateData from '../../../batches/services/dto/BatchCreateData';
import BatchName from '../../../batches/domain/BatchName';
import { RequireOnly } from '../../../utils/UtilityTypes';
import { faker } from '@faker-js/faker';

export class IntegrationTestBatch extends IntegrationTestEntity<Batch> {
    static async create(overrides: RequireOnly<BatchCreateData, 'courseId'>): Promise<IntegrationTestBatch> {
        const batch = await Container.get(BatchManagerToken).create({
            // @ts-expect-error TS(2783) FIXME: 'courseId' is specified more than once, so this us... Remove this comment to see the full error message
            courseId: overrides.courseId,
            endDate: new Date('2066-11-11'),
            name: new BatchName(faker.word.noun()),
            startDate: new Date(),
            ...overrides,
        });

        return new IntegrationTestBatch(batch);
    }
}
