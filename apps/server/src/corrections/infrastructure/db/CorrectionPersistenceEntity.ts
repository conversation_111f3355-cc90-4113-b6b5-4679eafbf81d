import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, RelationId } from 'typeorm';
import CalendarEventPersistenceEntity from '../../../calendar/infrastructure/db/CalendarEventPersistenceEntity';
import AssignedSprintPartPersistenceEntity from '../../../learning/submodules/roadmap/infrastructure/db/AssignedSprintPartPersistenceEntity';
import AbstractEntity from '../../../core/infrastructure/db/AbstractEntity';
import AssignedCorrectionQuestionPersistenceEntity from './AssignedCorrectionQuestionPersistenceEntity';
import AssignedStudentCorrectionFeedbackQuestionPersistenceEntity from './AssignedStudentCorrectionFeedbackQuestionPersistenceEntity';
import User from '../../../users/shared/infrastructure/db/User';
import { CorrectionStatus } from '../../domain/CorrectionStatus';
import { CorrectionType } from '../../domain/CorrectionType';
import { ReviewEvaluatorFuturePreference } from '../../../learning/submodules/evaluation/domain/ReviewEvaluatorFuturePreference';

@Entity({ name: 'correction' })
export default class CorrectionPersistenceEntity extends AbstractEntity {
    @Column({ type: 'timestamptz' })
    startDate: Date;

    @Column({ type: 'timestamptz' })
    endDate: Date;

    @Column({
        type: 'enum',
        enum: CorrectionStatus,
    })
    status: CorrectionStatus;

    @Column({
        type: 'enum',
        enum: CorrectionType,
    })
    type: CorrectionType;

    @Column({ nullable: true })
    evaluation?: number;

    @Column({ default: false })
    hasCriticalError: boolean;

    @Column()
    @RelationId((correction: CorrectionPersistenceEntity) => correction.user)
    userId: number;

    @Column()
    @RelationId((correction: CorrectionPersistenceEntity) => correction.evaluator)
    evaluatorId: number;

    @OneToMany(() => AssignedCorrectionQuestionPersistenceEntity, (question) => question.correction, { eager: true })
    questions: AssignedCorrectionQuestionPersistenceEntity[];

    @Column({ nullable: true })
    evaluatorFeedback?: string;

    @Column({ nullable: true })
    studentFeedback?: string;

    @OneToMany(() => AssignedStudentCorrectionFeedbackQuestionPersistenceEntity, (question) => question.correction)
    studentFeedbackQuestions: AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[];

    @Column({ nullable: true })
    @RelationId((correction: CorrectionPersistenceEntity) => correction.event)
    eventId: number;

    @Column({ nullable: true })
    cancellationNote: string;

    @Column({ type: 'timestamptz', nullable: true })
    canceledAt: Date;

    @Column({ nullable: true })
    noShowNote: string;

    @Column({ type: 'timestamptz', nullable: true })
    noShowAt: Date;

    @Column()
    @RelationId((correction: CorrectionPersistenceEntity) => correction.assignedSprintPart)
    assignedSprintPartId: number;

    @Column({ nullable: true })
    studentInternalFeedback?: string;

    @Column({ nullable: true, type: 'enum', enum: ReviewEvaluatorFuturePreference })
    studentEvaluatorFuturePreference: ReviewEvaluatorFuturePreference;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user?: never;

    @ManyToOne(() => User)
    private evaluator: never;

    @ManyToOne(() => AssignedSprintPartPersistenceEntity, {
        onDelete: 'CASCADE',
    })
    private assignedSprintPart?: never;

    @OneToOne(() => CalendarEventPersistenceEntity, { onDelete: 'SET NULL' })
    @JoinColumn()
    private event: never;

    isStudentCorrection(): boolean {
        return this.type === CorrectionType.STUDENT;
    }

    isMentorCorrection(): boolean {
        return this.type === CorrectionType.MENTOR;
    }

    isPending(): boolean {
        return this.status === CorrectionStatus.PENDING;
    }

    isInProgress(): boolean {
        return this.status === CorrectionStatus.IN_PROGRESS;
    }

    isPendingStudentFeedback(): boolean {
        return this.status === CorrectionStatus.PENDING_STUDENT_FEEDBACK;
    }

    isActive(): boolean {
        return this.isPending() || this.isInProgress() || this.isPendingStudentFeedback();
    }

    isCompleted(): boolean {
        return this.status === CorrectionStatus.SUCCESS || this.status === CorrectionStatus.FAIL;
    }

    isCanceled(): boolean {
        return this.status === CorrectionStatus.EVALUATOR_CANCELED || this.status === CorrectionStatus.STUDENT_CANCELED;
    }

    isNoShow(): boolean {
        return this.status === CorrectionStatus.EVALUATOR_NO_SHOW || this.status === CorrectionStatus.STUDENT_NO_SHOW;
    }

    isFailed(): boolean {
        return this.status === CorrectionStatus.FAIL;
    }
}
