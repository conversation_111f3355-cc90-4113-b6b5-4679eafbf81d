import Transaction from '../../core/infrastructure/Transaction';
import AssignedCorrectionQuestionPersistenceEntity from '../infrastructure/db/AssignedCorrectionQuestionPersistenceEntity';
import AssignedStudentCorrectionFeedbackQuestionPersistenceEntity from '../infrastructure/db/AssignedStudentCorrectionFeedbackQuestionPersistenceEntity';
import CorrectionEntity from '../infrastructure/db/CorrectionPersistenceEntity';
import CorrectionPersistenceEntity from '../infrastructure/db/CorrectionPersistenceEntity';
import Id from '../../core/domain/value-objects/Id';
import { CorrectionStatus } from '../domain/CorrectionStatus';
import { ReviewFeedback } from '../../learning/submodules/evaluation/domain/ReviewFeedback';

interface CorrectionService {
    getCorrection(correctionId: number): Promise<CorrectionEntity | undefined>;

    getCorrectionByEvent(eventId: number): Promise<CorrectionEntity | undefined>;

    beginCorrection(correctionId: number): Promise<void>;

    cancelCorrection(
        correctionId: number,
        status: CorrectionStatus.STUDENT_CANCELED | CorrectionStatus.EVALUATOR_CANCELED,
        note: string,
    ): Promise<void>;

    cancelPendingCorrections(userId: number, note: string, tx?: Transaction): Promise<void>;

    reportNoShow(
        correctionId: number,
        status: CorrectionStatus.STUDENT_NO_SHOW | CorrectionStatus.EVALUATOR_NO_SHOW,
        note: string,
    ): Promise<void>;

    setHasCriticalError(correctionId: number, hasCriticalError: boolean): Promise<void>;

    submitCorrection(correctionId: number, evaluatorFeedback: string, note?: string): Promise<void>;

    getCorrectionQuestions(correctionId: number): Promise<AssignedCorrectionQuestionPersistenceEntity[]>;

    submitCorrectionQuestionEvaluation(correctionId: number, questionId: number, evaluation: number): Promise<void>;

    getCorrectionFeedbackQuestions(
        correctionId: number,
    ): Promise<AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[]>;

    submitFeedback(
        correctionId: number,
        feedback: ReviewFeedback,
        tx?: Transaction,
    ): Promise<CorrectionEntity | undefined>;

    submitFeedbackQuestionEvaluation(correctionId: number, questionId: number, evaluation: number): Promise<void>;

    deleteCorrection(correctionId: number): Promise<void>;

    findPartIdsOfPendingCorrectionsWithEvaluator(evaluatorId: Id): Promise<Id[]>;

    findByUserIdAndModuleId(userId: Id, moduleId: Id): Promise<CorrectionPersistenceEntity[]>;
}

export default CorrectionService;
