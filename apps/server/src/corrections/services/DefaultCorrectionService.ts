import moment from 'moment-timezone';
import Analytics from '../../analytics/Analytics';
import CalendarEventScheduler from '../../calendar/services/CalendarEventScheduler';
import { Configuration } from '../../config/Configuration';
import Id from '../../core/domain/value-objects/Id';
import Transaction from '../../core/infrastructure/Transaction';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import StateValidation from '../../core/utils/validation/StateValidation';
import reviewCanceledDiscordMessage from '../../discord/messagesTemplates/reviewCanceledDiscordMessage';
import DiscordMessenger from '../../discord/services/messaging/DiscordMessenger';
import ReviewProperties from '../../education/sprints/domain/ReviewProperties';
import AssignedSprintPart from '../../learning/submodules/roadmap/domain/AssignedSprintPart';
import RoadmapAccessor from '../../learning/submodules/roadmap/services/RoadmapAccessor';
import InternalStudentNote from '../../notes/domain/InternalStudentNote';
import NoteContent from '../../notes/domain/NoteContent';
import Notebook from '../../notes/services/Notebook';
import AssignedCorrectionQuestionRepository from '../infrastructure/db/AssignedCorrectionQuestionRepository';
import AssignedStudentCorrectionFeedbackQuestionRepository from '../infrastructure/db/AssignedStudentCorrectionFeedbackQuestionRepository';
import CorrectionRepository from '../infrastructure/db/CorrectionRepository';
import AssignedCorrectionQuestionPersistenceEntity from '../infrastructure/db/AssignedCorrectionQuestionPersistenceEntity';
import AssignedStudentCorrectionFeedbackQuestionPersistenceEntity from '../infrastructure/db/AssignedStudentCorrectionFeedbackQuestionPersistenceEntity';
import CorrectionPersistenceEntity from '../infrastructure/db/CorrectionPersistenceEntity';
import { Role } from '../../users/shared/infrastructure/db/User';
import UserAccount from '../../users/accounts/domain/UserAccount';
import CorrectionService from './CorrectionService';
import Mailer from '../../mailer/Mailer';
import IllegalOperationError from '../../core/errors/IllegalOperationError';
import NotFoundError from '../../core/errors/NotFoundError';
import { MailType } from '../../mailer/types/TemplateParams';
import SubmissionManager from '../../learning/submodules/roadmap/services/submission/SubmissionManager';
import IllegalStateError from '../../core/errors/IllegalStateError';
import { CorrectionStatus } from '../domain/CorrectionStatus';
import { CostTrackingAutoTrackingService } from '../../cost-tracking/submodules/cost-tracking-auto-tracking/services/CostTrackingAutoTrackingService';
import { Duration } from '../../core/domain/value-objects/Duration';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';
import { CorrectionPointsTeller } from '../../users/students/profile/services/CorrectionPointsTeller';
import { TimezoneService } from '../../timezone/services/TimezoneService';
import { REVIEW_DATE_TIME_FORMAT, REVIEW_NO_SHOW_DATE_TIME_FORMAT } from '../../core/constants/DateFormats';
import { ReviewOutcomeDecider } from '../../learning/submodules/evaluation/domain/ReviewOutcomeDecider';
import { QueueService } from '../../queue/QueueService';
import { CompletedReviewCommand } from '../controllers/commands/dto/CompletedReviewCommand';
import { ReviewFeedback } from '../../learning/submodules/evaluation/domain/ReviewFeedback';

interface ReviewCancelledNotificationParams {
    correction: CorrectionPersistenceEntity;
    assignedPart: AssignedSprintPart;
    receiverUser: UserAccount;
    cancellerUser: UserAccount;
    cancellationNote: string;
}

class DefaultCorrectionService implements CorrectionService {
    private readonly supportLink: string;

    constructor(
        configuration: Configuration,
        private readonly transactionManager: TransactionManager,
        private readonly userAccountFinder: UserAccountFinder,
        private readonly roadmapAccessor: RoadmapAccessor,
        private readonly correctionRepository: CorrectionRepository,
        private readonly assignedCorrectionQuestionRepository: AssignedCorrectionQuestionRepository,
        private readonly studentFeedbackQuestionRepository: AssignedStudentCorrectionFeedbackQuestionRepository,
        private readonly correctionPointsTeller: CorrectionPointsTeller,
        private readonly submissionManager: SubmissionManager,
        private readonly eventScheduler: CalendarEventScheduler,
        private readonly notebook: Notebook<InternalStudentNote>,
        private readonly mailer: Mailer,
        private readonly analytics: Analytics,
        private readonly discordMessenger: DiscordMessenger,
        private readonly costTrackingAutoTrackingService: CostTrackingAutoTrackingService,
        private readonly timezoneService: TimezoneService,
        private readonly queueService: QueueService,
    ) {
        this.supportLink = configuration.support.email;
    }

    async getCorrection(correctionId: number): Promise<CorrectionPersistenceEntity> {
        // @ts-expect-error TS(2322) FIXME: Type 'CorrectionPersistenceEntity | undefined' is ... Remove this comment to see the full error message
        return this.correctionRepository.findById(correctionId, {
            relations: ['questions', 'studentFeedbackQuestions'],
        });
    }

    async getCorrectionByEvent(eventId: number): Promise<CorrectionPersistenceEntity> {
        // @ts-expect-error TS(2322) FIXME: Type 'CorrectionPersistenceEntity | undefined' is ... Remove this comment to see the full error message
        return this.correctionRepository.findByEvent(eventId, { relations: ['questions', 'studentFeedbackQuestions'] });
    }

    async beginCorrection(id: number): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            const correction = await this.correctionRepository.findById(id, {
                entityManager: transaction.entityManager,
            });
            if (!correction) {
                throw new NotFoundError('Correction was not found');
            }

            if (correction.isInProgress()) {
                return;
            }

            if (!correction.isPending()) {
                throw new IllegalStateError('Correction is not pending');
            }

            if (moment().isBefore(correction.startDate)) {
                throw new IllegalStateError('Cannot start correction too early');
            }

            await this.correctionRepository.update(
                id,
                { status: CorrectionStatus.IN_PROGRESS },
                { entityManager: transaction.entityManager },
            );
        });
    }

    async cancelCorrection(
        id: number,
        status: CorrectionStatus.STUDENT_CANCELED | CorrectionStatus.EVALUATOR_CANCELED,
        cancellationNote: string,
    ): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            const correction = await this.correctionRepository.findById(id, {
                entityManager: transaction.entityManager,
            });
            if (!correction) {
                throw new NotFoundError('Correction was not found');
            }

            if (!correction.isPending()) {
                throw new IllegalStateError('Cannot cancel a not pending correction');
            }

            if (moment().isAfter(correction.startDate)) {
                throw new IllegalStateError('Cannot cancel a correction after its start time');
            }

            await this.cancel(status, correction, cancellationNote, transaction);
        });
    }

    async cancelPendingCorrections(userId: number, note: string, tx?: Transaction): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            const now = moment();
            const evaluatorCorrections = await this.correctionRepository.findByEvaluator(
                userId,
                CorrectionStatus.PENDING,
                { entityManager: transaction.entityManager },
            );
            await Promise.all(
                evaluatorCorrections
                    .filter((c) => now.isBefore(c.startDate))
                    .map((c) => this.cancel(CorrectionStatus.EVALUATOR_CANCELED, c, note, transaction)),
            );

            const studentCorrections = await this.correctionRepository.findByStudent(userId, CorrectionStatus.PENDING, {
                entityManager: transaction.entityManager,
            });
            await Promise.all(
                studentCorrections
                    .filter((correction) => now.isBefore(correction.startDate))
                    .map((correction) => this.cancel(CorrectionStatus.STUDENT_CANCELED, correction, note, transaction)),
            );
        }, tx);
    }

    async reportNoShow(
        id: number,
        status: CorrectionStatus.STUDENT_NO_SHOW | CorrectionStatus.EVALUATOR_NO_SHOW,
        noShowNote: string,
    ): Promise<void> {
        await this.transactionManager.execute(async (tx) => {
            const correction = await this.correctionRepository.findById(id, { entityManager: tx.entityManager });
            if (!correction) {
                throw new NotFoundError('Correction was not found');
            }

            if (!correction.isPending() && !correction.isInProgress()) {
                throw new IllegalStateError('Cannot report a no-show for a correction in this state');
            }

            if (moment().isBefore(correction.startDate)) {
                throw new IllegalStateError('Cannot report a no-show for a correction before its start time');
            }

            const assignedPart = await this.roadmapAccessor.getPart(new Id(correction.assignedSprintPartId), tx);
            StateValidation.isValid(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                assignedPart.evaluationProperties.isReview(),
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                `Unexpected assigned sprint part ${assignedPart.id.value} evaluation type ${assignedPart.evaluationProperties.type}`,
            );

            const isStudentReporting = status === CorrectionStatus.EVALUATOR_NO_SHOW;
            await this.correctionRepository.update(
                id,
                {
                    status,
                    noShowNote,
                    noShowAt: new Date(),
                },
                { entityManager: tx.entityManager },
            );

            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            const evaluationProperties = assignedPart.evaluationProperties as ReviewProperties;
            if (isStudentReporting) {
                await this.creditStudent(correction.userId, evaluationProperties.cost, tx);
            } else {
                await this.creditEvaluator(correction.evaluatorId, evaluationProperties.reward, tx);
            }

            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            await this.revokeSprintContentAccessByPart(assignedPart.id, new Id(correction.evaluatorId), tx);
            const reportedUser = await this.userAccountFinder.findByUserId(
                new Id(isStudentReporting ? correction.evaluatorId : correction.userId),
            );

            const student = await this.userAccountFinder.findByUserId(new Id(correction.userId));

            await this.costTrackingAutoTrackingService.trackReviewCompleted({
                reviewerId: new Id(correction.evaluatorId),
                reviewId: new Id(id),
                reviewStartDate: correction.startDate,
                reviewEndDate: correction.endDate,
                isNoShow: true,
                description: `${student?.name?.toString()} | ${student?.username}`,
                activityTime: Duration.fromMinutes(isStudentReporting ? 0 : 10),
            });

            const timezone = await this.timezoneService.findByUserIdOrDefault(reportedUser?.id);

            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            await this.mailer.send(reportedUser.email.value, {
                type:
                    // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                    reportedUser.role === Role.USER ? MailType.REVIEW_NO_SHOW_STUDENT : MailType.REVIEW_NO_SHOW_MENTOR,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                name: reportedUser.name.firstName,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                projectName: assignedPart.name.name,
                date: moment(correction.startDate).tz(timezone).format(REVIEW_NO_SHOW_DATE_TIME_FORMAT),
                supportLink: this.supportLink,
            });
            // @ts-expect-error TS(2345) FIXME: Argument of type 'AssignedSprintPart | undefined' ... Remove this comment to see the full error message
            this.trackNoShowCorrection(correction, assignedPart);
        });
    }

    async setHasCriticalError(correctionId: number, hasCriticalError: boolean): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            const correction = await this.correctionRepository.findById(correctionId, {
                entityManager: transaction.entityManager,
            });
            if (!correction) {
                throw new NotFoundError('Correction was not found');
            }

            if (!correction.isInProgress()) {
                throw new IllegalStateError('Cannot set critical error for an inactive correction');
            }

            const updatedCorrection = await this.correctionRepository.update(
                correctionId,
                { hasCriticalError },
                { entityManager: transaction.entityManager },
            );
            const part = await this.roadmapAccessor.getPart(new Id(correction.assignedSprintPartId), transaction);
            const evaluator = await this.userAccountFinder.findByUserId(new Id(correction.evaluatorId));
            // @ts-expect-error TS(2345) FIXME: Argument of type 'CorrectionPersistenceEntity | un... Remove this comment to see the full error message
            this.trackCriticalErrorFlag(updatedCorrection, part, evaluator);
        });
    }

    async getCorrectionQuestions(correctionId: number): Promise<AssignedCorrectionQuestionPersistenceEntity[]> {
        return await this.assignedCorrectionQuestionRepository.findByCorrection(correctionId);
    }

    async submitCorrectionQuestionEvaluation(
        correctionId: number,
        questionId: number,
        evaluation: number,
    ): Promise<void> {
        const correction = await this.correctionRepository.findById(correctionId);
        if (!correction?.isInProgress()) {
            throw new IllegalOperationError('Correction is not active');
        }

        const question = await this.assignedCorrectionQuestionRepository.findById(questionId);
        if (!question || question.correctionId !== correctionId) {
            throw new NotFoundError('Question was not found');
        }

        await this.assignedCorrectionQuestionRepository.update(questionId, { evaluation });
    }

    async submitCorrection(correctionId: number, evaluatorFeedback: string, note?: string): Promise<void> {
        await this.transactionManager.execute(async (tx) => {
            const correction = await this.correctionRepository.findById(correctionId, {
                entityManager: tx.entityManager,
                relations: ['questions'],
            });
            if (!correction?.isInProgress()) {
                throw new IllegalOperationError('Correction is not active');
            }
            if (!correction.hasCriticalError && correction.questions.some(({ evaluation }) => !evaluation)) {
                throw new IllegalOperationError('Correction is incomplete');
            }

            const assignedPart = await this.roadmapAccessor.getPart(new Id(correction.assignedSprintPartId), tx);
            StateValidation.isValid(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                assignedPart.evaluationProperties.isReview(),
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                `Unexpected assigned sprint part ${assignedPart.id.value} evaluation type ${assignedPart.evaluationProperties.type}`,
            );

            const evaluation = this.calculateCorrectionEvaluation(correction);
            const submittedCorrection = await this.correctionRepository.update(
                correction.id,
                {
                    evaluation,
                    evaluatorFeedback,
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: new Date(),
                },
                { entityManager: tx.entityManager },
            );
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            const evaluationProperties = assignedPart.evaluationProperties as ReviewProperties;
            await this.creditEvaluator(correction.evaluatorId, evaluationProperties.reward, tx);
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            await this.revokeSprintContentAccessByPart(assignedPart.id, new Id(correction.evaluatorId), tx);

            if (note) {
                await this.notebook.create(
                    new InternalStudentNote(
                        new Id(correction.evaluatorId),
                        new NoteContent(note),
                        new Id(correction.userId),
                    ),
                    tx,
                );
            }

            const student = await this.userAccountFinder.findByUserId(new Id(correction.userId));
            const evaluator = await this.userAccountFinder.findByUserId(new Id(correction.evaluatorId));

            await this.costTrackingAutoTrackingService.trackReviewCompleted({
                reviewerId: new Id(correction.evaluatorId),
                reviewId: new Id(correctionId),
                reviewStartDate: correction.startDate,
                reviewEndDate: correction.endDate,
                isNoShow: false,
                description: `${student?.name?.toString()} | ${student?.username}`,
            });

            // @ts-expect-error TS(2345) FIXME: Argument of type 'CorrectionPersistenceEntity | un... Remove this comment to see the full error message
            this.trackSubmittedCorrection(submittedCorrection, assignedPart, student, evaluator);
        });
    }

    async getCorrectionFeedbackQuestions(
        correctionId: number,
    ): Promise<AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[]> {
        return await this.studentFeedbackQuestionRepository.findByCorrection(correctionId);
    }

    async submitFeedback(
        correctionId: number,
        feedback: ReviewFeedback,
        existingTx?: Transaction,
    ): Promise<CorrectionPersistenceEntity | undefined> {
        return await this.transactionManager.execute(async (tx) => {
            const correction = await this.correctionRepository.findById(correctionId, {
                entityManager: tx.entityManager,
                relations: ['studentFeedbackQuestions'],
            });
            ArgumentValidation.assert.defined(correction, 'Correction not found');
            ArgumentValidation.assert.true(correction.isPendingStudentFeedback(), 'Correction is in a wrong state');
            ArgumentValidation.assert.true(
                correction.studentFeedbackQuestions.every((c) => !!c.evaluation),
                'Not all feedback questions have been answered',
            );

            const part = await this.roadmapAccessor.getPart(new Id(correction.assignedSprintPartId), tx);

            ArgumentValidation.assert.defined(part, 'Assigned part not found');

            if (part.evaluationProperties.isReview()) {
                const reviewStatus = ReviewOutcomeDecider.isPassed({
                    reviewsPassingScore: part.evaluationProperties.reviewsPassingScore,
                    review: correction,
                })
                    ? CorrectionStatus.SUCCESS
                    : CorrectionStatus.FAIL;
                const completedCorrection = await this.correctionRepository.update(
                    correctionId,
                    {
                        studentFeedback: feedback.feedback,
                        studentInternalFeedback: feedback.internalFeedback,
                        studentEvaluatorFuturePreference: feedback.evaluatorFutureReviewPreference,
                        status: reviewStatus,
                    },
                    { entityManager: tx.entityManager },
                );

                const evaluator = await this.userAccountFinder.findByUserId(new Id(correction.evaluatorId));

                ArgumentValidation.assert.defined(evaluator, 'Evaluator not found');

                tx.addBeforeCommitAction(async () => {
                    this.analytics.studentCorrectionFeedbackSubmitted(correction.userId, {
                        sprintPartId: part.sprintPartId.value,
                        sprintPartAbbreviation: part.abbreviation.value,
                        evaluatorUsername: evaluator.username?.value ?? 'Unknown',
                    });
                });

                await this.queueService.command({
                    command: new CompletedReviewCommand({
                        studentId: new Id(correction.userId),
                        reviewId: new Id(correction.id),
                        reviewStatus,
                    }),
                });

                return completedCorrection;
            } else {
                ArgumentValidation.assert.fail('Assigned part evaluation is not review');
            }
        }, existingTx);
    }

    async submitFeedbackQuestionEvaluation(
        correctionId: number,
        questionId: number,
        evaluation: number,
    ): Promise<void> {
        await this.transactionManager.execute(async (tx) => {
            const correction = await this.correctionRepository.findById(correctionId, {
                entityManager: tx.entityManager,
                relations: ['studentFeedbackQuestions'],
            });
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            StateValidation.isValid(correction.isPendingStudentFeedback(), 'Correction is in a wrong state');
            ArgumentValidation.assert.true(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                correction.studentFeedbackQuestions.some((q) => q.id === questionId),
                'Correction feedback question was not found',
            );

            await this.studentFeedbackQuestionRepository.update(
                questionId,
                { evaluation },
                { entityManager: tx.entityManager },
            );
        });
    }

    async deleteCorrection(correctionId: number): Promise<void> {
        await this.correctionRepository.delete(correctionId);
    }

    findPartIdsOfPendingCorrectionsWithEvaluator(evaluatorId: Id): Promise<Id[]> {
        return this.correctionRepository.findPartIdsOfPendingCorrectionsWithEvaluator(evaluatorId);
    }

    async findByUserIdAndModuleId(userId: Id, moduleId: Id): Promise<CorrectionPersistenceEntity[]> {
        return this.correctionRepository.findByUserIdAndModuleId(userId, moduleId);
    }

    private async cancel(
        status: CorrectionStatus.EVALUATOR_CANCELED | CorrectionStatus.STUDENT_CANCELED,
        correction: CorrectionPersistenceEntity,
        cancellationNote: string,
        tx: Transaction,
    ): Promise<void> {
        const assignedPart = await this.roadmapAccessor.getPart(new Id(correction.assignedSprintPartId), tx);
        StateValidation.isValid(
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            assignedPart.evaluationProperties.isReview(),
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            `Unexpected assigned sprint part ${assignedPart.id.value} evaluation type ${assignedPart.evaluationProperties.type}`,
        );

        const isEvaluatorCancel = status === CorrectionStatus.EVALUATOR_CANCELED;
        await this.correctionRepository.update(
            correction.id,
            {
                status,
                cancellationNote,
                canceledAt: new Date(),
            },
            { entityManager: tx.entityManager },
        );

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const evaluationProperties = assignedPart.evaluationProperties as ReviewProperties;
        if (isEvaluatorCancel) {
            await this.creditStudent(correction.userId, evaluationProperties.cost, tx);
        } else {
            await this.creditEvaluator(correction.evaluatorId, evaluationProperties.reward, tx);
        }

        await this.eventScheduler.cancel(new Id(correction.eventId), tx);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        await this.revokeSprintContentAccessByPart(assignedPart.id, new Id(correction.evaluatorId), tx);

        const canceller = await this.userAccountFinder.findByUserId(
            new Id(new Id(isEvaluatorCancel ? correction.evaluatorId : correction.userId)),
        );
        const receiver = await this.userAccountFinder.findByUserId(
            new Id(isEvaluatorCancel ? correction.userId : correction.evaluatorId),
        );

        await this.notifySecondParticipantAboutReviewCancellation({
            correction,
            // @ts-expect-error TS(2322) FIXME: Type 'AssignedSprintPart | undefined' is not assig... Remove this comment to see the full error message
            assignedPart,
            cancellationNote,
            // @ts-expect-error TS(2322) FIXME: Type 'UserAccount | undefined' is not assignable t... Remove this comment to see the full error message
            cancellerUser: canceller,
            // @ts-expect-error TS(2322) FIXME: Type 'UserAccount | undefined' is not assignable t... Remove this comment to see the full error message
            receiverUser: receiver,
        });
        // @ts-expect-error TS(2345) FIXME: Argument of type 'AssignedSprintPart | undefined' ... Remove this comment to see the full error message
        this.trackCanceledCorrection(correction, assignedPart);
    }

    private calculateCorrectionEvaluation(correction: CorrectionPersistenceEntity): number {
        if (correction.hasCriticalError) {
            return 0;
        }

        const maxScore: number = correction.questions.reduce((sum, { weight }) => sum + 5 * weight, 0);
        const evaluationSum = correction.questions.reduce(
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            (sum, { evaluation, weight }) => sum + evaluation * weight,
            0,
        );

        return maxScore > 0 ? Math.round((100 * evaluationSum) / maxScore) : 0;
    }

    private async creditStudent(userId: number, amount: number, transaction: Transaction): Promise<void> {
        await this.correctionPointsTeller.credit(new Id(userId), amount, transaction);
    }

    private async creditEvaluator(userId: number, amount: number, transaction: Transaction): Promise<void> {
        await this.correctionPointsTeller.credit(new Id(userId), amount, transaction);
    }

    private async revokeSprintContentAccessByPart(
        assignedSprintPartId: Id,
        userId: Id,
        tx: Transaction,
    ): Promise<void> {
        // TODO Now we only give access to the main part.
        // Keeping this here for now to revoke access for already scheduled corrections.
        const sprint = await this.roadmapAccessor.getSprintByAssignedPart(assignedSprintPartId, tx);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        await Promise.all(sprint.parts.map((p) => this.submissionManager.revokeAccess(p, userId, tx)));
    }

    private trackCriticalErrorFlag(
        correction: CorrectionPersistenceEntity,
        part: AssignedSprintPart,
        evaluator: UserAccount,
    ): void {
        this.analytics.correctionFlaggedAsFailed(correction.userId, {
            sprintPartId: part.sprintPartId.value,
            sprintPartAbbreviation: part.abbreviation.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            peerUsername: evaluator.username.value,
            isFlagged: correction.hasCriticalError,
            minutesRemaining: moment(correction.endDate).diff(new Date(), 'minutes'),
        });
    }

    private trackSubmittedCorrection(
        correction: CorrectionPersistenceEntity,
        part: AssignedSprintPart,
        student: UserAccount,
        evaluator: UserAccount,
    ): void {
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        this.analytics.correctionSubmitted(evaluator.id.value, {
            sprintPartId: part.sprintPartId.value,
            sprintPartName: part.name.name,
            sprintPartAbbreviation: part.abbreviation.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            peerUsername: student.username.value,
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            grade: correction.evaluation,
            time: correction.endDate,
            status: correction.status,
            isFlagged: correction.hasCriticalError,
        });
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        this.analytics.correctionReceived(student.id.value, {
            sprintPartId: part.sprintPartId.value,
            sprintPartName: part.name.name,
            sprintPartAbbreviation: part.abbreviation.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            evaluatorUsername: evaluator.username.value,
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            grade: correction.evaluation,
            time: correction.endDate,
            status: correction.status,
            isFlagged: correction.hasCriticalError,
        });
    }

    private trackCanceledCorrection(correction: CorrectionPersistenceEntity, part: AssignedSprintPart): void {
        this.analytics.correctionCanceled(correction.userId, {
            sprintPartAbbreviation: part.abbreviation.value,
            peerId: correction.evaluatorId,
            type: correction.type,
            minutesRemaining: moment(correction.endDate).diff(new Date(), 'minutes'),
        });
    }

    private trackNoShowCorrection(correction: CorrectionPersistenceEntity, part: AssignedSprintPart): void {
        this.analytics.correctionNoShow(correction.userId, {
            sprintPartAbbreviation: part.abbreviation.value,
            peerId: correction.evaluatorId,
            type: correction.type,
            minutesRemaining: moment(correction.endDate).diff(new Date(), 'minutes'),
        });
    }

    private async notifySecondParticipantAboutReviewCancellation({
        correction,
        assignedPart,
        cancellerUser,
        receiverUser,
        cancellationNote,
    }: ReviewCancelledNotificationParams): Promise<void> {
        const timezone = await this.timezoneService.findByUserIdOrDefault(receiverUser.id);
        const meetingDateUtcString = moment(correction.startDate).tz(timezone).format(REVIEW_DATE_TIME_FORMAT);
        const canceller = {
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            firstName: cancellerUser.name.firstName,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            lastName: cancellerUser.name.lastName,
            username: cancellerUser.username?.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            role: cancellerUser.id.value === correction.evaluatorId ? 'Evaluator' : 'Learner',
        };
        const receiver = {
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            firstName: receiverUser.name.firstName,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            lastName: receiverUser.name.lastName,
            username: receiverUser.username?.value,
        };

        await this.mailer.send(receiverUser.email.value, {
            type: MailType.REVIEW_CANCELED,
            time: meetingDateUtcString,
            part: assignedPart.name.name,
            // @ts-expect-error TS(2322) FIXME: Type '{ firstName: string; lastName: string; usern... Remove this comment to see the full error message
            canceller,
            // @ts-expect-error TS(2322) FIXME: Type '{ firstName: string; lastName: string; usern... Remove this comment to see the full error message
            receiver,
            cancellationNote,
        });

        try {
            await this.discordMessenger.sendDirectMessage(
                // @ts-expect-error TS(2345) FIXME: Argument of type 'Id | undefined' is not assignabl... Remove this comment to see the full error message
                receiverUser.id,
                reviewCanceledDiscordMessage({
                    time: meetingDateUtcString,
                    assignedPartName: assignedPart.name.name,
                    // @ts-expect-error TS(2322) FIXME: Type '{ firstName: string; lastName: string; usern... Remove this comment to see the full error message
                    canceller,
                    cancellationNote,
                    receiver,
                }),
            );
        } catch {
            // Ignore
        }
    }
}

export default DefaultCorrectionService;
