import { faker } from '@faker-js/faker';
import 'jest-extended';
import moment from 'moment';
import { anything, capture, deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import Id from '../../core/domain/value-objects/Id';
import reviewCanceledDiscordMessage from '../../discord/messagesTemplates/reviewCanceledDiscordMessage';
import DiscordMessenger from '../../discord/services/messaging/DiscordMessenger';
import { EvaluationType } from '../../education/sprints/domain/EvaluationProperties';
import ReviewProperties from '../../education/sprints/domain/ReviewProperties';
import AssignedSprint, { AssignedSprintState } from '../../learning/submodules/roadmap/domain/AssignedSprint';
import AssignedSprintPart, {
    AssignedSprintPartState,
} from '../../learning/submodules/roadmap/domain/AssignedSprintPart';
import InternalStudentNote from '../../notes/domain/InternalStudentNote';
import NoteContent from '../../notes/domain/NoteContent';
import AssignedCorrectionQuestionPersistenceEntity from '../infrastructure/db/AssignedCorrectionQuestionPersistenceEntity';
import AssignedStudentCorrectionFeedbackQuestionPersistenceEntity from '../infrastructure/db/AssignedStudentCorrectionFeedbackQuestionPersistenceEntity';
import { default as Correction, default as CorrectionEntity } from '../infrastructure/db/CorrectionPersistenceEntity';
import { Role } from '../../users/shared/infrastructure/db/User';
import DefaultCorrectionService from './DefaultCorrectionService';
import Mailer from '../../mailer/Mailer';
import NotFoundError from '../../core/errors/NotFoundError';
import UserAccount from '../../users/accounts/domain/UserAccount';
import AnalyticsMock from '../../test-toolkit/unit/deprecated-mocks/AnalyticsMock';
import AssignedCorrectionQuestionRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/AssignedCorrectionQuestionRepositoryMock';
import AssignedStudentCorrectionFeedbackQuestionRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/AssignedStudentCorrectionFeedbackQuestionRepositoryMock';
import CalendarEventSchedulerMock from '../../test-toolkit/unit/deprecated-mocks/CalendarEventSchedulerMock';
import CoreTransactionManagerMock from '../../test-toolkit/unit/deprecated-mocks/CoreTransactionManagerMock';
import CorrectionRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/CorrectionRepositoryMock';
import NotebookMock from '../../test-toolkit/unit/deprecated-mocks/NotebookMock';
import { TestConfigurationService } from '../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import TestObjects from '../../test-toolkit/shared/TestObjects';
import { getRandomAssignedSprint, getRandomAssignedSprintPart } from '../../test-toolkit/shared/learning';
import { MailType } from '../../mailer/types/TemplateParams';
import { UserState } from '../../users/accounts/domain/UserState';
import IllegalStateError from '../../core/errors/IllegalStateError';
import { CorrectionStatus } from '../domain/CorrectionStatus';
import { CorrectionType } from '../domain/CorrectionType';

import { CostTrackingAutoTrackingService } from '../../cost-tracking/submodules/cost-tracking-auto-tracking/services/CostTrackingAutoTrackingService';
import RoadmapAccessor from '../../learning/submodules/roadmap/services/RoadmapAccessor';
import SubmissionManager from '../../learning/submodules/roadmap/services/submission/SubmissionManager';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';
import { CorrectionPointsTeller } from '../../users/students/profile/services/CorrectionPointsTeller';
import { TimezoneService } from '../../timezone/services/TimezoneService';
import { QueueService } from '../../queue/QueueService';
import { CompletedReviewCommand } from '../controllers/commands/dto/CompletedReviewCommand';
import { ReviewEvaluatorFuturePreference } from '../../learning/submodules/evaluation/domain/ReviewEvaluatorFuturePreference';
import { ReviewFeedback } from '../../learning/submodules/evaluation/domain/ReviewFeedback';

function correctionInstance(correction: Partial<CorrectionEntity>): CorrectionEntity {
    return Object.setPrototypeOf(correction, CorrectionEntity.prototype);
}

function assertNotification(
    receiver: UserAccount,
    cancellerRole: string,
    canceller: UserAccount,
    assignedPart: AssignedSprintPart,
    time: string,
    cancellationNote: string,
    mailerMock: Mailer,
    discordMessengerMock: DiscordMessenger,
): void {
    const mailerSendArgs = capture(mailerMock.send).last();

    expect(mailerSendArgs).toMatchObject([
        receiver.email.value,
        {
            type: MailType.REVIEW_CANCELED,
            time,
            part: assignedPart.name.name,
            canceller: {
                firstName: canceller.name?.firstName,
                lastName: canceller.name?.lastName,
                username: canceller.username?.value,
                role: cancellerRole,
            },
            receiver: {
                firstName: receiver.name?.firstName,
                lastName: receiver.name?.lastName,
                username: receiver.username?.value,
            },
            cancellationNote,
        },
    ]);

    const discordSenderArgs = capture(discordMessengerMock.sendDirectMessage).last();

    expect(discordSenderArgs).toMatchObject([
        receiver.id,
        reviewCanceledDiscordMessage({
            time,
            assignedPartName: assignedPart.name.name,
            canceller: {
                firstName: canceller.name?.firstName as string,
                lastName: canceller.name?.lastName as string,
                username: canceller.username?.value as string,
                role: cancellerRole,
            },
            receiver: {
                firstName: receiver.name?.firstName as string,
                lastName: receiver.name?.lastName as string,
            },
            cancellationNote,
        }),
    ]);
}

describe('DefaultCorrectionService', () => {
    const config = TestConfigurationService.create().getConfigurationSync();
    const userAccountFinder = mock(UserAccountFinder);
    const roadmapAccessor = mock(RoadmapAccessor);
    const correctionRepository = new CorrectionRepositoryMock();
    const assignedCorrectionQuestionRepository = new AssignedCorrectionQuestionRepositoryMock();
    const assignedStudentFeedbackQuestionRepository = new AssignedStudentCorrectionFeedbackQuestionRepositoryMock();
    const correctionPointsTeller = mock(CorrectionPointsTeller);
    const submissionManager = mock(SubmissionManager);
    const eventScheduler = new CalendarEventSchedulerMock();
    const notebook = new NotebookMock<InternalStudentNote>();
    const analytics = new AnalyticsMock();
    const mailerMock = mock<Mailer>();
    const discordMessengerMock = mock<DiscordMessenger>();
    const timezoneService = mock(TimezoneService);
    const queueService = mock(QueueService);
    const correctionService = new DefaultCorrectionService(
        config,
        new CoreTransactionManagerMock(),
        instance(userAccountFinder),
        instance(roadmapAccessor),
        correctionRepository,
        assignedCorrectionQuestionRepository,
        assignedStudentFeedbackQuestionRepository,
        instance(correctionPointsTeller),
        instance(submissionManager),
        eventScheduler,
        notebook,
        instance(mailerMock),
        analytics,
        instance(discordMessengerMock),
        instance(mock<CostTrackingAutoTrackingService>()),
        instance(timezoneService),
        instance(queueService),
    );
    let student: UserAccount;
    let evaluator: UserAccount;
    let assignedSprint: AssignedSprint;
    let assignedPart: AssignedSprintPart;

    const reviewInFutureDate = moment('Mon, 18 Sep 2090 17:10:45 GMT').toDate();
    const reviewInFutureFormattedString = 'September 18th, Monday, 20:10 (GMT +03:00)';

    beforeEach(() => {
        student = new UserAccount({
            role: Role.USER,
            email: TestObjects.email(),
            state: UserState.ACTIVE,
            calendarToken: faker.word.noun(),
            createdAt: new Date(),
            username: TestObjects.username(),
            name: TestObjects.fullName(),
            id: TestObjects.uniqueId(),
            permissions: [],
        });
        evaluator = new UserAccount({
            role: Role.USER,
            email: TestObjects.email(),
            state: UserState.ACTIVE,
            calendarToken: faker.word.noun(),
            createdAt: new Date(),
            username: TestObjects.username(),
            name: TestObjects.fullName(),
            id: TestObjects.uniqueId(),
            permissions: [],
        });
        assignedPart = getRandomAssignedSprintPart(
            true,
            student.id,
            AssignedSprintPartState.SUBMITTED,
            EvaluationType.REVIEW,
        );
        assignedSprint = getRandomAssignedSprint(true, student.id, AssignedSprintState.IN_PROGRESS, new Date(), [
            assignedPart,
        ]);
        when(userAccountFinder.findByUserId(anything())).thenCall((id: Id) =>
            [student, evaluator].find((u) => id.equals(u.id)),
        );
        when(roadmapAccessor.getSprintByAssignedPart(anything(), anything())).thenCall((id: Id) =>
            assignedSprint.parts.some((p) => p.getIdOrThrow().equals(id)) ? assignedSprint : undefined,
        );
        when(roadmapAccessor.getPart(deepEqual(assignedPart.getIdOrThrow()), anything())).thenResolve(assignedPart);
        when(timezoneService.findByUserIdOrDefault(anything())).thenResolve(config.date.timezone);
        reset(mailerMock);
        reset(discordMessengerMock);
    });

    describe('beginCorrection', () => {
        it('should not start non-existent', async () => {
            await expect(correctionService.beginCorrection(999)).rejects.toEqual(
                new NotFoundError('Correction was not found'),
            );
        });

        it('should do nothing if correction is in progress', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    evaluatorId: 2,
                    status: CorrectionStatus.IN_PROGRESS,
                }),
            );

            await expect(correctionService.beginCorrection(1)).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).not.toBeCalled();
        });

        it('should not start a completed correction', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    evaluatorId: 2,
                    status: CorrectionStatus.SUCCESS,
                }),
            );

            await expect(correctionService.beginCorrection(1)).rejects.toEqual(
                new IllegalStateError('Correction is not pending'),
            );
        });

        it('should not start correction too early', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    evaluatorId: 2,
                    status: CorrectionStatus.PENDING,
                    startDate: moment().add(1, 'day').toDate(),
                }),
            );

            await expect(correctionService.beginCorrection(1)).rejects.toEqual(
                new IllegalStateError('Cannot start correction too early'),
            );
        });

        it('should start', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    evaluatorId: 2,
                    status: CorrectionStatus.PENDING,
                    startDate: new Date(),
                }),
            );

            await expect(correctionService.beginCorrection(1)).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                1,
                { status: CorrectionStatus.IN_PROGRESS },
                expect.anything(),
            );
        });
    });

    describe('cancelCorrection', () => {
        const correctionId = TestObjects.id().value;
        const eventId = TestObjects.id().value;
        const cancellationNote = faker.lorem.sentence();

        it('should not cancel non-existent correction', async () => {
            await expect(
                correctionService.cancelCorrection(999, CorrectionStatus.EVALUATOR_CANCELED, cancellationNote),
            ).rejects.toEqual(new NotFoundError('Correction was not found'));
        });

        it('should not cancel not pending correction', async () => {
            correctionRepository.findByIdMock.mockResolvedValueOnce(
                correctionInstance({
                    id: correctionId,
                    status: CorrectionStatus.IN_PROGRESS,
                    userId: student.getIdOrThrow().value,
                    evaluatorId: evaluator.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.cancelCorrection(correctionId, CorrectionStatus.EVALUATOR_CANCELED, cancellationNote),
            ).rejects.toEqual(new IllegalStateError('Cannot cancel a not pending correction'));
        });

        it('should not cancel correction too late', async () => {
            correctionRepository.findByIdMock.mockResolvedValueOnce(
                correctionInstance({
                    id: correctionId,
                    status: CorrectionStatus.PENDING,
                    startDate: moment().subtract(1, 'hour').toDate(),
                    userId: student.getIdOrThrow().value,
                    evaluatorId: evaluator.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.cancelCorrection(correctionId, CorrectionStatus.EVALUATOR_CANCELED, cancellationNote),
            ).rejects.toEqual(new IllegalStateError('Cannot cancel a correction after its start time'));
        });

        it('Cancel with an evaluator', async () => {
            const correction = correctionInstance({
                eventId,
                id: correctionId,
                status: CorrectionStatus.PENDING,
                startDate: reviewInFutureDate,
                type: CorrectionType.MENTOR,
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValueOnce(correction);

            await expect(
                correctionService.cancelCorrection(correctionId, CorrectionStatus.EVALUATOR_CANCELED, cancellationNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    cancellationNote,
                    status: CorrectionStatus.EVALUATOR_CANCELED,
                    canceledAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(new Id(student.getIdOrThrow())),
                    (assignedPart.evaluationProperties as ReviewProperties).cost,
                    anything(),
                ),
            ).once();

            expect(eventScheduler.cancelMock).toBeCalledWith(new Id(eventId), expect.anything());
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            assertNotification(
                student,
                'Evaluator',
                evaluator,
                assignedPart,
                reviewInFutureFormattedString,
                cancellationNote,
                mailerMock,
                discordMessengerMock,
            );

            expect(analytics.correctionCanceledMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });

        it('Cancel with a student for a student evaluator', async () => {
            const correction = correctionInstance({
                eventId,
                id: correctionId,
                status: CorrectionStatus.PENDING,
                type: CorrectionType.STUDENT,
                startDate: reviewInFutureDate,
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValueOnce(correction);

            await correctionService.cancelCorrection(correctionId, CorrectionStatus.STUDENT_CANCELED, cancellationNote);

            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    cancellationNote,
                    status: CorrectionStatus.STUDENT_CANCELED,
                    canceledAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(new Id(evaluator.getIdOrThrow())),
                    (assignedPart.evaluationProperties as ReviewProperties).reward,
                    anything(),
                ),
            ).once();
            expect(eventScheduler.cancelMock).toBeCalledWith(new Id(eventId), expect.anything());
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            assertNotification(
                evaluator,
                'Learner',
                student,
                assignedPart,
                reviewInFutureFormattedString,
                cancellationNote,
                mailerMock,
                discordMessengerMock,
            );

            expect(analytics.correctionCanceledMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });

        it('Cancel with a student for a mentor evaluator', async () => {
            const correction = correctionInstance({
                eventId,
                id: correctionId,
                status: CorrectionStatus.PENDING,
                startDate: reviewInFutureDate,
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);

            await expect(
                correctionService.cancelCorrection(correctionId, CorrectionStatus.STUDENT_CANCELED, cancellationNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    cancellationNote,
                    status: CorrectionStatus.STUDENT_CANCELED,
                    canceledAt: expect.any(Date),
                },
                expect.anything(),
            );
            expect(eventScheduler.cancelMock).toBeCalledWith(new Id(eventId), expect.anything());
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            assertNotification(
                evaluator,
                'Learner',
                student,
                assignedPart,
                reviewInFutureFormattedString,
                cancellationNote,
                mailerMock,
                discordMessengerMock,
            );

            expect(analytics.correctionCanceledMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });
    });

    describe('cancelPendingCorrections', () => {
        const cancellationNote = 'test cancellation';
        let correction: Correction;

        beforeEach(() => {
            correction = correctionInstance({
                id: 3,
                eventId: 4,
                status: CorrectionStatus.PENDING,
                type: CorrectionType.MENTOR,
                startDate: reviewInFutureDate,
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
        });

        it('should cancel pending evaluator corrections', async () => {
            correctionRepository.findByEvaluatorMock.mockResolvedValue([correction]);
            correctionRepository.findByStudentMock.mockResolvedValue([]);

            await expect(
                correctionService.cancelPendingCorrections(evaluator.getIdOrThrow().value, cancellationNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                {
                    cancellationNote,
                    status: CorrectionStatus.EVALUATOR_CANCELED,
                    canceledAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(new Id(student.getIdOrThrow())),
                    (assignedPart.evaluationProperties as ReviewProperties).cost,
                    anything(),
                ),
            ).once();
            expect(eventScheduler.cancelMock).toBeCalledWith(new Id(correction.eventId), expect.anything());
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            assertNotification(
                student,
                'Evaluator',
                evaluator,
                assignedPart,
                reviewInFutureFormattedString,
                cancellationNote,
                mailerMock,
                discordMessengerMock,
            );

            expect(analytics.correctionCanceledMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: expect.any(String),
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });

        it('should cancel pending student corrections', async () => {
            correctionRepository.findByEvaluatorMock.mockResolvedValue([]);
            correctionRepository.findByStudentMock.mockResolvedValue([correction]);

            await expect(
                correctionService.cancelPendingCorrections(student.getIdOrThrow().value, cancellationNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                {
                    cancellationNote,
                    status: CorrectionStatus.STUDENT_CANCELED,
                    canceledAt: expect.any(Date),
                },
                expect.anything(),
            );
            expect(eventScheduler.cancelMock).toBeCalledWith(new Id(correction.eventId), expect.anything());
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            assertNotification(
                evaluator,
                'Learner',
                student,
                assignedPart,
                reviewInFutureFormattedString,
                cancellationNote,
                mailerMock,
                discordMessengerMock,
            );

            expect(analytics.correctionCanceledMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });
    });

    describe('reportNoShow', () => {
        const correctionId = 3;
        const eventId = 4;
        const noShowNote = 'test cancellation';

        it('should not report a no-show for a non-existent correction', async () => {
            await expect(
                correctionService.reportNoShow(999, CorrectionStatus.STUDENT_NO_SHOW, noShowNote),
            ).rejects.toEqual(new NotFoundError('Correction was not found'));
        });

        it('should not report a no-show for a completed correction', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.SUCCESS,
                    userId: student.getIdOrThrow().value,
                    evaluatorId: evaluator.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.reportNoShow(correctionId, CorrectionStatus.STUDENT_NO_SHOW, noShowNote),
            ).rejects.toEqual(new IllegalStateError('Cannot report a no-show for a correction in this state'));
        });

        it('should not report a no-show too early', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING,
                    startDate: moment().add(1, 'hour').toDate(),
                    userId: student.getIdOrThrow().value,
                    evaluatorId: evaluator.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.reportNoShow(correctionId, CorrectionStatus.STUDENT_NO_SHOW, noShowNote),
            ).rejects.toEqual(new IllegalStateError('Cannot report a no-show for a correction before its start time'));
        });

        it('Report no-show with a student', async () => {
            const correction = correctionInstance({
                eventId,
                status: CorrectionStatus.PENDING,
                type: CorrectionType.MENTOR,
                startDate: moment().subtract(1, 'hour').toDate(),
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);

            await expect(
                correctionService.reportNoShow(correctionId, CorrectionStatus.EVALUATOR_NO_SHOW, noShowNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    noShowNote,
                    status: CorrectionStatus.EVALUATOR_NO_SHOW,
                    noShowAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(student.getIdOrThrow()),
                    (assignedPart.evaluationProperties as ReviewProperties).cost,
                    anything(),
                ),
            ).once();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();

            const mailerSendArgs = capture(mailerMock.send).last();

            expect(mailerSendArgs).toMatchObject([
                evaluator.email.value,
                {
                    type: MailType.REVIEW_NO_SHOW_STUDENT,
                    name: evaluator.name?.firstName,
                    projectName: assignedPart.name.name,
                    date: expect.any(String),
                    supportLink: expect.any(String),
                },
            ]);
            expect(analytics.correctionNoShowMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });

        it('Report no-show with a student evaluator', async () => {
            const correction = correctionInstance({
                eventId,
                status: CorrectionStatus.PENDING,
                type: CorrectionType.STUDENT,
                startDate: moment().subtract(1, 'hour').toDate(),
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);

            await expect(
                correctionService.reportNoShow(correctionId, CorrectionStatus.STUDENT_NO_SHOW, noShowNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    noShowNote,
                    status: CorrectionStatus.STUDENT_NO_SHOW,
                    noShowAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(evaluator.getIdOrThrow()),
                    (assignedPart.evaluationProperties as ReviewProperties).reward,
                    anything(),
                ),
            ).once();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            const mailerSendArgs = capture(mailerMock.send).last();

            expect(mailerSendArgs).toMatchObject([
                student.email.value,
                {
                    type: MailType.REVIEW_NO_SHOW_STUDENT,
                    name: student.name?.firstName,
                    projectName: assignedPart.name.name,
                    date: expect.any(String),
                    supportLink: expect.any(String),
                },
            ]);
            expect(analytics.correctionNoShowMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: expect.any(String),
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });

        it('Report no-show with a mentor evaluator', async () => {
            const correction = correctionInstance({
                eventId,
                status: CorrectionStatus.PENDING,
                type: CorrectionType.MENTOR,
                startDate: moment().subtract(1, 'hour').toDate(),
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);

            await expect(
                correctionService.reportNoShow(correctionId, CorrectionStatus.STUDENT_NO_SHOW, noShowNote),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    noShowNote,
                    status: CorrectionStatus.STUDENT_NO_SHOW,
                    noShowAt: expect.any(Date),
                },
                expect.anything(),
            );
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            const mailerSendArgs = capture(mailerMock.send).last();

            expect(mailerSendArgs).toMatchObject([
                student.email.value,
                {
                    type: MailType.REVIEW_NO_SHOW_STUDENT,
                    name: student.name?.firstName,
                    projectName: assignedPart.name.name,
                    date: expect.any(String),
                    supportLink: expect.any(String),
                },
            ]);
            expect(analytics.correctionNoShowMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                type: correction.type,
                peerId: correction.evaluatorId,
                minutesRemaining: expect.any(Number),
            });
        });
    });

    describe('setCriticalError', () => {
        const correctionId = 1;

        it('should not set for non-existent', async () => {
            await expect(correctionService.setHasCriticalError(999, true)).rejects.toEqual(
                new NotFoundError('Correction was not found'),
            );
        });

        it('should not set if correction is not in-progress', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    evaluatorId: evaluator.getIdOrThrow().value,
                    id: correctionId,
                    status: CorrectionStatus.PENDING,
                }),
            );

            await expect(correctionService.setHasCriticalError(correctionId, true)).rejects.toEqual(
                new IllegalStateError('Cannot set critical error for an inactive correction'),
            );
        });

        it('should set critical error to true', async () => {
            const correction = correctionInstance({
                evaluatorId: evaluator.getIdOrThrow().value,
                userId: student.getIdOrThrow().value,
                id: correctionId,
                status: CorrectionStatus.IN_PROGRESS,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);
            correctionRepository.updateMock.mockResolvedValue(
                correctionInstance({
                    ...correction,
                    hasCriticalError: true,
                }),
            );

            await expect(correctionService.setHasCriticalError(correctionId, true)).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                { hasCriticalError: true },
                expect.anything(),
            );
            expect(analytics.correctionFlaggedAsFailedMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                peerUsername: evaluator.username?.value,
                isFlagged: true,
                minutesRemaining: expect.any(Number),
            });
        });

        it('should set critical error to false', async () => {
            const correction = correctionInstance({
                evaluatorId: evaluator.getIdOrThrow().value,
                userId: student.getIdOrThrow().value,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
                id: correctionId,
                status: CorrectionStatus.IN_PROGRESS,
            });
            correctionRepository.findByIdMock.mockResolvedValue(correction);
            correctionRepository.updateMock.mockResolvedValue(
                correctionInstance({
                    ...correction,
                    hasCriticalError: false,
                }),
            );

            await expect(correctionService.setHasCriticalError(correctionId, false)).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                { hasCriticalError: false },
                expect.anything(),
            );
            expect(analytics.correctionFlaggedAsFailedMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                peerUsername: evaluator.username?.value,
                isFlagged: false,
                minutesRemaining: expect.any(Number),
            });
        });
    });

    describe('submitCorrection', () => {
        let correction: CorrectionEntity;

        beforeEach(() => {
            correction = correctionInstance({
                id: 1,
                status: CorrectionStatus.IN_PROGRESS,
                questions: [],
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                hasCriticalError: false,
                assignedSprintPartId: assignedPart.getIdOrThrow().value,
            });

            correctionRepository.findByIdMock.mockResolvedValue(correction);
        });

        it('submit successful mentor correction', async () => {
            evaluator = new UserAccount({ ...evaluator, role: Role.MENTOR });
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 3,
                    weight: 1,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                    evaluation: 4,
                    weight: 1,
                },
                {
                    id: 3,
                    text: 'Test question 3',
                    evaluation: 5,
                    weight: 1,
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(
                correctionService.submitCorrection(correction.id, 'Test feedback', 'Test note'),
            ).resolves.toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 80,
                    evaluatorFeedback: 'Test feedback',
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: expect.any(Date),
                }),
                expect.anything(),
            );
            expect(notebook.createMock).toBeCalledWith(
                new InternalStudentNote(
                    new Id(correction.evaluatorId),
                    new NoteContent('Test note'),
                    new Id(correction.userId),
                ),
                expect.anything(),
            );
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            expect(analytics.correctionSubmittedMock).toBeCalledWith(correction.evaluatorId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                peerUsername: student.username?.value,
                grade: 80,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
            expect(analytics.correctionReceivedMock).toBeCalledWith(correction.userId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                evaluatorUsername: evaluator.username?.value,
                grade: 80,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
        });

        it('submit failed mentor correction', async () => {
            evaluator = new UserAccount({ ...evaluator, role: Role.MENTOR });
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 1,
                    weight: 1,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                    evaluation: 2,
                    weight: 1,
                },
                {
                    id: 3,
                    text: 'Test question 3',
                    evaluation: 3,
                    weight: 1,
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, '')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 40,
                    evaluatorFeedback: '',
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: expect.any(Date),
                }),
                expect.anything(),
            );
            expect(notebook.createMock).not.toHaveBeenCalled();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            expect(analytics.correctionSubmittedMock).toBeCalledWith(correction.evaluatorId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                peerUsername: student.username?.value,
                grade: 40,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
            expect(analytics.correctionReceivedMock).toBeCalledWith(correction.userId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                evaluatorUsername: evaluator.username?.value,
                grade: 40,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
        });

        it('submit mentor correction with critical error', async () => {
            evaluator = new UserAccount({ ...evaluator, role: Role.MENTOR });
            correction.hasCriticalError = true;
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 1,
                    weight: 1,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                    evaluation: 2,
                    weight: 1,
                },
                {
                    id: 3,
                    text: 'Test question 3',
                    weight: 1,
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, '')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 0,
                    evaluatorFeedback: '',
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: expect.any(Date),
                }),
                expect.anything(),
            );
            expect(notebook.createMock).not.toBeCalled();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            expect(analytics.correctionSubmittedMock).toBeCalledWith(correction.evaluatorId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                peerUsername: student.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: true,
            });
            expect(analytics.correctionReceivedMock).toBeCalledWith(correction.userId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                evaluatorUsername: evaluator.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: true,
            });
        });

        it('submit peer correction', async () => {
            correction.type = CorrectionType.STUDENT;
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, 'Test feedback')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 0,
                    evaluatorFeedback: 'Test feedback',
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: expect.any(Date),
                }),
                expect.anything(),
            );
            verify(
                correctionPointsTeller.credit(
                    deepEqual(new Id(correction.evaluatorId)),
                    (assignedPart.evaluationProperties as ReviewProperties).reward,
                    anything(),
                ),
            ).once();
            expect(notebook.createMock).not.toHaveBeenCalled();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            expect(analytics.correctionSubmittedMock).toBeCalledWith(correction.evaluatorId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                peerUsername: student.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
            expect(analytics.correctionReceivedMock).toBeCalledWith(correction.userId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                evaluatorUsername: evaluator.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
        });

        it('submit peer correction with mentor', async () => {
            evaluator = new UserAccount({ ...evaluator, role: Role.MENTOR });
            correction.type = CorrectionType.STUDENT;
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, 'Test feedback')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 0,
                    evaluatorFeedback: 'Test feedback',
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    endDate: expect.any(Date),
                }),
                expect.anything(),
            );
            expect(notebook.createMock).not.toHaveBeenCalled();
            verify(
                submissionManager.revokeAccess(
                    deepEqual(assignedPart),
                    deepEqual(evaluator.getIdOrThrow()),
                    anything(),
                ),
            ).called();
            expect(analytics.correctionSubmittedMock).toBeCalledWith(correction.evaluatorId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                peerUsername: student.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
            expect(analytics.correctionReceivedMock).toBeCalledWith(correction.userId, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                sprintPartName: assignedPart.name.name,
                evaluatorUsername: evaluator.username?.value,
                grade: 0,
                time: expect.any(Date),
                status: expect.any(String),
                isFlagged: false,
            });
        });

        it('round evaluation down', async () => {
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 3,
                    weight: 3,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                    evaluation: 4,
                    weight: 2,
                },
                {
                    id: 3,
                    text: 'Test question 3',
                    evaluation: 5,
                    weight: 1,
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, '')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 73,
                }),
                expect.anything(),
            );
        });

        it('round evaluation up', async () => {
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 3,
                    weight: 1,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                    evaluation: 4,
                    weight: 2,
                },
                {
                    id: 3,
                    text: 'Test question 3',
                    evaluation: 5,
                    weight: 3,
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];
            correctionRepository.updateMock.mockImplementation((id, data) => ({ ...correction, ...data }));

            await expect(correctionService.submitCorrection(correction.id, '')).toResolve();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correction.id,
                expect.objectContaining({
                    evaluation: 87,
                }),
                expect.anything(),
            );
        });

        it('should not submit non-existent correction', async () => {
            correctionRepository.findByIdMock.mockResolvedValue(undefined);

            await expect(correctionService.submitCorrection(1000, 'Test feedback')).rejects.toThrow(
                'Correction is not active',
            );
            expect(correctionRepository.updateMock).not.toBeCalled();
            expect(analytics.correctionSubmittedMock).not.toBeCalled();
            expect(analytics.correctionReceivedMock).not.toBeCalled();
        });

        it('should not submit pending correction', async () => {
            correction.status = CorrectionStatus.PENDING;

            await expect(correctionService.submitCorrection(correction.id, 'Test feedback')).rejects.toThrow(
                'Correction is not active',
            );
            expect(correctionRepository.updateMock).not.toBeCalled();
            expect(analytics.correctionSubmittedMock).not.toBeCalled();
            expect(analytics.correctionReceivedMock).not.toBeCalled();
        });

        it('should not submit completed correction', async () => {
            correction.status = CorrectionStatus.SUCCESS;

            await expect(correctionService.submitCorrection(correction.id, 'Test feedback')).rejects.toThrow(
                'Correction is not active',
            );
            expect(correctionRepository.updateMock).not.toBeCalled();
            expect(analytics.correctionSubmittedMock).not.toBeCalled();
            expect(analytics.correctionReceivedMock).not.toBeCalled();
        });

        it('should not submit incomplete correction', async () => {
            correction.questions = [
                {
                    id: 1,
                    text: 'Test question 1',
                    evaluation: 1,
                },
                {
                    id: 2,
                    text: 'Test question 2',
                },
            ] as AssignedCorrectionQuestionPersistenceEntity[];

            await expect(correctionService.submitCorrection(correction.id, 'Test feedback')).rejects.toThrow(
                'Correction is incomplete',
            );
            expect(correctionRepository.updateMock).not.toBeCalled();
            expect(analytics.correctionSubmittedMock).not.toBeCalled();
            expect(analytics.correctionReceivedMock).not.toBeCalled();
        });
    });

    describe('submitCorrectionQuestionEvaluation', () => {
        let correction: CorrectionEntity;

        beforeEach(() => {
            correction = correctionInstance({
                id: 1,
                status: CorrectionStatus.IN_PROGRESS,
                userId: student.getIdOrThrow().value,
                evaluatorId: evaluator.getIdOrThrow().value,
                questions: [
                    {
                        id: 1,
                        text: 'test-text',
                        image: 'test-image',
                        evaluation: 5,
                        correctionId: 1,
                    } as AssignedCorrectionQuestionPersistenceEntity,
                ] as AssignedCorrectionQuestionPersistenceEntity[],
            });
            correctionRepository.findByIdMock.mockReturnValue(correction);
            assignedCorrectionQuestionRepository.updateMock.mockReset();
        });

        it('should submit evaluation', async () => {
            assignedCorrectionQuestionRepository.findByIdMock.mockReturnValueOnce(correction.questions[0]);

            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1, 5)).toResolve();
            expect(assignedCorrectionQuestionRepository.updateMock).toBeCalledWith(
                correction.questions[0].id,
                { evaluation: 5 },
                undefined,
            );
        });

        it('should not submit for another correction question', async () => {
            assignedCorrectionQuestionRepository.findByIdMock.mockReturnValueOnce({
                ...correction.questions[0],
                correctionId: 999,
            });

            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1000, 5)).rejects.toEqual(
                new NotFoundError('Question was not found'),
            );
            expect(assignedCorrectionQuestionRepository.updateMock).not.toBeCalled();
        });

        it('should not submit for non-existent correction', async () => {
            correctionRepository.findByIdMock.mockReturnValue(undefined);

            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1, 5)).rejects.toEqual(
                new NotFoundError('Correction is not active'),
            );
            expect(assignedCorrectionQuestionRepository.updateMock).not.toBeCalled();
        });

        it('should not submit for non-existent question', async () => {
            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1000, 5)).rejects.toEqual(
                new NotFoundError('Question was not found'),
            );
        });

        it('should not submit for a pending correction', async () => {
            correction.status = CorrectionStatus.PENDING;
            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1, 5)).rejects.toThrow(
                'Correction is not active',
            );
            expect(assignedCorrectionQuestionRepository.updateMock).not.toBeCalled();
        });

        it('should not submit for a completed correction', async () => {
            correction.status = CorrectionStatus.SUCCESS;
            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1, 5)).rejects.toThrow(
                'Correction is not active',
            );
            expect(assignedCorrectionQuestionRepository.updateMock).not.toBeCalled();
        });

        it('should handle update correction not found', async () => {
            await expect(correctionService.submitCorrectionQuestionEvaluation(1, 1, 5)).rejects.toThrow(
                'Question was not found',
            );
            expect(assignedCorrectionQuestionRepository.updateMock).not.toBeCalled();
        });
    });

    describe('submitFeedbackQuestionEvaluation', () => {
        it('should not submit question of a pending correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(correctionService.submitFeedbackQuestionEvaluation(correctionId, 3, 4)).rejects.toEqual(
                new IllegalStateError('Correction is in a wrong state'),
            );
        });

        it('should not submit question of an in-progress correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.IN_PROGRESS,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(correctionService.submitFeedbackQuestionEvaluation(correctionId, 3, 4)).rejects.toEqual(
                new IllegalStateError('Correction is in a wrong state'),
            );
        });

        it('should not submit question of a failed correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.FAIL,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(correctionService.submitFeedbackQuestionEvaluation(correctionId, 3, 4)).rejects.toEqual(
                new IllegalStateError('Correction is in a wrong state'),
            );
        });

        it('should not submit question of a successful correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.SUCCESS,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(correctionService.submitFeedbackQuestionEvaluation(correctionId, 3, 4)).rejects.toEqual(
                new IllegalStateError('Correction is in a wrong state'),
            );
        });

        it('should not submit question of another correction', async () => {
            const correctionId = 2;
            const questionId = 3;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    userId: student.getIdOrThrow().value,
                    studentFeedbackQuestions: [],
                }),
            );

            await expect(
                correctionService.submitFeedbackQuestionEvaluation(correctionId, questionId, 4),
            ).rejects.toEqual(new IllegalStateError('Correction feedback question was not found'));
        });

        it('should submit feedback question evaluation', async () => {
            const correctionId = 2;
            const questionId = 3;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    userId: student.getIdOrThrow().value,
                    studentFeedbackQuestions: [
                        {
                            id: questionId,
                            text: 'Test question',
                            evaluation: 2,
                        } as AssignedStudentCorrectionFeedbackQuestionPersistenceEntity,
                    ],
                }),
            );

            await expect(
                correctionService.submitFeedbackQuestionEvaluation(correctionId, questionId, 4),
            ).resolves.toBeUndefined();

            expect(assignedStudentFeedbackQuestionRepository.updateMock).toBeCalledWith(
                questionId,
                { evaluation: 4 },
                expect.anything(),
            );
        });
    });

    describe('submitFeedback', () => {
        it('should not submit student feedback of a pending correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.submitFeedback(correctionId, {
                    feedback: '',
                    evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
                }),
            ).rejects.toEqual(new IllegalStateError('Correction is in a wrong state'));
        });

        it('should not submit student feedback of an in-progress correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.IN_PROGRESS,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.submitFeedback(correctionId, {
                    feedback: '',
                    evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
                }),
            ).rejects.toEqual(new IllegalStateError('Correction is in a wrong state'));
        });

        it('should not submit student feedback of a failed correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.FAIL,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.submitFeedback(correctionId, {
                    feedback: '',
                    evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
                }),
            ).rejects.toEqual(new IllegalStateError('Correction is in a wrong state'));
        });

        it('should not submit student feedback of a successful correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.SUCCESS,
                    userId: student.getIdOrThrow().value,
                }),
            );

            await expect(
                correctionService.submitFeedback(correctionId, {
                    feedback: '',
                    evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
                }),
            ).rejects.toEqual(new IllegalStateError('Correction is in a wrong state'));
        });

        it('should not submit student feedback with incomplete questions', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    userId: student.getIdOrThrow().value,
                    studentFeedbackQuestions: [
                        { evaluation: 5 },
                        { evaluation: undefined },
                    ] as AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[],
                }),
            );

            await expect(
                correctionService.submitFeedback(correctionId, {
                    feedback: '',
                    evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
                }),
            ).rejects.toEqual(new IllegalStateError('Not all feedback questions have been answered'));
        });

        it('should submit student feedback for successful correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    id: correctionId,
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    evaluation: 100,
                    userId: student.getIdOrThrow().value,
                    type: CorrectionType.STUDENT,
                    evaluatorId: evaluator.getIdOrThrow().value,
                    assignedSprintPartId: assignedPart.getIdOrThrow().value,
                    studentFeedbackQuestions: [
                        { evaluation: 5 },
                        { evaluation: 4 },
                    ] as AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[],
                }),
            );

            const reviewFeedback = ReviewFeedback.fromParams({
                feedback: 'Student feedback',
                internalFeedback: 'Internal feedback',
                evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
            });

            await correctionService.submitFeedback(correctionId, reviewFeedback);

            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    studentFeedback: reviewFeedback.feedback,
                    studentInternalFeedback: reviewFeedback.internalFeedback,
                    studentEvaluatorFuturePreference: reviewFeedback.evaluatorFutureReviewPreference,
                    status: CorrectionStatus.SUCCESS,
                },
                expect.anything(),
            );
            expect(analytics.studentCorrectionFeedbackSubmittedMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                evaluatorUsername: evaluator.username?.value,
            });
            verify(
                queueService.command(
                    deepEqual({
                        command: new CompletedReviewCommand({
                            studentId: student.getIdOrThrow(),
                            reviewId: new Id(correctionId),
                            reviewStatus: CorrectionStatus.SUCCESS,
                        }),
                    }),
                ),
            ).once();
        });

        it('should submit student feedback for failed correction', async () => {
            const correctionId = 2;
            correctionRepository.findByIdMock.mockResolvedValue(
                correctionInstance({
                    id: correctionId,
                    status: CorrectionStatus.PENDING_STUDENT_FEEDBACK,
                    evaluation: 50,
                    userId: student.getIdOrThrow().value,
                    type: CorrectionType.STUDENT,
                    evaluatorId: evaluator.getIdOrThrow().value,
                    assignedSprintPartId: assignedPart.getIdOrThrow().value,
                    studentFeedbackQuestions: [
                        { evaluation: 5 },
                        { evaluation: 4 },
                    ] as AssignedStudentCorrectionFeedbackQuestionPersistenceEntity[],
                }),
            );

            const reviewFeedback = ReviewFeedback.fromParams({
                feedback: 'Test feeedback',
                internalFeedback: 'Internal feedback',
                evaluatorFutureReviewPreference: faker.helpers.enumValue(ReviewEvaluatorFuturePreference),
            });

            const result = await correctionService.submitFeedback(correctionId, reviewFeedback);
            expect(result).toBeUndefined();
            expect(correctionRepository.updateMock).toBeCalledWith(
                correctionId,
                {
                    studentFeedback: reviewFeedback.feedback,
                    studentInternalFeedback: reviewFeedback.internalFeedback,
                    studentEvaluatorFuturePreference: reviewFeedback.evaluatorFutureReviewPreference,
                    status: CorrectionStatus.FAIL,
                },
                expect.anything(),
            );
            expect(analytics.studentCorrectionFeedbackSubmittedMock).toBeCalledWith(student.getIdOrThrow().value, {
                sprintPartId: assignedPart.sprintPartId.value,
                sprintPartAbbreviation: assignedPart.abbreviation.value,
                evaluatorUsername: evaluator.username?.value,
            });
            verify(
                queueService.command(
                    deepEqual({
                        command: new CompletedReviewCommand({
                            studentId: student.getIdOrThrow(),
                            reviewId: new Id(correctionId),
                            reviewStatus: CorrectionStatus.FAIL,
                        }),
                    }),
                ),
            ).once();
        });
    });
});
