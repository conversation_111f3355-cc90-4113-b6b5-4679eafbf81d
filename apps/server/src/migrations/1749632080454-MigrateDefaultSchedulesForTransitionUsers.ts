import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateDefaultSchedulesForTransitionUsers1749632080454 implements MigrationInterface {
    name = 'MigrateDefaultSchedulesForTransitionUsers1749632080454';

    public async up(queryRunner: QueryRunner): Promise<void> {
        if (process.env.NODE_ENV === 'local') {
            console.log('Skipping migration MigrateDefaultSchedulesForTransitionUsers in local environment');
            return;
        }

        await queryRunner.query(`
            INSERT INTO pgboss.job (name, data, startafter)
            SELECT
                'uzt.schedule.setDefaultSchedule' as name,
                json_build_object('userId', pu.id) as data,
                NOW() as startafter  -- Immediate execution
            FROM platform_user pu
            INNER JOIN gov_profile gp ON pu.id = gp.user_id
            INNER JOIN gov_group gg ON gp.group_id = gg.id
            INNER JOIN gov_program gpr ON gg.program_id = gpr.id
            INNER JOIN gov_agency ga ON gpr.agency_id = ga.id
            WHERE pu.role = 'user'
              AND pu.state = 'onboarding'
              AND ga.type IN ('uzt', 'afa')
              AND gg.period_to > NOW()
              AND pu.id NOT IN (
                SELECT DISTINCT lws.user_id
                FROM learner_week_schedule lws
                WHERE lws.user_id = pu.id
              );
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DELETE FROM pgboss.job
            WHERE name = 'uzt.schedule.setDefaultSchedule'
              AND state IN ('created', 'retry')
              AND createdon >= NOW() - INTERVAL '1 hour'  -- Only recent jobs from this migration
        `);
    }
}
