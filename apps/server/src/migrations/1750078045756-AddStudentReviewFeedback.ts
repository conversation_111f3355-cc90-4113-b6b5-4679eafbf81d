import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStudentReviewFeedback1750078045756 implements MigrationInterface {
    name = 'AddStudentReviewFeedback1750078045756';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "correction" ADD "student_internal_feedback" character varying');
        await queryRunner.query(
            "CREATE TYPE \"public\".\"correction_student_evaluator_future_preference_enum\" AS ENUM('less_often', 'more_often', 'no_preference')",
        );
        await queryRunner.query(
            'ALTER TABLE "correction" ADD "student_evaluator_future_preference" "public"."correction_student_evaluator_future_preference_enum"',
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "correction" DROP COLUMN "student_evaluator_future_preference"');
        await queryRunner.query('DROP TYPE "public"."correction_student_evaluator_future_preference_enum"');
        await queryRunner.query('ALTER TABLE "correction" DROP COLUMN "student_internal_feedback"');
    }
}
