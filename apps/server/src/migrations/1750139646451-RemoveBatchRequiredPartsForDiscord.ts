import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveBatchRequiredPartsForDiscord1750139646451 implements MigrationInterface {
    name = 'RemoveBatchRequiredPartsForDiscord1750139646451';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "batch" DROP COLUMN "required_parts_for_discord"');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "batch" ADD "required_parts_for_discord" integer NOT NULL DEFAULT \'3\'');
    }
}
