import { use<PERSON>ontainer } from 'routing-controllers';
import { Container } from 'typedi';
import { AnalyticsPlatformModule } from '../analytics-platform/infrastructure/di/AnalyticsPlatformModule';
import { DataEncryptionModule } from '../data-encryption/infrastructure/di/DataEncryptionModule';
import { AnalyticsModule } from '../analytics/infrastructure/di/AnalyticsModule';
import setupAnnouncementDependencies from '../announcements/infrastructure/di/setup';
import { ApiSchemaModule } from '../api-schema/infrastructure/di/ApiSchemaModule';
import { ExpressApplicationModule } from '../app/infrastructure/di/ExpressApplicationModule';
import { AuthModule } from '../auth/infrastructure/di/AuthModule';
import setupBatchDependencies from '../batches/infrastructure/di/setup';
import { CalendarSettingsModule } from '../calendar-settings/infrastructure/di/CalendarSettingsModule';
import { CalendarModule } from '../calendar/infrastructure/di/CalendarModule';
import { DefaultIcsService } from '../calendar/services/IcsService';
import { ClientUrlModule } from '../client-url/infrastructure/di/ClientUrlModule';
import { Configuration } from '../config/Configuration';
import { ConfigurationModule } from '../config/infrastructure/di/ConfigurationModule';
import controllers from '../controllers';
import TransactionManager from '../core/infrastructure/TransactionManager';
import RequestContextStorage from '../core/infrastructure/context/RequestContextStorage';
import setupCoreDependencies from '../core/infrastructure/di/setup';
import { CorrectionsModule } from '../corrections/infrastructure/di/CorrectionsModule';
import { CostTrackingModule } from '../cost-tracking/infrastructure/di/CostTrackingModule';
import { CostTrackingAuditModule } from '../cost-tracking/submodules/cost-tracking-audit/infrastructure/di/CostTrackingAuditModule';
import { CostTrackingAutoTrackingModule } from '../cost-tracking/submodules/cost-tracking-auto-tracking/infrastructure/di/CostTrackingAutoTrackingModule';
import { CostTrackingProfileModule } from '../cost-tracking/submodules/cost-tracking-profile/infrastructure/di/CostTrackingProfileModule';
import { CostTrackingWorklogEntryModule } from '../cost-tracking/submodules/cost-tracking-worklog-entry/infrastructure/di/CostTrackingWorklogEntryModule';
import { CostTrackingWorklogModule } from '../cost-tracking/submodules/cost-tracking-worklog/infrastructure/di/CostTrackingWorklogModule';
import { CountriesModule } from '../countries/di/setup';
import { CronModule } from '../cron/infrastructure/di/CronModule';
import { DataSourceModule } from '../db/DataSourceModule';
import { DiscordPlatformModule } from '../discord-platform/infrastructure/di/DiscordPlatformModule';
import { BatchDiscordModule } from '../discord-platform/submodules/batch-discord/infrastructure/di/BatchDiscordModule';
import { CourseDiscordModule } from '../discord-platform/submodules/course-discord/infrastructure/di/CourseDiscordModule';
import { DiscordServerInfoSyncModule } from '../discord-platform/submodules/discord-server-info-sync/infrastructure/di/DiscordServerInfoSyncModule';
import { DiscordServerRoleModule } from '../discord-platform/submodules/discord-server-role/infrastructure/di/DiscordServerRoleModule';
import { DiscordServerModule } from '../discord-platform/submodules/discord-server/infrastructure/di/DiscordServerModule';
import { UserDiscordModule } from '../discord-platform/submodules/user-discord/infrastructure/di/UserDiscordModule';
import { DiscordProfileModule } from '../discord-profile/infrastructure/di/DiscordProfileModule';
import { DiscordTrackerModule } from '../discord-tracker/infrastructure/di/DiscordTrackerModule';
import { DiscordModule } from '../discord/infrastructure/di/DiscordModule';
import { DocumentModule } from '../document/infrastructure/di/DocumentModule';
import { DokobitModule } from '../dokobit/DokobitModule';
import { CourseModule } from '../education/courses/infrastructure/di/CourseModule';
import { ModulesModule } from '../education/modules/infrastructure/di/ModulesModule';
import { QuizQuestionModule } from '../education/quizzes/infrastructure/di/QuizQuestionModule';
import { ReviewQuestionsModule } from '../education/review-questions/infrastructure/di/ReviewQuestionsModule';
import { SprintsModule } from '../education/sprints/infrastructure/di/SprintsModule';
import { EndorsementModule } from '../endorsement/infrastructure/di/EndorsementModule';
import { ErrorTrackingModule } from '../error-tracking/infrastructure/di/ErrorTrackingModule';
import { ErrorTracker } from '../error-tracking/services/ErrorTracker';
import { ApplicationEventBrokerModule } from '../events/infrastructure/di/ApplicationEventBrokerModule';
import { FeatureFlagModule } from '../feature-flag/infrasturucture/di/FeatureFlagModule';
import { GithubModule } from '../github/infrastructure/di/GithubModule';
import { GoogleProfileModule } from '../google-account/infrastructure/di/GoogleProfileModule';
import { GovAgencyModule } from '../gov/submodules/gov-agency/infrastructure/di/GovAgencyModule';
import { GovAggregatedEducationPlanModule } from '../gov/submodules/gov-aggregated-education-plan/infrastructure/di/GovAggregatedEducationPlanModule';
import { GovAttendanceModule } from '../gov/submodules/gov-attendance/infrastructure/di/GovAttendanceModule';
import { GovEducationPlanModule } from '../gov/submodules/gov-education-plan/infrastructure/di/GovEducationPlanModule';
import { GovGroupEducationPlanModule } from '../gov/submodules/gov-group-education-plan/infrastructure/di/GovGroupEducationPlanModule';
import { GovGroupModule } from '../gov/submodules/gov-group/infrastructure/di/GovGroupModule';
import { GovLearnersModule } from '../gov/submodules/gov-learners/infrastructure/di/GovLearnersModule';
import { GovParticipationAggregationModule } from '../gov/submodules/gov-participation-aggregation/infrastructure/di/GovParticipationAggregationModule';
import { GovParticipationDayAutoAggregationModule } from '../gov/submodules/gov-participation-auto-aggregation/infrastructure/di/GovParticipationDayAutoAggregationModule';
import { GovParticipationModule } from '../gov/submodules/gov-participation/infrastructure/di/GovParticipationModule';
import { GovProgramModule } from '../gov/submodules/gov-program/infrastructure/di/GovProgramModule';
import { GovWeekParticipationModule } from '../gov/submodules/gov-week-participation/infrastructure/di/GovWeekParticipationModule';
import { DefaultWeekScheduleModule } from '../gov/submodules/gov-week-schedule/submodules/default-week-schedule/infrastructure/di/DefaultWeekScheduleModule';
import { InvalidWeekScheduleNotificationModule } from '../gov/submodules/gov-week-schedule/submodules/invalid-week-schedule-notification/infrastructure/di/InvalidWeekScheduleNotificationModule';
import { WeekScheduleConsentModule } from '../gov/submodules/gov-week-schedule/submodules/week-schedule-consent/infrastructure/di/WeekScheduleConsentModule';
import { WeekScheduleGroupsModule } from '../gov/submodules/gov-week-schedule/submodules/week-schedule-groups/infrastructure/di/WeekScheduleGroupsModule';
import { WeekScheduleRequirementsModule } from '../gov/submodules/gov-week-schedule/submodules/week-schedule-requirements/infrastructure/di/WeekScheduleRequirementsModule';
import { WeekScheduleModule } from '../gov/submodules/gov-week-schedule/submodules/week-schedule/infrastructure/di/WeekScheduleModule';
import { HealthModule } from '../health/infrastructure/di/HealthModule';
import { InvoiceModule } from '../invoices/infrastructure/di/InvoiceModule';
import { LearningMilestonesModule } from '../learning-milestones/infrastructure/di/LearningMilestonesModule';
import LearningPath from '../learning/services/LearningPath';
import { LearningDeadlinesModule } from '../learning/submodules/deadlines/infrastructure/di/LearningDeadlinesModule';
import { LearningEvaluationModule } from '../learning/submodules/evaluation/infrastructure/di/LearningEvaluationModule';
import { LearningQuizModule } from '../learning/submodules/quizzes/infrastructure/di/LearningQuizModule';
import { LearningRoadmapModule } from '../learning/submodules/roadmap/infrastructure/di/LearningRoadmapModule';
import { LearningStandupAttendanceRequirementsModule } from '../learning/submodules/standup-attendance-requirements/infrastructure/di/LearningStandupAttendanceRequirementsModule';
import { LoggingModule } from '../logging/infrastructure/di/LoggingModule';
import { setupMailerDependencies } from '../mailer/infrastructure/di/setup';
import { MeetingModule } from '../meetings/infrastructure/di/MeetingModule';
import { MentorAssignedCourseModule } from '../mentor-assigned-course/infrastructure/di/MentorAssignedCourseModule';
import { MessagingModule } from '../messaging/di/MessagingModule';
import { NotesModule } from '../notes/infrastructure/di/NotesModule';
import { OnboardingModule } from '../onboarding/infrastructure/di/OnboardingModule';
import { PhysicalAddressModule } from '../physical-address/infrastructure/di/PhysicalAddressModule';
import { PresenceTrackingModule } from '../presence-tracking/infrastructure/di/PresenceTrackingModule';
import { ProfileSetupModule } from '../profile-setup-steps/infrastructure/di/ProfileSetupModule';
import { QueueModule } from '../queue/infrastructure/di/QueueModule';
import { ResumeModule } from '../resume/infrastructure/di/setup';
import { ReviewSettingsModule } from '../review/submodules/review-settings/infrastructure/di/ReviewSettingsModule';
import { StudentSignupModule } from '../student-signup/infrastructure/di/StudentSignupModule';
import { TimeslotModule } from '../timeslot/infrastructure/di/TimeslotModule';
import { TimezoneModule } from '../timezone/infrastructure/di/TimezoneModule';
import { UserConnectionsModule } from '../user-connections/infrastructure/di/UserConnectionsModule';
import { UserConsentModule } from '../user-consent/infrastructure/di/UserConsentModule';
import { UserPermissionsModule } from '../user-permissions/infrastructure/di/UserPermissionsModule';
import { PasswordRepository } from '../users/accounts/infrastructure/db/PasswordRepository';
import { UsersAccountsModule } from '../users/accounts/infrastructure/di/UsersAccountsModule';
import { MentorModule } from '../users/mentors/infrastructure/di/MentorModule';
import { Role } from '../users/shared/infrastructure/db/User';
import { UserRepository } from '../users/shared/infrastructure/db/UserRepository';
import { SignupModule } from '../users/signup/infrastructure/di/SignupModule';
import { SignupCompletedEventWorker } from '../users/signup/events/SignupCompletedEventWorker';
import { EmailAvailabilityChecker } from '../users/signup/services/EmailAvailabilityChecker';
import MentorSignupFinishStrategy from '../users/signup/services/MentorSignupFinishStrategy';
import { MentorSignupStarter } from '../users/signup/services/MentorSignupStarter';
import { SignupIntegrationEventsPublisher } from '../users/signup/services/SignupEventsPublisher';
import { SignupFinisher, SignupFinisherStrategy } from '../users/signup/services/SignupFinisher';
import { StaffSignupFinishStrategy } from '../users/signup/services/StaffSignupFinishStrategy';
import { StaffSignupStarter } from '../users/signup/services/StaffSignupStarter';
import { StudentsManagementModule } from '../users/students/management/infrastructure/di/StudentsManagementModule';
import { UsersStudentsProfileModule } from '../users/students/profile/infrastructure/di/UsersStudentsProfileModule';
import { UsersStudentsSettingsModule } from '../users/students/settings/infrastructure/di/UsersStudentsSettingsModule';
import { UsersStudentsTermsModule } from '../users/students/terms/infrastructure/di/UsersStudentsTermsModule';
import { UsersTagsModule } from '../users/tags/infrastructure/di/UsersTagsModule';
import Logger from '../utils/logger/Logger';
import { UztAlertsModule } from '../uzt/submodules/uzt-alerts/infrastructure/di/UztAlertsModule';
import { UztChapterModule } from '../uzt/submodules/uzt-chapter/infrastructure/di/UztChapterModule';
import { UztDocumentModule } from '../uzt/submodules/uzt-document/infrastructure/di/UztDocumentModule';
import { UztMonthlyInfoModule } from '../uzt/submodules/uzt-monthly-info/infrastructure/di/UztMonthlyInfoModule';
import { UztNotificationModule } from '../uzt/submodules/uzt-notification/infrastructure/di/UztNotificationModule';
import { UztParticipationModule } from '../uzt/submodules/uzt-participation/infrastructure/di/UztParticipationModule';
import { UztProfileModule } from '../uzt/submodules/uzt-profile/infrastructure/di/UztProfileModule';
import { UztProofOfCompletionModule } from '../uzt/submodules/uzt-proof-of-completion/infrastructure/di/UztProofOfCompletionModule';
import { UztReportsModule } from '../uzt/submodules/uzt-reports/infrastructure/di/UztReportsModule';
import { VersionModule } from '../version/infrastructure/di/VersionModule';
import { VirtualClassroomModule } from '../virtual-classroom/infrastructure/di/VirtualClassroomModule';
import {
    EmailAvailabilityCheckerToken,
    IcsServiceToken,
    MentorSignupStarterToken,
    SignupFinisherToken,
    StaffSignupStarterToken,
    UserRepositoryToken,
} from './tokens';
import { ModuleOnStart, ModuleOnStop, ModuleWithControllers } from './types/ApplicationModule';
import { GovProfileModule } from '../gov/submodules/gov-profile/infrastructure/di/GovProfileModule';
import { HolidaysModule } from '../holidays/infrastructure/di/HolidaysModule';
import { StaffModule } from '../users/staff/infrastructure/di/StaffModule';
import { PrivacySettingsModule } from '../privacy/submodules/infrastructure/di/PrivacySettingsModule';
import { NotificationModule } from '../notifications/infrastructure/di/NotificationModule';
import setupIntercomDependencies from '../intercom/infrastructure/di/setup';

export interface CoreDi {
    configurationModule: ConfigurationModule;
    errorTrackingModule: ErrorTrackingModule;
    transactionManager: TransactionManager;
    loggingModule: LoggingModule;
    requestContextStorage: RequestContextStorage;
    dataSourceModule: DataSourceModule;
    queueModule: QueueModule;
    featureFlagModule: FeatureFlagModule;
    cronModule: CronModule;
    applicationEventBrokerModule: ApplicationEventBrokerModule;
    messagingModule: MessagingModule;
}

export class ApplicationLifeCycleController {
    private isInitialized = false;
    private isDiResolved = false;
    private onStartModules: ModuleOnStart[] = [];
    private onStopModules: ModuleOnStop[] = [];
    private controllers: any[] = [];

    private errorTracker: ErrorTracker;
    private logger: Logger;

    constructor(private readonly configuration: Configuration) {}

    onInit(): void {
        if (this.isInitialized) {
            throw new Error('Application DI is already set up');
        }

        this.resolveDi();
        this.initRejectionAndExceptionHandlers();
    }

    async onStart(): Promise<void> {
        this.assertIsInitialized();

        for (const mod of this.onStartModules) {
            this.logger.debug(`Starting module ${mod.getName()}`);
            try {
                await mod.moduleOnStart();
            } catch (error) {
                this.logger.error(`Error starting module ${mod.getName()}`, error);
                this.errorTracker.track(error);
                throw new Error(`Failed to start module ${mod.getName()}: ${error.message}`);
            }
            this.logger.debug(`Module ${mod.getName()} started`);
        }
    }

    async onStop(): Promise<void> {
        this.assertIsInitialized();

        for (const mod of this.onStopModules) {
            this.logger.debug(`Stopping module ${mod.getName()}`);
            try {
                await mod.moduleOnStop();
            } catch (error) {
                this.logger.error(`Error stopping module ${mod.getName()}`, error);
                this.errorTracker.track(error);
                throw new Error(`Failed to stop module ${mod.getName()}: ${error.message}`);
            }
            this.logger.debug(`Module ${mod.getName()} stopped`);
        }
    }

    private initRejectionAndExceptionHandlers(): void {
        process.on('unhandledRejection', (reason) => {
            const error = reason instanceof Error ? reason : new Error(JSON.stringify(reason));
            this.logger.error('Unhandled rejection', error);
            this.errorTracker.track(error);
        });

        process.on('uncaughtException', (error) => {
            this.logger.error('Uncaught exception', error);
            this.errorTracker.track(error);
        });
    }

    private resolveCoreDi(): CoreDi {
        const configurationModule = ConfigurationModule.init(this.configuration);

        const dataSourceModule = new DataSourceModule(this.configuration);
        this.registerOnStart(dataSourceModule);
        this.registerOnStop(dataSourceModule);

        const [transactionManager, requestContextStorage] = setupCoreDependencies(dataSourceModule);
        const loggingModule = LoggingModule.init(this.configuration, requestContextStorage);
        const cronModule = CronModule.init({
            loggingModule,
        });
        this.registerOnStart(cronModule);
        this.registerOnStop(cronModule);

        const errorTrackingModule = ErrorTrackingModule.init();

        /* Assign core dependencies to controller */
        this.errorTracker = errorTrackingModule.errorTracker;
        this.logger = loggingModule.loggingService.createLogger(ApplicationLifeCycleController.name);

        const queueModule = QueueModule.init(this.configuration, loggingModule, errorTrackingModule.errorTracker);
        this.registerOnStart(queueModule);
        this.registerOnStop(queueModule);

        const featureFlagModule = FeatureFlagModule.init(
            this.configuration,
            loggingModule.loggingService,
            requestContextStorage,
        );
        this.registerOnStart(featureFlagModule);

        const applicationEventBrokerModule = ApplicationEventBrokerModule.init({
            configuration: this.configuration,
            loggingService: loggingModule.loggingService,
        });
        this.registerOnStop(applicationEventBrokerModule);

        const messagingModule = MessagingModule.init({
            loggingModule,
        });
        this.registerOnStart(messagingModule);
        this.registerOnStop(messagingModule);

        return {
            configurationModule,
            errorTrackingModule,
            transactionManager,
            requestContextStorage,
            loggingModule,
            queueModule,
            featureFlagModule,
            dataSourceModule,
            cronModule,
            applicationEventBrokerModule,
            messagingModule,
        };
    }

    private resolveDi(): void {
        if (this.isDiResolved) {
            throw new Error('Application DI is already resolved');
        }

        const {
            configurationModule,
            errorTrackingModule,
            transactionManager,
            loggingModule,
            requestContextStorage,
            dataSourceModule,
            queueModule,
            featureFlagModule,
            cronModule,
            applicationEventBrokerModule,
        } = this.resolveCoreDi();

        const configuration = configurationModule.configuration;

        /* autogen: new-module-autogen */
        const dataEncryptionModule = DataEncryptionModule.init({
            configurationModule,
        });

        setupIntercomDependencies(configuration, loggingModule.loggingService);

        const clientUrlModule = ClientUrlModule.init(configuration);

        const analyticsModule = AnalyticsModule.init({ configuration, loggingModule });
        this.registerOnStop(analyticsModule);

        const userRepository = new UserRepository(dataSourceModule.dataSource);

        const reviewSettingsModule = ReviewSettingsModule.init({ transactionManager });
        this.registerControllers(reviewSettingsModule);

        const privacySettingsModule = PrivacySettingsModule.init({ transactionManager, requestContextStorage });
        this.registerControllers(privacySettingsModule);

        const govProfileModule = GovProfileModule.init({ transactionManager, queueModule });

        const holidaysModule = HolidaysModule.init({ loggingModule, transactionManager });
        this.registerControllers(holidaysModule);

        const govAgencyModule = GovAgencyModule.init({
            transactionManager,
        });
        this.registerControllers(govAgencyModule);

        const discordTrackerModule = DiscordTrackerModule.init({ configuration });

        const usersAccountsModule = UsersAccountsModule.init({
            transactionManager,
            loggingModule,
            requestContextStorage,
            userRepository,
            queueModule,
        });

        const profileSetupModule = ProfileSetupModule.init({
            transactionManager,
            usersAccountsModule,
            queueModule,
            analyticsModule,
        });
        this.registerControllers(profileSetupModule);

        const userPermissionsModule = UserPermissionsModule.init({ usersAccountsModule });
        this.registerControllers(userPermissionsModule);

        const userConsentModule = UserConsentModule.init(transactionManager, requestContextStorage);
        this.registerControllers(userConsentModule);

        const googleProfileModule = GoogleProfileModule.init(
            transactionManager,
            loggingModule.loggingService,
            queueModule,
            configuration,
            profileSetupModule,
        );
        this.registerControllers(googleProfileModule);

        const healthModule = HealthModule.init({ usersAccountsModule, errorTracker: errorTrackingModule.errorTracker });
        this.registerControllers(healthModule);

        const { mailer } = setupMailerDependencies(configuration, queueModule);
        const dokobitModule = new DokobitModule(configuration);
        const invoiceModule = InvoiceModule.init({
            transactionManager,
            loggingService: loggingModule.loggingService,
            configuration: configuration.invoice,
        });

        const documentModule = DocumentModule.init({
            configuration,
            loggingModule,
            transactionManager,
            usersAccountsModule,
            dokobitModule,
            invoiceModule,
            queueModule,
        });
        this.registerControllers(documentModule);

        const notificationModule = NotificationModule.init({
            transactionManager,
            analyticsModule,
            loggingModule,
        });
        this.registerControllers(notificationModule);

        const costTrackingAuditModule = CostTrackingAuditModule.init({ transactionManager });
        const costTrackingWorklogModule = CostTrackingWorklogModule.init({
            transactionManager,
            costTrackingAuditModule,
            requestContextStorage,
            queueModule,
            configuration,
            clientUrlModule,
        });
        CostTrackingWorklogEntryModule.init({
            transactionManager,
            costTrackingAuditModule,
            requestContextStorage,
        });
        const costTrackingWorklogEntryModule = CostTrackingWorklogEntryModule.init({
            transactionManager,
            costTrackingAuditModule,
            requestContextStorage,
        });
        const costTrackingProfileModule = CostTrackingProfileModule.init({
            transactionManager,
            costTrackingAuditModule,
            requestContextStorage,
            queueModule,
        });
        const costTrackingAutoTrackingModule = CostTrackingAutoTrackingModule.init({
            costTrackingProfileModule,
            costTrackingWorklogEntryModule,
            costTrackingWorklogModule,
            notificationModule,
            queueModule,
            loggingModule,
            featureFlagModule,
        });

        const costTrackingModule = CostTrackingModule.init({
            costTrackingAuditModule,
            costTrackingProfileModule,
            costTrackingWorklogEntryModule,
            costTrackingWorklogModule,
            transactionManager,
            documentModule,
            usersAccountsModule,
        });
        this.registerControllers(costTrackingModule);

        const usersTagsModule = UsersTagsModule.init({ transactionManager, analyticsModule });
        this.registerControllers(usersTagsModule);

        const notesModule = NotesModule.init({
            loggingService: loggingModule.loggingService,
            transactionManager,
        });
        this.registerControllers(notesModule);

        const physicalAddressModule = PhysicalAddressModule.init({
            configuration,
            loggingModule,
        });
        this.registerControllers(physicalAddressModule);

        const usersStudentsProfileModule = UsersStudentsProfileModule.init({
            loggingModule,
            transactionManager,
            physicalAddressModule,
            requestContextStorage,
        });
        this.registerControllers(usersStudentsProfileModule);

        const timezoneModule = TimezoneModule.init({ configuration, loggingModule, usersStudentsProfileModule });
        this.registerControllers(timezoneModule);

        const discordProfileModule = DiscordProfileModule.init(transactionManager, queueModule);
        const govProgramModule = GovProgramModule.init({ tm: transactionManager, queueModule });
        this.registerControllers(govProgramModule);

        const govGroupModule = GovGroupModule.init({ tm: transactionManager, govAgencyModule, govProgramModule });
        this.registerControllers(govGroupModule);

        const weekScheduleRequirementsModule = WeekScheduleRequirementsModule.init({
            transactionManager,
        });
        this.registerControllers(weekScheduleRequirementsModule);

        const govEducationPlanModule = GovEducationPlanModule.init({
            tm: transactionManager,
        });

        const uztProfileModule = UztProfileModule.init({
            transactionManager,
            loggingModule,
            queueModule,
            requestContextStorage,
            notificationModule,
            configuration,
        });
        this.registerControllers(uztProfileModule);

        const usersStudentsSettingsModule = UsersStudentsSettingsModule.init({
            loggingModule,
            transactionManager,
            requestContextStorage,
            applicationEventBrokerModule,
            analyticsModule,
            uztProfileModule,
            queueModule,
            usersStudentsProfileModule,
            govProfileModule,
            govGroupModule,
        });
        this.registerControllers(usersStudentsSettingsModule);

        const meetingModule = MeetingModule.init({
            configuration,
            loggingModule,
            tm: transactionManager,
            usersAccountsModule,
            usersStudentsSettingsModule,
            applicationEventBrokerModule,
            queueModule,
            cronModule,
        });
        this.registerOnStart(meetingModule);
        this.registerControllers(meetingModule);

        const virtualClassroomModule = VirtualClassroomModule.init(transactionManager);
        const courseModule = CourseModule.init({
            transactionManager,
        });
        this.registerControllers(courseModule);

        const courseDiscordModule = CourseDiscordModule.init({ transactionManager, queueModule });
        const batchDiscordModule = BatchDiscordModule.init({ transactionManager, queueModule });

        const [batchFinder] = setupBatchDependencies(
            transactionManager,
            analyticsModule,
            courseModule,
            usersStudentsSettingsModule,
            queueModule,
            batchDiscordModule,
        );
        const mentorAssignedCourseModule = MentorAssignedCourseModule.init(
            configuration,
            courseModule,
            transactionManager,
            queueModule,
            discordProfileModule,
            loggingModule,
        );

        const discordServerModule = DiscordServerModule.init({ transactionManager });

        const discordModule = DiscordModule.init({
            configuration,
            loggingModule,
            tm: transactionManager,
            batchFinder,
            usersAccountsModule,
            usersStudentsSettingsModule,
            profileSetupModule,
            queueModule,
            mentorAssignedCourseModule,
            discordProfileModule,
            dataEncryptionModule,
            discordServerModule,
        });
        this.registerControllers(discordModule);

        const discordServerRoleModule = DiscordServerRoleModule.init({ transactionManager });
        DiscordServerInfoSyncModule.init({
            discordModule,
            discordServerModule,
            discordServerRoleModule,
            queueModule,
            configuration,
        });

        UserDiscordModule.init({
            courseDiscordModule,
            batchDiscordModule,
            discordServerModule,
            discordServerRoleModule,
            usersStudentsSettingsModule,
            usersAccountsModule,
            mentorAssignedCourseModule,
            discordModule,
            discordProfileModule,
            notificationModule,
            loggingModule,
            queueModule,
            dataEncryptionModule,
        });

        const discordPlatformModule = DiscordPlatformModule.init();
        this.registerControllers(discordPlatformModule);

        const presenceTrackingModule = PresenceTrackingModule.init({
            transactionManager,
            discordTrackerModule,
            discordProfileModule,
            virtualClassroomModule,
            meetingModule,
            configuration,
            queueModule,
            govGroupModule,
            govProgramModule,
            govAgencyModule,
        });
        this.registerControllers(presenceTrackingModule);

        const govAttendanceModule = GovAttendanceModule.init({
            featureFlagModule,
            transactionManager,
            queueModule,
            presenceTrackingModule,
        });

        const govParticipationAggregationModule = GovParticipationAggregationModule.init({
            govAttendanceModule,
        });

        const govLearnersModule = GovLearnersModule.init({ tm: transactionManager });

        const weekScheduleConsentModule = WeekScheduleConsentModule.init({
            transactionManager,
            userConsentModule,
            notificationModule,
            usersStudentsSettingsModule,
        });
        this.registerControllers(weekScheduleConsentModule);

        const govAggregatedEducationPlanModule = GovAggregatedEducationPlanModule.init({
            govGroupModule,
            govProgramModule,
            holidaysModule,
            usersStudentsProfileModule,
            govAgencyModule,
            weekScheduleRequirementsModule,
            govEducationPlanModule,
        });
        this.registerControllers(govAggregatedEducationPlanModule);

        const govGroupEducationPlanModule = GovGroupEducationPlanModule.init({
            transactionManager,
            govGroupModule,
            govProgramModule,
            govAgencyModule,
            holidaysModule,
            govEducationPlanModule,
            queueModule,
        });
        this.registerControllers(govGroupEducationPlanModule);

        const weekScheduleModule = WeekScheduleModule.init({
            loggingModule,
            transactionManager,
            weekScheduleConsentModule,
            queueModule,
            govAggregatedEducationPlanModule,
            usersStudentsSettingsModule,
        });
        this.registerControllers(weekScheduleModule);

        const weekScheduleGroupsModule = WeekScheduleGroupsModule.init({
            weekScheduleRequirementsModule,
            weekScheduleModule,
            govGroupModule,
            featureFlagModule,
            govAggregatedEducationPlanModule,
        });
        this.registerControllers(weekScheduleGroupsModule);

        InvalidWeekScheduleNotificationModule.init({
            queueModule,
            govLearnersModule,
            notificationModule,
            weekScheduleGroupsModule,
            configuration,
        });

        DefaultWeekScheduleModule.init({
            featureFlagModule,
            usersAccountsModule,
            queueModule,
            loggingModule,
            weekScheduleModule,
            govAggregatedEducationPlanModule,
        });

        const govParticipationModule = GovParticipationModule.init({
            tm: transactionManager,
            queueModule,
            govAggregatedEducationPlanModule,
        });
        this.registerControllers(govParticipationModule);

        const uztParticipationModule = UztParticipationModule.init({
            govParticipationModule,
        });

        const govWeekParticipationModule = GovWeekParticipationModule.init({
            govAttendanceModule,
            weekScheduleModule,
            govParticipationAggregationModule,
            govAggregatedEducationPlanModule,
            govParticipationModule,
        });
        this.registerControllers(govWeekParticipationModule);

        const uztChapterModule = UztChapterModule.init({ tm: transactionManager });
        this.registerControllers(uztChapterModule);

        const uztMonthlyInfoModule = UztMonthlyInfoModule.init({
            usersStudentsProfileModule,
            usersAccountsModule,
            uztChapterModule,
            govGroupModule,
            govParticipationModule,
            uztProfileModule,
            govProgramModule,
            weekScheduleModule,
            govProfileModule,
        });

        const uztReportsModule = UztReportsModule.init({
            uztMonthlyInfoModule,
            usersAccountsModule,
            govGroupModule,
            govProgramModule,
        });
        this.registerControllers(uztReportsModule);

        const uztDocumentModule = UztDocumentModule.init({
            loggingModule,
            transactionManager,
            mailer,
            configuration,
            usersAccountsModule,
            documentModule,
            invoiceModule,
            govProgramModule,
            govGroupModule,
            uztProfileModule,
            uztChapterModule,
            uztMonthlyInfoModule,
            weekScheduleConsentModule,
            weekScheduleModule,
            usersStudentsProfileModule,
            queueModule,
            usersStudentsSettingsModule,
            featureFlagModule,
            govProfileModule,
        });
        this.registerControllers(uztDocumentModule);

        const uztAlertsModule = UztAlertsModule.init({
            govParticipationModule,
            queueModule,
            loggingModule,
            configuration,
            govLearnersModule,
            govGroupModule,
            usersAccountsModule,
            discordModule,
            transactionManager,
            uztParticipationModule,
        });
        this.registerControllers(uztAlertsModule);

        GovParticipationDayAutoAggregationModule.init({
            govLearnersModule,
            govParticipationModule,
            weekScheduleModule,
            govAttendanceModule,
            queueModule,
            loggingModule,
            configuration,
            govAggregatedEducationPlanModule,
            govParticipationAggregationModule,
        });

        UztNotificationModule.init({
            queueModule,
            loggingModule,
            notificationModule,
            usersStudentsSettingsModule,
            transactionManager,
        });

        const uztProofOfCompletionModule = UztProofOfCompletionModule.init({
            configuration,
            queueModule,
            loggingModule,
            uztProfileModule,
            govGroupModule,
            mailer,
            usersAccountsModule,
            documentModule,
            uztDocumentModule,
            usersStudentsSettingsModule,
            timezoneModule,
        });
        this.registerControllers(uztProofOfCompletionModule);

        const calendarSettingsModule = CalendarSettingsModule.init(transactionManager, requestContextStorage);
        this.registerControllers(calendarSettingsModule);

        const quizQuestionModule = QuizQuestionModule.init({
            transactionManager,
        });
        this.registerControllers(quizQuestionModule);

        const reviewQuestionsModule = ReviewQuestionsModule.init({
            transactionManager,
        });
        this.registerControllers(reviewQuestionsModule);

        const sprintsModule = SprintsModule.init({
            configuration,
            transactionManager,
            queueModule,
            quizQuestionModule,
            reviewQuestionsModule,
        });
        this.registerControllers(sprintsModule);

        const modulesModule = ModulesModule.init({
            tm: transactionManager,
            sprintsModule,
        });
        this.registerControllers(modulesModule);

        const githubModule = GithubModule.init({
            configuration,
            loggingModule,
            transactionManager,
            queueModule,
            profileSetupModule,
        });
        this.registerControllers(githubModule);

        const [announcer] = setupAnnouncementDependencies(transactionManager, discordModule);
        const learningStandupAttendanceRequirementsModule = LearningStandupAttendanceRequirementsModule.init({
            transactionManager,
        });

        const userConnectionsModule = UserConnectionsModule.init(
            githubModule,
            googleProfileModule,
            discordModule,
            loggingModule,
            sprintsModule,
        );
        this.registerControllers(userConnectionsModule);

        const learningQuizModule = LearningQuizModule.init(
            loggingModule.loggingService,
            transactionManager,
            quizQuestionModule,
        );
        const usersStudentsTermsModule = UsersStudentsTermsModule.init({ loggingModule, transactionManager });
        const calendarModule = CalendarModule.init({
            tm: transactionManager,
            meetingModule,
            usersStudentsSettingsModule,
            usersAccountsModule,
            batchFinder,
            courseModule,
            requestContextStorage,
            cronModule,
            configuration,
            discordModule,
            loggingModule,
        });
        this.registerOnStart(calendarModule);

        const passwordRepository = new PasswordRepository(transactionManager);

        const emailAvailabilityChecker = new EmailAvailabilityChecker(userRepository);

        const signupModule = SignupModule.init();
        this.registerControllers(signupModule);

        const learningMilestonesModule = LearningMilestonesModule.init({
            configuration,
            loggingModule,
            transactionManager,
            learningStandupAttendanceRequirementsModule,
            calendarModule,
            notificationModule,
            mailer,
            usersAccountsModule,
            meetingModule,
            cronModule,
        });
        this.registerOnStart(learningMilestonesModule);
        this.registerControllers(learningMilestonesModule);

        const learningRoadmapModule = LearningRoadmapModule.init({
            configuration,
            loggingModule,
            transactionManager,
            usersAccountsModule,
            courseModule,
            modulesModule,
            sprintsModule,
            usersStudentsSettingsModule,
            githubModule,
            discordModule,
            announcer,
            analyticsModule,
            learningMilestonesModule,
            googleProfileModule,
            queueModule,
            applicationEventBrokerModule,
        });
        this.registerControllers(learningRoadmapModule);

        const timeslotModule = TimeslotModule.init(dataSourceModule, configuration, analyticsModule);
        this.registerControllers(timeslotModule);

        const correctionsModule = CorrectionsModule.init({
            transactionManager,
            sprintsModule,
            reviewQuestionsModule,
            timeslotModule,
            calendarSettingsModule,
            configuration,
            loggingModule,
            usersAccountsModule,
            usersStudentsProfileModule,
            learningRoadmapModule,
            calendarModule,
            discordModule,
            analyticsModule,
            mailer,
            notesModule,
            userRepository,
            dataSourceModule,
            costTrackingAutoTrackingModule,
            timezoneModule,
            reviewSettingsModule,
            queueModule,
            notificationModule,
            usersStudentsSettingsModule,
        });

        learningRoadmapModule.registerWorkers(
            correctionsModule,
            usersAccountsModule,
            notificationModule,
            sprintsModule.sprintPartReviewerPreconditionsService,
        );

        const learningPath = new LearningPath(learningRoadmapModule.roadmapManager);

        const authModule = AuthModule.init(
            configuration,
            loggingModule,
            dataSourceModule,
            userRepository,
            requestContextStorage,
            passwordRepository,
            applicationEventBrokerModule,
            transactionManager,
            mailer,
            analyticsModule,
        );
        this.registerControllers(authModule);

        const mentorModule = MentorModule.init({
            transactionManager,
            loggingModule,
            correctionsModule,
            timeslotModule,
            analyticsModule,
            githubModule,
            emailAvailabilityChecker,
            authModule,
            calendarModule,
            dataSourceModule,
            physicalAddressModule,
            mentorAssignedCourseModule,
            costTrackingProfileModule,
            usersAccountsModule,
            reviewSettingsModule,
            privacySettingsModule,
            notificationModule,
        });
        this.registerControllers(mentorModule);

        const onboardingModule = OnboardingModule.init({
            transactionManager,
            loggingModule,
            queueModule,
            usersAccountsModule,
            batchFinder,
            usersStudentsSettingsModule,
            profileSetupModule,
            analyticsModule,
        });
        this.registerControllers(onboardingModule);

        const studentManagementModule = StudentsManagementModule.init({
            configuration,
            queueModule,
            notificationModule,
            transactionManager,
            loggingModule,
            correctionsModule,
            timeslotModule,
            discordModule,
            usersAccountsModule,
            applicationEventBrokerModule,
            emailAvailabilityChecker,
            authModule,
            calendarModule,
            githubModule,
            usersTagsModule,
            profileSetupModule,
            learningMilestonesModule,
            dataSourceModule,
            analyticsModule,
        });
        this.registerControllers(studentManagementModule);

        AnalyticsPlatformModule.init({ studentManagementModule, analyticsModule, batchFinder });

        const studentSignupModule = StudentSignupModule.init({
            configuration,
            transactionManager,
            studentManagementModule,
            usersAccountsModule,
            usersStudentsSettingsModule,
            usersStudentsProfileModule,
            calendarSettingsModule,
            usersTagsModule,
            mailer,
            analyticsModule,
            passwordRepository,
            batchFinder,
            usersStudentsTermsModule,
            learningPath,
            profileSetupModule,
            googleProfileModule,
            emailAvailabilityChecker,
            onboardingModule,
            notificationModule,
            govProfileModule,
            uztProfileModule,
            govGroupModule,
            reviewSettingsModule,
            privacySettingsModule,
            batchDiscordModule,
        });

        const staffModule = StaffModule.init({
            loggingModule,
            transactionManager,
            usersAccountsModule,
            emailAvailabilityChecker,
            authModule,
            calendarModule,
            dataSourceModule,
        });
        this.registerControllers(staffModule);

        const icsService = new DefaultIcsService(
            userRepository,
            calendarModule.calendarEventScheduler,
            analyticsModule.analytics,
        );

        Container.set(UserRepositoryToken, userRepository);

        Container.set(IcsServiceToken, icsService);
        Container.set(LearningPath, learningPath);
        Container.set(EmailAvailabilityCheckerToken, emailAvailabilityChecker);

        Container.set(
            MentorSignupStarterToken,
            new MentorSignupStarter(
                configuration,
                transactionManager,
                mentorModule.mentorRepository,
                usersAccountsModule.userAccountManager,
                mentorModule.mentorProfileManager,
                calendarSettingsModule.calendarSettingsService,
                mailer,
                analyticsModule.analytics,
                mentorAssignedCourseModule.mentorAssignedCourseService,
                costTrackingProfileModule.costTrackingProfileService,
                emailAvailabilityChecker,
                reviewSettingsModule.reviewSettingsService,
                privacySettingsModule.privacySettingsService,
                notificationModule.notificationManager,
            ),
        );
        Container.set(
            StaffSignupStarterToken,
            new StaffSignupStarter(
                configuration,
                transactionManager,
                staffModule.staffRepository,
                usersAccountsModule.userAccountManager,
                emailAvailabilityChecker,
                mailer,
            ),
        );
        Container.set(
            SignupFinisherToken,
            new SignupFinisher(
                transactionManager,
                userRepository,
                usersStudentsSettingsModule.studentSettingsFinder,
                applicationEventBrokerModule.applicationEventBroker,
                new SignupIntegrationEventsPublisher(
                    configuration,
                    applicationEventBrokerModule.integrationEventPublisher,
                ),
                queueModule.queueService,
                new Map<Role, SignupFinisherStrategy>([
                    [Role.USER, studentSignupModule.studentSignupFinishStrategy],
                    [
                        Role.MENTOR,
                        new MentorSignupFinishStrategy(
                            mentorModule.mentorRepository,
                            passwordRepository,
                            usersAccountsModule.userAccountManager,
                            usersAccountsModule.usernameGenerator,
                            analyticsModule.analytics,
                            profileSetupModule.profileSetupStepService,
                            mentorAssignedCourseModule.mentorAssignedCourseService,
                            courseModule.courseFinder,
                            googleProfileModule.googleProfileService,
                            courseDiscordModule.courseDiscordServerService,
                        ),
                    ],
                    [
                        Role.STAFF,
                        new StaffSignupFinishStrategy(
                            staffModule.staffRepository,
                            passwordRepository,
                            usersAccountsModule.userAccountManager,
                            usersAccountsModule.usernameGenerator,
                        ),
                    ],
                    [
                        Role.ADMIN,
                        new StaffSignupFinishStrategy(
                            staffModule.staffRepository,
                            passwordRepository,
                            usersAccountsModule.userAccountManager,
                            usersAccountsModule.usernameGenerator,
                        ),
                    ],
                ]),
            ),
        );

        // Register SignupCompletedEventWorker for immediate schedule creation
        queueModule.queueWorkerRegistry.registerWorker(new SignupCompletedEventWorker(queueModule.queueService));

        const learningEvaluationModule = LearningEvaluationModule.init({
            loggingModule,
            transactionManager,
            learningRoadmapModule,
            learningQuizModule,
            analyticsModule,
            correctionsModule,
            calendarModule,
            usersAccountsModule,
            cronModule,
            configuration,
        });
        this.registerOnStart(learningEvaluationModule);
        this.registerControllers(learningEvaluationModule);

        const learningDeadlinesModule = LearningDeadlinesModule.init({
            configuration,
            loggingModule,
            transactionManager,
            requestContextStorage,
            learningRoadmapModule,
            modulesModule,
            usersAccountsModule,
            usersStudentsSettingsModule,
            correctionsModule,
            notificationModule,
            discordModule,
            mailer,
            timezoneModule,
            cronModule,
        });
        this.registerOnStart(learningDeadlinesModule);
        this.registerControllers(learningDeadlinesModule);

        const endorsementModule = EndorsementModule.init({
            configuration,
            loggingModule,
            transactionManager,
            applicationEventBrokerModule,
            studentManagementModule,
            usersStudentsSettingsModule,
            batchFinder,
            learningRoadmapModule,
            notificationModule,
            usersAccountsModule,
            announcer,
            analyticsModule,
        });
        this.registerControllers(endorsementModule);

        const resumeModule = ResumeModule.init({
            loggingModule,
            transactionManager,
            endorsementModule,
            learningRoadmapModule,
            correctionsModule,
            usersAccountsModule,
            batchFinder,
            usersStudentsSettingsModule,
            mentorModule,
        });
        this.registerControllers(resumeModule);

        const countriesModule = CountriesModule.init({
            configuration,
            loggingModule,
        });
        this.registerControllers(countriesModule);

        const versionModule = VersionModule.init();
        this.registerControllers(versionModule);

        const apiSchemaModule = ApiSchemaModule.init();
        this.registerControllers(apiSchemaModule);

        useContainer(Container);

        /* Must be registered last so that all modules can register controllers before */
        const expressApplicationModule = ExpressApplicationModule.init({
            configuration,
            loggingModule,
            authModule,
            errorTrackingModule,
            controllers: [
                /* TODO: move generic controllers to corresponding modules */
                ...controllers,
                ...this.controllers,
            ],
        });
        this.registerOnStart(expressApplicationModule);
        this.registerOnStop(expressApplicationModule);

        this.isInitialized = true;
    }

    private registerOnStart(mod: ModuleOnStart): void {
        if (!('moduleOnStart' in mod)) {
            throw new Error('Module does not implement ModuleOnStart interface');
        }

        this.onStartModules.push(mod);
        this.onStartModules.sort((a, b) => a.getPriority() - b.getPriority());
    }

    private registerOnStop(mod: ModuleOnStop): void {
        if (!('moduleOnStop' in mod)) {
            throw new Error('Module does not implement ModuleOnStop interface');
        }

        this.onStopModules.unshift(mod);
        this.onStopModules.sort((a, b) => b.getPriority() - a.getPriority());
    }

    private registerControllers(mod: ModuleWithControllers): void {
        if (!('getControllers' in mod)) {
            throw new Error('Module does not implement ModuleWithControllers interface');
        }

        this.controllers.push(...mod.getControllers());
    }

    private assertIsInitialized(): void {
        if (!this.isInitialized) {
            throw new Error('ApplicationDi is not set up yet');
        }
    }
}
