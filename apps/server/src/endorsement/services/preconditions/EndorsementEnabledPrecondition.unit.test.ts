import moment from 'moment';
import Batch from '../../../batches/domain/Batch';
import BatchName from '../../../batches/domain/BatchName';
import Id from '../../../core/domain/value-objects/Id';
import Course from '../../../education/courses/domain/Course';
import Endorsement from '../../domain/Endorsement';
import HiringProfile from '../../domain/hiring-profile/HiringProfile';
import HRInterviewEndorsement from '../../domain/interview/HRInterviewEndorsement';
import Interview from '../../domain/interview/Interview';
import TechInterviewEndorsement from '../../domain/interview/TechInterviewEndorsement';
import GithubProfileEndorsement from '../../domain/social-media/GithubProfileEndorsement';
import LinkedInProfileEndorsement from '../../domain/social-media/LinkedInProfileEndorsement';
import SocialMedia from '../../domain/social-media/SocialMedia';
import PendingStageState from '../../domain/state/PendingStageState';
import EndorsementEnabledPrecondition from './EndorsementEnabledPrecondition';
import StudentSettings from '../../../users/students/settings/domain/StudentSettings';
import BatchFinderMock from '../../../test-toolkit/unit/deprecated-mocks/BatchFinderMock';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import { faker } from '@faker-js/faker';
import { CourseOnboardingConnection } from '../../../education/courses/domain/CourseOnboardingConnection';
import { StudentSettingsFinder } from '../../../users/students/settings/services/StudentSettingsFinder';
import { anything, instance, mock, when } from 'ts-mockito';
import { getTestStudentSettings } from '../../../test-toolkit/shared/studentSettingsHelper';

describe(EndorsementEnabledPrecondition.name, () => {
    const studentProfileFinder = mock(StudentSettingsFinder);
    const batchFinder = new BatchFinderMock();

    describe('isSatisfied', () => {
        async function testIsSatisfied(
            userId: Id,
            studentProfile: StudentSettings | undefined,
            batch: Batch | undefined,
            expectedResult: boolean,
        ): Promise<void> {
            when(studentProfileFinder.findByUserId(anything(), anything())).thenResolve(studentProfile);
            batchFinder.findByIdMock.mockResolvedValueOnce(batch);

            const endorsement = new Endorsement(
                userId,
                new HiringProfile(userId, [new PendingStageState()], []),
                new SocialMedia(
                    userId,
                    [new PendingStageState()],
                    [],
                    new GithubProfileEndorsement(),
                    new LinkedInProfileEndorsement(),
                ),
                new Interview(
                    userId,
                    [new PendingStageState()],
                    [],
                    new HRInterviewEndorsement(),
                    new TechInterviewEndorsement(),
                ),
            );
            const precondition = new EndorsementEnabledPrecondition(instance(studentProfileFinder), batchFinder);

            const isSatisfied = await precondition.isSatisfied(endorsement);
            expect(isSatisfied).toBe(expectedResult);
        }

        test('should return false if student profile is not found', async () => {
            await testIsSatisfied(TestObjects.uniqueId(), undefined, undefined, false);
        });

        test('should return false if batch is not found', async () => {
            const userId = TestObjects.uniqueId();
            const profile = getTestStudentSettings({ studentId: userId, isDeadlineMandatory: false });

            await testIsSatisfied(userId, profile, undefined, false);
        });

        test('should return false if endorsement is not enabled', async () => {
            const userId = TestObjects.uniqueId();
            const profile = getTestStudentSettings({ studentId: userId, isDeadlineMandatory: false });
            const batch = new Batch({
                course: new Course({
                    name: 'test',
                    isEndorsementEnabled: false,
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
                name: new BatchName('test'),
                startDate: new Date(),
                endDate: moment().add(1, 'year').toDate(),
            });

            await testIsSatisfied(userId, profile, batch, false);
        });

        test('should return true if endorsement is enabled', async () => {
            const userId = TestObjects.uniqueId();
            const profile = getTestStudentSettings({ studentId: userId, isDeadlineMandatory: false });
            const batch = new Batch({
                course: new Course({
                    name: 'test',
                    isEndorsementEnabled: true,
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
                name: new BatchName('test'),
                startDate: new Date(),
                endDate: moment().add(1, 'year').toDate(),
            });

            await testIsSatisfied(userId, profile, batch, true);
        });
    });
});
