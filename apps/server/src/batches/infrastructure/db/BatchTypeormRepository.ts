import { ILike, In } from 'typeorm';
import Id from '../../../core/domain/value-objects/Id';
import TypeormRepository from '../../../core/infrastructure/db/TypeormRepository';
import Transaction from '../../../core/infrastructure/Transaction';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import Batch from '../../domain/Batch';
import BatchName from '../../domain/BatchName';
import BatchRepository from '../../domain/BatchRepository';
import BatchMapper from './BatchMapper';
import BatchPersistenceEntity from './BatchPersistenceEntity';
import { QueriedBatch } from '../../domain/QueriedBatch';
import StudentSettingsPersistenceEntity from '../../../users/students/settings/db/StudentSettingsPersistenceEntity';
import { BatchDiscordServerRolePersistenceEntity } from '../../../discord-platform/submodules/batch-discord/infrastructure/db/batch-discord-server-role/BatchDiscordServerRolePersistenceEntity';
import { BatchDiscordServerPersistenceEntity } from '../../../discord-platform/submodules/batch-discord/infrastructure/db/batch-discord-server/BatchDiscordServerPersistenceEntity';
import CoursePersistenceEntity from '../../../education/courses/infrastructure/db/CoursePersistenceEntity';
import Course from '../../../education/courses/domain/Course';
import { BatchDiscordServerWithRoles } from '../../../discord-platform/submodules/batch-discord/domain/BatchDiscordServerWithRoles';
import { BatchDiscordServerRole } from '../../../discord-platform/submodules/batch-discord/domain/BatchDiscordServerRole';
import { BatchDiscordServerRoleType } from '../../../discord-platform/submodules/batch-discord/domain/BatchDiscordServerRoleType';

export default class BatchTypeormRepository
    extends TypeormRepository<Batch, BatchPersistenceEntity>
    implements BatchRepository
{
    constructor(transactionManager: TransactionManager) {
        super(transactionManager, new BatchMapper(), BatchPersistenceEntity, ['course', 'mentors']);
    }

    async getAllByIds(ids: Id[], transaction?: Transaction): Promise<Batch[]> {
        return await this.getAllWhere({ id: In(ids.map((id) => id.value)) }, transaction);
    }

    async getByName(name: BatchName, transaction?: Transaction): Promise<Batch | undefined> {
        return await this.getWhere({ name: ILike(name.value) }, transaction);
    }

    async getByCourseId(courseId: Id, transaction?: Transaction): Promise<Batch[]> {
        return await this.getAllWhere({ courseId: courseId.value }, transaction);
    }

    async queryBatches(): Promise<QueriedBatch[]> {
        return this.transactionManager.execute(async (tx) => {
            const userCountSubQuery = tx.entityManager
                .createQueryBuilder()
                .select('COUNT(student_settings.id)', 'count')
                .addSelect('student_settings.batch_id', 'batch_id')
                .from(StudentSettingsPersistenceEntity, 'student_settings')
                .groupBy('student_settings.batch_id');

            const discordRolesSubQuery = tx.entityManager
                .createQueryBuilder()
                .select('batch_discord_server_role.batch_id', 'batch_id')
                .addSelect(
                    "ARRAY_AGG(JSON_BUILD_OBJECT('id', batch_discord_server_role.discord_server_role_id, 'type', batch_discord_server_role.type))",
                    'discord_server_roles',
                )
                .from(BatchDiscordServerRolePersistenceEntity, 'batch_discord_server_role')
                .groupBy('batch_discord_server_role.batch_id');

            const discordServerSubQuery = tx.entityManager
                .createQueryBuilder()
                .select('batch_discord_server.batch_id', 'batch_id')
                .addSelect('batch_discord_server.discord_server_id', 'discord_server_id')
                .from(BatchDiscordServerPersistenceEntity, 'batch_discord_server');

            const result = await tx.entityManager
                .createQueryBuilder()
                .select('*')
                .addSelect('batch.id', 'main_batch_id')
                .addSelect('course.name', 'course_name')
                .addSelect('batch.name', 'batch_name')
                .addSelect('user_count.count', 'user_count')
                .addSelect('discord_roles.discord_server_roles', 'discord_server_roles')
                .addSelect('discord_server.discord_server_id', 'discord_server_id')
                .from(BatchPersistenceEntity, 'batch')
                .leftJoinAndSelect(`(${userCountSubQuery.getQuery()})`, 'user_count', 'user_count.batch_id = batch.id')
                .leftJoin(`(${discordRolesSubQuery.getQuery()})`, 'discord_roles', 'discord_roles.batch_id = batch.id')
                .leftJoin(
                    `(${discordServerSubQuery.getQuery()})`,
                    'discord_server',
                    'discord_server.batch_id = batch.id',
                )
                .innerJoin(CoursePersistenceEntity, 'course', 'batch.course_id = course.id')
                .getRawMany();

            return result.map((row) => {
                const batchId = new Id(row['main_batch_id']);

                return QueriedBatch.fromParams({
                    course: new Course(
                        {
                            name: row['course_name'],
                            isEndorsementEnabled: row['is_endorsement_enabled'],
                            studentDiscordRoleId: row['student_discord_role_id'],
                            mentorDiscordRoleId: row['mentor_discord_role_id'],
                            onboardingConnections: row['onboarding_connections'],
                        },
                        new Id(row['course_id']),
                    ),
                    discordServer: row['discord_server_id']
                        ? BatchDiscordServerWithRoles.fromParams({
                              discordServerId: new Id(row['discord_server_id']),
                              batchId,
                              roles: row['discord_server_roles'].map(
                                  (role: { id: number; type: BatchDiscordServerRoleType }) => {
                                      return BatchDiscordServerRole.fromParams({
                                          batchId,
                                          discordServerRoleId: new Id(role.id),
                                          type: role.type,
                                      });
                                  },
                              ),
                          })
                        : undefined,
                    endDate: new Date(row['end_date']),
                    id: batchId,
                    mentors: [],
                    name: new BatchName(row['batch_name']),
                    startDate: new Date(row['start_date']),
                    studentsCount: +row['user_count'] || 0,
                });
            });
        });
    }
}
