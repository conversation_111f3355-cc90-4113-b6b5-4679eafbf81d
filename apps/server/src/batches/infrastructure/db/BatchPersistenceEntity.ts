import { Column, Entity, ManyTo<PERSON>ne, OneToMany, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../core/infrastructure/db/TypeormPersistenceEntity';
import CoursePersistenceEntity from '../../../education/courses/infrastructure/db/CoursePersistenceEntity';
import BatchMentorPersistenceEntity from './BatchMentorPersistenceEntity';

@Entity('batch')
export default class BatchPersistenceEntity extends TypeormPersistenceEntity {
    @Column({ unique: true })
    name: string;

    @Column({ type: 'timestamptz' })
    startDate: Date;

    @Column({ type: 'timestamptz' })
    endDate: Date;

    @Column({ nullable: true })
    discordRoleId?: string;

    @Column()
    @RelationId((batch: BatchPersistenceEntity) => batch.course)
    courseId: number;

    @ManyToOne(() => CoursePersistenceEntity, { eager: true })
    course: CoursePersistenceEntity;

    @OneToMany(() => BatchMentorPersistenceEntity, 'batch', {
        eager: true,
        cascade: true,
    })
    mentors: BatchMentorPersistenceEntity[];

    constructor(params?: Partial<Omit<BatchPersistenceEntity, 'mentors' | 'course'> & { mentors: number[] }>) {
        super(params?.id, params?.createdAt, params?.updatedAt);

        if (params) {
            Object.assign(this, params);
            if (params.mentors) {
                this.mentors = params.mentors?.map(
                    (userId) =>
                        new BatchMentorPersistenceEntity({
                            userId: userId,
                            batchId: this.id,
                        }),
                );
            }
        }
    }
}

/**
 * @deprecated
 */
export function isBatch(batch: BatchPersistenceEntity): batch is BatchPersistenceEntity {
    return 'id' in batch && 'name' in batch;
}
