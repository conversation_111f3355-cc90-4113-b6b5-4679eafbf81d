import Id from '../../../core/domain/value-objects/Id';
import TypeormPersistenceMapper from '../../../core/infrastructure/db/TypeormPersistenceMapper';
import Batch from '../../domain/Batch';
import BatchName from '../../domain/BatchName';
import BatchPersistenceEntity from './BatchPersistenceEntity';
import CourseMapper from '../../../education/courses/infrastructure/db/CourseMapper';

export default class BatchMapper implements TypeormPersistenceMapper<Batch, BatchPersistenceEntity> {
    private readonly courseMapper = new CourseMapper();

    toDomain(entity: BatchPersistenceEntity): Batch {
        return new Batch({
            course: this.courseMapper.toDomain(entity.course),
            name: new BatchName(entity.name),
            startDate: entity.startDate,
            endDate: entity.endDate,
            mentors: entity.mentors.map((mentor) => new Id(mentor.userId)),
            id: entity.id ? new Id(entity.id) : undefined,
        });
    }

    toPersistence(batch: Batch): BatchPersistenceEntity {
        return new BatchPersistenceEntity({
            name: batch.name.value,
            startDate: batch.startDate,
            endDate: batch.endDate,
            courseId: batch.course.id?.value,
            mentors: batch.mentors.map((userId) => userId.value),
            id: batch.id?.value,
        });
    }
}
