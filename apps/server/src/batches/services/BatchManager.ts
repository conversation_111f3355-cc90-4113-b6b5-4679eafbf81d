import Id from '../../core/domain/value-objects/Id';
import IllegalArgumentError from '../../core/errors/IllegalArgumentError';
import Transaction from '../../core/infrastructure/Transaction';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import StateValidation from '../../core/utils/validation/StateValidation';
import CourseFinder from '../../education/courses/services/CourseFinder';
import { StudentSettingsFinder } from '../../users/students/settings/services/StudentSettingsFinder';
import Batch from '../domain/Batch';
import BatchRepository from '../domain/BatchRepository';
import BatchAnalytics from './BatchAnalytics';
import BatchCreateData from './dto/BatchCreateData';
import BatchUpdateData from './dto/BatchUpdateData';
import { QueueService } from '../../queue/QueueService';
import { BatchStartedEvent } from '../controllers/events/dto/BatchStartedEvent';
import { AppQueues } from '../../queue/domain/AppQueues';
import { BatchStartDateChanged } from '../domain/BatchDomainEvents';
import { BatchDiscordService } from '../../discord-platform/submodules/batch-discord/services/BatchDiscordService';

interface BatchManager {
    create(data: BatchCreateData, transaction?: Transaction): Promise<Batch>;

    update(id: Id, data: BatchUpdateData, transaction?: Transaction): Promise<Batch | undefined>;

    delete(id: Id, transaction?: Transaction): Promise<void>;
}

export default BatchManager;

export class DefaultBatchManager implements BatchManager {
    constructor(
        private readonly transactionManager: TransactionManager,
        private readonly batchRepository: BatchRepository,
        private readonly courseFinder: CourseFinder,
        private readonly studentSettingsFinder: StudentSettingsFinder,
        private readonly batchAnalytics: BatchAnalytics,
        private readonly queueService: QueueService,
        private readonly batchDiscordService: BatchDiscordService,
    ) {}

    async create(data: BatchCreateData, existingTransaction?: Transaction): Promise<Batch> {
        return await this.transactionManager.execute(async (transaction) => {
            const course = await this.courseFinder.findById(data.courseId);
            if (!course) {
                throw new IllegalArgumentError('Course does not exist');
            }

            const existingWithName = await this.batchRepository.getByName(data.name, transaction);
            if (existingWithName) {
                throw new IllegalArgumentError('Batch name is already in use');
            }

            const batch = await this.batchRepository.save(
                new Batch({
                    course,
                    name: data.name,
                    startDate: data.startDate,
                    endDate: data.endDate,
                }),
                transaction,
            );

            await this.batchDiscordService.setServerAndRolesForBatch({
                batchId: batch.getIdOrThrow(),
                batchDiscordServer: data.discordServer?.toDomain(batch.getIdOrThrow()),
                tx: transaction,
            });

            transaction.addBeforeCommitAction(async () => {
                await this.queueService.publish({
                    event: new BatchStartedEvent({
                        batchId: batch.getIdOrThrow(),
                    }),
                    options: {
                        startAfter: batch.startDate,
                        subQueue: batch.getIdOrThrow().toString(),
                    },
                });
            });

            return batch;
        }, existingTransaction);
    }

    async update(id: Id, data: BatchUpdateData, existingTransaction?: Transaction): Promise<Batch | undefined> {
        return await this.transactionManager.execute(async (transaction) => {
            const existingWithName = await this.batchRepository.getByName(data.name, transaction);
            if (existingWithName && !existingWithName.getIdOrThrow().equals(id)) {
                throw new IllegalArgumentError('Batch name is already in use');
            }

            const batch = await this.batchRepository.get(id, transaction);
            if (!batch) {
                return undefined;
            }

            if (!batch.name.equals(data.name)) {
                await this.batchAnalytics.updateBatchName(batch.getIdOrThrow(), data.name, transaction);
            }

            const updatedBatch = batch
                .rename(data.name)
                .changeDates(data.startDate || batch.startDate, data.endDate || batch.endDate);

            if (
                updatedBatch.getDomainEvents().some((e) => e instanceof BatchStartDateChanged) &&
                // If batch already started, we will not reschedule event
                !batch.isStarted()
            ) {
                transaction.addBeforeCommitAction(async () => {
                    await this.queueService.deleteQueue(
                        AppQueues.schema.batch.started.subQueue(updatedBatch.getIdOrThrow().toString()),
                    );
                    await this.queueService.publish({
                        event: new BatchStartedEvent({
                            batchId: updatedBatch.getIdOrThrow(),
                        }),
                        options: {
                            startAfter: updatedBatch.startDate,
                            subQueue: updatedBatch.getIdOrThrow().toString(),
                        },
                    });
                });
            }

            await this.batchDiscordService.setServerAndRolesForBatch({
                batchId: batch.getIdOrThrow(),
                batchDiscordServer: data.discordServer?.toDomain(batch.getIdOrThrow()),
                tx: transaction,
            });

            return this.batchRepository.save(updatedBatch, transaction);
        }, existingTransaction);
    }

    async delete(id: Id, existingTransaction?: Transaction): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            const studentsCount = await this.studentSettingsFinder.numberOfStudentsInBatch(id, transaction);
            StateValidation.isValid(studentsCount === 0, 'Batch cannot be deleted because it has students');

            await this.batchRepository.delete(id, transaction);
        }, existingTransaction);
    }
}
