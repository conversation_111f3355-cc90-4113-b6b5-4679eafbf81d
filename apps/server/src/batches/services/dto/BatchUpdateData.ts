import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import BatchName from '../../domain/BatchName';
import { BatchLinkedDiscordServerDto } from '../../controllers/http/dto/BatchLinkedDiscordServerDto';

export default class BatchUpdateData {
    readonly name: BatchName;

    readonly startDate?: Date;

    readonly endDate?: Date;

    readonly discordServer?: BatchLinkedDiscordServerDto;

    constructor(name: BatchName, startDate?: Date, endDate?: Date, discordServer?: BatchLinkedDiscordServerDto) {
        ArgumentValidation.assert.defined(name, 'Batch name is required');

        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
        this.discordServer = discordServer;
    }
}
