import Id from '../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import BatchName from '../../domain/BatchName';
import { BatchLinkedDiscordServerDto } from '../../controllers/http/dto/BatchLinkedDiscordServerDto';

export default class BatchCreateData {
    readonly courseId: Id;

    readonly name: BatchName;

    readonly startDate: Date;

    readonly endDate: Date;

    readonly discordServer?: BatchLinkedDiscordServerDto;

    constructor(
        courseId: Id,
        name: BatchName,
        startDate: Date,
        endDate: Date,
        discordServer?: BatchLinkedDiscordServerDto,
    ) {
        ArgumentValidation.assert.defined(courseId, 'Batch course is required');
        ArgumentValidation.assert.defined(name, 'Batch name is required');
        ArgumentValidation.assert.defined(startDate, 'Batch start date is required');
        ArgumentValidation.assert.after(endDate, startDate, 'Batch end date has to be after the start');

        this.courseId = courseId;
        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
        this.discordServer = discordServer;
    }
}
