import DomainEntity from '../../core/domain/DomainEntity';
import Id from '../../core/domain/value-objects/Id';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import Course from '../../education/courses/domain/Course';
import BatchName from './BatchName';
import { BatchDomainEvents, BatchEndDateChanged, BatchStartDateChanged } from './BatchDomainEvents';
import { NonFunctionProperties, RequireAllExcept } from '../../utils/UtilityTypes';

export type BatchParams = RequireAllExcept<NonFunctionProperties<Batch>, 'mentors' | 'id'>;

export default class Batch extends DomainEntity<BatchDomainEvents> {
    readonly course: Course;

    readonly name: BatchName;

    readonly startDate: Date;

    readonly endDate: Date;

    readonly mentors: Id[];

    constructor(
        { id, course, name, startDate, endDate, mentors = [] }: BatchParams,
        domainEvents: BatchDomainEvents[] = [],
    ) {
        super(id, domainEvents);

        ArgumentValidation.assert.defined(course, 'Batch course is required');
        ArgumentValidation.assert.defined(name, 'Batch name is required');
        ArgumentValidation.assert.defined(startDate, 'Batch start date is required');
        ArgumentValidation.assert.after(endDate, startDate, 'Batch end date has to be after the start');

        this.course = course;
        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
        this.mentors = [...mentors];
    }

    isStarted(): boolean {
        return +this.startDate <= +new Date();
    }

    rename(name: BatchName): Batch {
        if (this.name.equals(name)) {
            return this;
        }

        return new Batch({
            ...this,
            name,
        });
    }

    changeDates(startDate: Date, endDate: Date): Batch {
        const isStartChanged = this.startDate.getTime() !== startDate.getTime();
        const isEndChanged = this.endDate.getTime() !== endDate.getTime();

        if (!isStartChanged && !isEndChanged) {
            return this;
        }

        return new Batch(
            {
                ...this,
                startDate,
                endDate,
            },
            [
                ...this.domainEvents,
                ...(isStartChanged ? [new BatchStartDateChanged()] : []),
                ...(isEndChanged ? [new BatchEndDateChanged()] : []),
            ],
        );
    }

    changeMentors(mentors: Id[]): Batch {
        return new Batch({ ...this, mentors }, this.domainEvents);
    }
}
