import { IsDate, IsOptional, IsString, ValidateNested } from 'class-validator';
import { IsInFuture } from '../../../../core/controllers/validators/IsInFuture';
import BatchName from '../../../domain/BatchName';
import BatchUpdateData from '../../../services/dto/BatchUpdateData';
import { Type } from 'class-transformer';
import { BatchLinkedDiscordServerDto } from './BatchLinkedDiscordServerDto';

export default class BatchUpdateDTO {
    @IsString()
    name: string;

    @IsOptional()
    @IsDate()
    @IsInFuture()
    startDate?: Date;

    @IsOptional()
    @IsDate()
    @IsInFuture()
    endDate?: Date;

    @IsOptional()
    @ValidateNested()
    @Type(() => BatchLinkedDiscordServerDto)
    discordServer?: BatchLinkedDiscordServerDto;

    toBatchUpdateData(): BatchUpdateData {
        return new BatchUpdateData(new BatchName(this.name), this.startDate, this.endDate, this.discordServer);
    }
}
