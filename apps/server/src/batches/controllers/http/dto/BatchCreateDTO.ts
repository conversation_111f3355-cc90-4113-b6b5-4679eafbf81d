import { IsDate, IsOptional, IsString, ValidateNested } from 'class-validator';
import { IsInFuture } from '../../../../core/controllers/validators/IsInFuture';
import Id from '../../../../core/domain/value-objects/Id';
import BatchName from '../../../domain/BatchName';
import BatchCreateData from '../../../services/dto/BatchCreateData';
import { IsId } from '../../../../core/controllers/validators/IsId';
import { Type } from 'class-transformer';
import { BatchLinkedDiscordServerDto } from './BatchLinkedDiscordServerDto';

export default class BatchCreateDTO {
    @IsString()
    name: string;

    @IsDate()
    @IsInFuture()
    startDate: Date;

    @IsDate()
    @IsInFuture()
    endDate: Date;

    @IsId()
    courseId: number;

    @IsOptional()
    @ValidateNested()
    @Type(() => BatchLinkedDiscordServerDto)
    discordServer: BatchLinkedDiscordServerDto;

    toBatchCreateData(): BatchCreateData {
        return new BatchCreateData(new Id(this.courseId), new BatchName(this.name), this.startDate, this.endDate);
    }
}
