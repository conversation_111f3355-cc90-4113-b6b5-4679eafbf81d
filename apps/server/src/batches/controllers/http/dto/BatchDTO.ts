import { Type } from 'class-transformer';
import { IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import CourseDTO from './CourseDTO';
import { BatchLinkedDiscordServerDto } from './BatchLinkedDiscordServerDto';
import { QueriedBatch } from '../../../domain/QueriedBatch';

export class BatchDTO {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsDate()
    startDate: Date;

    @IsDate()
    endDate: Date;

    @ValidateNested()
    @Type(() => CourseDTO)
    course: CourseDTO;

    @IsNumber()
    studentCount: number;

    @IsOptional()
    @ValidateNested()
    @Type(() => BatchLinkedDiscordServerDto)
    discordServer?: BatchLinkedDiscordServerDto;

    constructor(
        id: number,
        name: string,
        startDate: Date,
        endDate: Date,
        course: CourseDTO,
        studentCount: number,
        discordServer?: BatchLinkedDiscordServerDto,
    ) {
        this.id = id;
        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
        this.course = course;
        this.studentCount = studentCount;
        this.discordServer = discordServer;
    }

    static fromBatch(batch: QueriedBatch): BatchDTO {
        return new BatchDTO(
            batch?.getIdOrThrow().value,
            batch.name.value,
            batch.startDate,
            batch.endDate,
            CourseDTO.fromCourse(batch.course),
            batch.studentsCount,
            batch.discordServer ? BatchLinkedDiscordServerDto.fromDomain(batch.discordServer) : undefined,
        );
    }
}

export default BatchDTO;
