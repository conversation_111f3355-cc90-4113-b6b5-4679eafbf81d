import { faker } from '@faker-js/faker';
import 'jest-extended';
import moment from 'moment';
import request from 'supertest';
import Container from 'typedi';
import BatchController from './BatchController';
import BatchDTO from './dto/BatchDTO';
import Batch from '../../domain/Batch';
import BatchName from '../../domain/BatchName';
import BatchRepository from '../../domain/BatchRepository';
import { isBatch } from '../../infrastructure/db/BatchPersistenceEntity';
import BatchTypeormRepository from '../../infrastructure/db/BatchTypeormRepository';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Arrays from '../../../core/collections/Arrays';
import Id from '../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import Course from '../../../education/courses/domain/Course';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import { IntegrationTestBatch } from '../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { CourseOnboardingConnection } from '../../../education/courses/domain/CourseOnboardingConnection';

describe(BatchController.name, () => {
    let config: Configuration;
    let batchRepository: BatchRepository;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let student: IntegrationTestUser;
    let batches: IntegrationTestBatch[];
    let course: IntegrationTestCourse;

    async function count(): Promise<number> {
        const res = await request(config.server)
            .get('/batch')
            .set('Cookie', `token=${admin.getAuthTokenString()}`)
            .send()
            .expect(200);

        return res.body.batches.length;
    }

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        batchRepository = new BatchTypeormRepository(Container.get(TransactionManagerToken));
        ({ admin, student, staff } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());

        course = await IntegrationTestCourse.create();

        batches = await Promise.all(
            Arrays.stream(4).map(() => {
                return IntegrationTestBatch.create({ courseId: course.params.getIdOrThrow() });
            }),
        );
    });

    describe('GET /batch', () => {
        it('should return 200 OK and a list of batches for admin', async () => {
            const res = await request(config.server)
                .get('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body.batches).toBeDefined();
            expect(res.body.batches).toSatisfyAll(isBatch);
            res.body.batches.forEach((batch: any) => {
                expect(batch.studentCount).toBeNumber();
                expect(batch.course.id).toBeDefined();
                expect(batch.course.name).toBeString();
            });
        });

        it('should return 200 OK and a list of batches for staff', async () => {
            const res = await request(config.server)
                .get('/batch')
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body.batches).toBeDefined();
            expect(res.body.batches).toSatisfyAll(isBatch);
            res.body.batches.forEach((batch: any) => {
                expect(batch.studentCount).toBeNumber();
                expect(batch.course.id).toBeDefined();
                expect(batch.course.name).toBeString();
            });
        });

        it('should return 403 Forbidden for student', async () => {
            await request(config.server)
                .get('/batch')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);
        });
    });

    describe('POST /batch', () => {
        it('should return 200 OK and create', async () => {
            const data = {
                name: TestObjects.uniqueWords(),
                startDate: moment().add(1, 'day').toDate(),
                endDate: moment().add(1, 'year').toDate(),
                courseId: course.getIdOrThrow().value,
            };

            const res = await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(200);

            expect(res.body).toEqual({
                id: expect.any(Number),
                name: data.name,
                startDate: data.startDate.toISOString(),
                endDate: data.endDate.toISOString(),
                course: {
                    id: data.courseId,
                    name: expect.any(String),
                    internalName: expect.any(String),
                },
                studentCount: expect.any(Number),
            });
            await expect(batchRepository.exists(new Id(res.body.id))).resolves.toBeTruthy();
        });

        it('should return 403 Forbidden for staff', async () => {
            const countBefore = await count();

            await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(403);

            await expect(count()).resolves.toEqual(countBefore);
        });

        it('should return 403 Forbidden for user', async () => {
            const countBefore = await count();

            await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);

            await expect(count()).resolves.toEqual(countBefore);
        });

        it('should return 400 Bad Request if course by id was not found', async () => {
            const data = {
                name: TestObjects.uniqueWords(),
                startDate: moment().add(1, 'day').toDate(),
                courseId: 999,
            };

            await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(400);
        });

        it('should return 400 Bad Request if startDate is in the past', async () => {
            const data = {
                name: TestObjects.uniqueWords(),
                startDate: moment().subtract(1, 'day').toDate(),
                endDate: moment().add(1, 'day').toDate(),
                courseId: course.getIdOrThrow().value,
            };

            const res = await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(400);

            const expectedError = {
                isInFuture: 'startDate must be in the future',
            };
            expect(res.body.errors.length).toEqual(1);
            expect(res.body.errors[0].constraints).toEqual(expectedError);
        });

        it('should return 400 Bad Request if name is not unique', async () => {
            const data = {
                name: TestObjects.uniqueWords(),
                startDate: moment().add(1, 'day').toDate(),
                endDate: moment().add(1, 'year').toDate(),
                courseId: course.getIdOrThrow().value,
            };

            await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(200);

            const res = await request(config.server)
                .post('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(400);

            expect(res.body.message).toEqual('Batch name is already in use');
        });
    });

    describe('PUT /batch/:id', () => {
        it('should return 200 OK and update batch by id', async () => {
            const id = 1;
            const data = {
                name: TestObjects.uniqueWords(),
                startDate: moment().add(1, 'day').toDate(),
                endDate: moment().add(1, 'year').toDate(),
            };

            const res = await request(config.server)
                .put(`/batch/${id}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(200);

            expect(res.body).toEqual({
                id,
                name: data.name,
                startDate: data.startDate.toISOString(),
                endDate: data.endDate.toISOString(),
                course: {
                    id: expect.any(Number),
                    name: expect.any(String),
                    internalName: expect.any(String),
                },
                studentCount: expect.any(Number),
            });
        });

        it('should return 200 OK and update batch without start date', async () => {
            const id = 1;
            const data = {
                name: TestObjects.uniqueWords(),
            };

            const foundResBefore = await request(config.server)
                .get('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);
            const batchBefore = foundResBefore.body.batches.find((b: BatchDTO) => b.id === id);
            const res = await request(config.server)
                .put(`/batch/${id}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(200);

            expect(res.body).toEqual({
                ...batchBefore,
                name: data.name,
            });
        });

        it('should return 404 Not Found if batch by id is not found', async () => {
            await request(config.server)
                .put('/batch/999')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send({
                    name: TestObjects.uniqueWords(),
                })
                .expect(404);
        });

        it('should return 403 Forbidden for staff', async () => {
            await request(config.server)
                .put('/batch/${batches[0].id.value}')
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should return 403 Forbidden for user', async () => {
            await request(config.server)
                .put('/batch/${batches[0].id.value}')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should return 400 Bad Request if startDate is in the past', async () => {
            const res = await request(config.server)
                .put(`/batch/${batches[0].getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send({
                    name: TestObjects.uniqueWords(),
                    startDate: moment().subtract(1, 'day').toDate(),
                })
                .expect(400);

            const expectedError = {
                isInFuture: 'startDate must be in the future',
            };
            expect(res.body.errors.length).toEqual(1);
            expect(res.body.errors[0].constraints).toEqual(expectedError);
        });

        it('should return 400 Bad Request if name is not unique', async () => {
            const batchOne = await batchRepository.save(
                new Batch({
                    course: new Course(
                        {
                            name: course.params.name,
                            onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                        },
                        new Id(course.getIdOrThrow()),
                    ),
                    name: new BatchName(faker.word.noun()),
                    startDate: moment().add(1, 'day').toDate(),
                    endDate: moment().add(1, 'year').toDate(),
                    id: TestObjects.id(),
                }),
            );
            const batchTwo = await batchRepository.save(
                new Batch({
                    course: new Course(
                        {
                            name: course.params.name,
                            onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                        },
                        new Id(course.getIdOrThrow().value),
                    ),
                    name: new BatchName(faker.word.noun()),
                    startDate: moment().add(1, 'day').toDate(),
                    endDate: moment().add(1, 'year').toDate(),
                    id: TestObjects.id(),
                }),
            );

            const res = await request(config.server)
                .put(`/batch/${batchOne.id?.value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send({
                    name: batchTwo.name.value,
                    startDate: batchOne.startDate,
                })
                .expect(400);

            expect(res.body.message).toEqual('Batch name is already in use');
        });
    });

    describe('DELETE /batch/:id', () => {
        it('should return 204 No Content and delete batch by id for admin', async () => {
            const batchToDelete = await IntegrationTestBatch.create({ courseId: course.params.getIdOrThrow() });
            await request(config.server)
                .delete(`/batch/${batchToDelete.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204)
                .expect({});

            const foundRes = await request(config.server)
                .get('/batch')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(foundRes.body.batches).not.toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        id: batchToDelete.getIdOrThrow().value,
                    }),
                ]),
            );
        });

        it('should return 403 Forbidden for staff', async () => {
            await request(config.server)
                .delete(`/batch/${batches[0].getIdOrThrow().value}`)
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should return 403 Forbidden for user', async () => {
            await request(config.server)
                .delete(`/batch/${batches[0].getIdOrThrow().value}`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should return 409 if batch has assigned students', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            const assigningBranchId = batches[batches.length - 1].params.getIdOrThrow();
            await student.createStudentSettings(assigningBranchId);

            const res = await request(config.server)
                .delete(`/batch/${assigningBranchId.value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(409);

            expect(res.body.message).toBe('Batch cannot be deleted because it has students');
        });
    });
});
