import Batch from '../domain/Batch';
import { NonFunctionProperties } from '../../utils/UtilityTypes';
import { CourseTestDataGenerator } from '../../education/courses/mock/CourseTestDataGenerator';
import BatchName from '../domain/BatchName';
import { faker } from '@faker-js/faker';

export class BatchTestDataGenerator {
    static create(params?: Partial<NonFunctionProperties<Batch>>): Batch {
        const startDate = params?.startDate ?? new Date();
        const endDate = params?.endDate ?? new Date(startDate.getTime() + 365 * 24 * 60 * 60 * 1000);

        return new Batch({
            course: params?.course ?? CourseTestDataGenerator.create(),
            name: new BatchName(params?.name ?? faker.company.name()),
            startDate,
            endDate,
            mentors: params?.mentors ?? [],
            id: params?.id,
        });
    }
}
