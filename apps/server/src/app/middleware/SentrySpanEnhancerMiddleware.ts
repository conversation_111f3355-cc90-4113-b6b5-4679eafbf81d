import * as Sentry from '@sentry/node';
import { Request, Response } from 'express';
import { ExpressMiddlewareInterface, Middleware } from 'routing-controllers';
import Container, { Service } from 'typedi';
import { RequestContextStorageToken } from '../../core/infrastructure/di/tokens';

@Service()
@Middleware({ type: 'before' })
export default class SentrySpanEnhancerMiddleware implements ExpressMiddlewareInterface {
    private readonly requestContextStorage = Container.get(RequestContextStorageToken);

    use(req: Request, res: Response, next: (err?: any) => any): void {
        const activeSpan = Sentry.getActiveSpan();

        if (activeSpan) {
            Sentry.updateSpanName(activeSpan, `${req.method} ${req.originalUrl}`);

            const requestContext = this.requestContextStorage.get();
            if (requestContext) {
                Sentry.setUser({
                    id: requestContext.userId?.value,
                    email: requestContext.userEmail?.value,
                    ip_address: requestContext.ip?.value,
                });
            }
        }

        next();
    }
}
