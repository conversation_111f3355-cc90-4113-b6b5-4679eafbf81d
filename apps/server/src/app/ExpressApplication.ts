import cookieParser from 'cookie-parser';
import express from 'express';
import basicAuth from 'express-basic-auth';
import { Server } from 'http';
import passport from 'passport';
import { useExpressServer } from 'routing-controllers';
import { SwaggerTheme, SwaggerThemeName } from 'swagger-themes';
import { serve as swaggerServe, setup as swaggerSetup } from 'swagger-ui-express';
import { generateApiSchema } from '../api-schema/generateApiSchema';
import CookieAuthorizer from '../auth/controllers/http/middleware/CookieAuthorizer';
import GithubAuthStrategyFactory from '../auth/controllers/http/middleware/GithubAuthStrategyFactory';
import GoogleAuthStrategyFactory from '../auth/controllers/http/middleware/GoogleAuthStrategyFactory';
import { Configuration } from '../config/Configuration';
import Url from '../core/domain/value-objects/Url';
import RequestContextMiddleware from '../core/infrastructure/context/RequestContextMiddleware';
import { ErrorTracker } from '../error-tracking/services/ErrorTracker';
import { LoggingService } from '../logging/services/LoggingService';
import GithubSignupStrategyFactory from '../users/signup/express/GithubSignupStrategyFactory';
import GoogleSignupStrategyFactory from '../users/signup/express/GoogleSignupStrategyFactory';
import Logger from '../utils/logger/Logger';
import SentrySpanEnhancerMiddleware from './middleware/SentrySpanEnhancerMiddleware';
import { createSystemErrorHandler } from './middleware/system-error-handler';

export default class ExpressApplication {
    private _server?: Server;

    private constructor(
        private readonly app: express.Express,
        private readonly port: number,
        private readonly logger: Logger,
    ) {}

    get server(): Server | undefined {
        return this._server;
    }

    async listen(): Promise<void> {
        this.logger.debug('STARTING_EXPRESS_SERVER', { existingServer: !!this._server, port: this.port });
        if (!this._server) {
            await new Promise((resolve, reject): void => {
                this._server = this.app.listen(this.port, (err?: Error): void => {
                    if (err) {
                        return reject(err);
                    }

                    this.logger.info('EXPRESS_RUNNING', { port: this.port });

                    return resolve(undefined);
                });
            });
        }
        this.logger.debug('EXPRESS_SERVER_STARTED');
    }

    async stop(): Promise<void> {
        this.logger.debug('STOPPING_EXPRESS_SERVER', { existingServer: !!this._server });

        if (this._server) {
            await this.closeServer();
            this._server = undefined;
        }

        this.logger.debug('EXPRESS_SERVER_STOPPED');
    }

    private async closeServer(): Promise<unknown> {
        if (this._server) {
            return new Promise((resolve): void => {
                if (this._server) {
                    this._server.close(resolve);
                }
            });
        }
    }

    static create({
        configuration,
        authorizer,
        errorTracker,
        controllers,
        loggingService,
    }: {
        configuration: Configuration;
        authorizer: CookieAuthorizer;
        errorTracker: ErrorTracker;
        controllers: any[];
        loggingService: LoggingService;
    }): ExpressApplication {
        const app = express();

        app.set('trust proxy', true);
        app.use(cookieParser());

        ExpressApplication.setupPassport({ app, configuration });
        ExpressApplication.setupExpressServer({ app, configuration, controllers, authorizer });
        ExpressApplication.setupSwagger({ app, configuration });
        ExpressApplication.setupErrorHandling({ app, errorTracker, loggingService });

        return new ExpressApplication(app, configuration.port, loggingService.createLogger(ExpressApplication.name));
    }

    private static setupPassport({ app, configuration }: { app: express.Express; configuration: Configuration }): void {
        passport.use(GithubAuthStrategyFactory.STRATEGY_NAME, GithubAuthStrategyFactory.getStrategy(configuration));
        passport.use(GithubSignupStrategyFactory.STRATEGY_NAME, GithubSignupStrategyFactory.getStrategy(configuration));
        passport.use(GoogleAuthStrategyFactory.STRATEGY_NAME, GoogleAuthStrategyFactory.getStrategy(configuration));
        passport.use(GoogleSignupStrategyFactory.STRATEGY_NAME, GoogleSignupStrategyFactory.getStrategy(configuration));
        app.use(passport.initialize());
    }

    private static setupErrorHandling({
        app,
        errorTracker,
        loggingService,
    }: {
        app: express.Express;
        errorTracker: ErrorTracker;
        loggingService: LoggingService;
    }): void {
        // Error tracker has to be registered before all the other error handling middleware
        errorTracker.registerExpressApp(app);
        app.use(createSystemErrorHandler(loggingService));
    }

    private static setupExpressServer({
        app,
        configuration,
        controllers,
        authorizer,
    }: {
        app: express.Express;
        configuration: Configuration;
        controllers: any[];
        authorizer: CookieAuthorizer;
    }): void {
        useExpressServer(app, {
            controllers,
            authorizationChecker: (action, roles) =>
                authorizer.authorize(action.request, action.response, roles).then((user) => !!user),
            currentUserChecker: (action) => authorizer.getAuthorizedUser(action.request),
            // Do not add any error handling middleware here. It is setup separately.
            middlewares: [RequestContextMiddleware, SentrySpanEnhancerMiddleware],
            defaultErrorHandler: false,
            classTransformer: true,
            plainToClassTransformOptions: {
                enableImplicitConversion: true,
            },
            cors: {
                origin: configuration.http.cors.origin.split(' '),
                optionsSuccessStatus: 200,
                credentials: configuration.http.cors.isCredentials,
            },
            validation: {
                validationError: {
                    target: false,
                    value: false,
                },
                whitelist: true,
                forbidUnknownValues: true,
            },
            defaults: {
                nullResultCode: 404,
                undefinedResultCode: 204,
                paramOptions: {
                    required: true,
                },
            },
        });
    }

    private static setupSwagger({ app, configuration }: { app: express.Express; configuration: Configuration }): void {
        if (!configuration.swagger.isEnabled) {
            return;
        }

        const spec = generateApiSchema();

        const middlewares = [
            swaggerServe,
            swaggerSetup(
                spec,
                {
                    customCss:
                        new SwaggerTheme().getBuffer(<SwaggerThemeName>'dark') +
                        `
                        .swagger-ui .topbar {
                            display: none;
                        }
                    `,
                    customSiteTitle: `TC API - ${new Url(configuration.server).hostname}`,
                    customfavIcon: 'https://intra.turingcollege.com/favicon-32x32.png',
                },
                {},
            ),
        ];

        if (configuration.swagger.username) {
            middlewares.unshift(
                basicAuth({
                    challenge: true,
                    users: { [configuration.swagger.username]: configuration.swagger.password },
                }),
            );
        }

        app.use('/docs', ...middlewares);
    }
}
