import { faker } from '@faker-js/faker';
import moment from 'moment';
import { anything, instance, mock, reset, when } from 'ts-mockito';
import Batch from '../../batches/domain/Batch';
import BatchName from '../../batches/domain/BatchName';
import Email from '../../core/domain/value-objects/Email';
import ImageString from '../../core/domain/value-objects/ImageString';
import Order from '../../core/domain/value-objects/Order';
import FullName from '../../core/domain/value-objects/user/FullName';
import IllegalStateError from '../../core/errors/IllegalStateError';
import Correction from '../../corrections/domain/Correction';
import CorrectionTime from '../../corrections/domain/CorrectionTime';
import Course from '../../education/courses/domain/Course';
import HiringProfile from '../../endorsement/domain/hiring-profile/HiringProfile';
import Language from '../../endorsement/domain/hiring-profile/Language';
import PhoneNumber from '../../endorsement/domain/hiring-profile/PhoneNumber';
import GithubProfileEndorsement from '../../endorsement/domain/social-media/GithubProfileEndorsement';
import GithubUrl from '../../endorsement/domain/social-media/GithubUrl';
import LinkedInProfileEndorsement from '../../endorsement/domain/social-media/LinkedInProfileEndorsement';
import LinkedInUrl from '../../endorsement/domain/social-media/LinkedInUrl';
import SocialMedia from '../../endorsement/domain/social-media/SocialMedia';
import CompletedStageState from '../../endorsement/domain/state/CompletedStageState';
import { StudentSkillsFinder } from '../../learning/submodules/roadmap/services/StudentSkillsFinder';
import Skill from '../../learning/submodules/skills/domain/Skill';
import SkillCategory from '../../learning/submodules/skills/domain/SkillCategory';
import SkillCategoryName from '../../learning/submodules/skills/domain/SkillCategoryName';
import SkillName from '../../learning/submodules/skills/domain/SkillName';
import { Role } from '../../users/shared/infrastructure/db/User';
import ResumePublicId from '../domain/ResumePublicId';
import ResumeSettings from '../domain/ResumeSettings';
import ResumeManager from './ResumeManager';
import Resume from './dto/Resume';
import ResumeAccountExecutive from './dto/ResumeAccountExecutive';
import ResumeBatchMentor from './dto/ResumeBatchMentor';
import ResumeCorrections from './dto/ResumeCorrections';
import ResumeEvaluator from './dto/ResumeEvaluator';
import ResumeProfile from './dto/ResumeProfile';
import ResumeSkillCategory from './dto/ResumeSkillCategory';
import UserAccount from '../../users/accounts/domain/UserAccount';
import EmploymentPosition from '../../users/mentors/domain/EmploymentPosition';
import MentorProfile from '../../users/mentors/domain/MentorProfile';
import { BillingType } from '../../users/students/settings/domain/BillingType';
import StudentSettings from '../../users/students/settings/domain/StudentSettings';
import ResumeStudentSettingsHelper from '../../users/students/settings/services/ResumeStudentSettingsHelper';
import BatchFinderMock from '../../test-toolkit/unit/deprecated-mocks/BatchFinderMock';
import CorrectionFinderMock from '../../test-toolkit/unit/deprecated-mocks/CorrectionFinderMock';
import HiringProfileManagerMock from '../../test-toolkit/unit/deprecated-mocks/HiringProfileManagerMock';
import ResumeSettingsRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/ResumeSettingsRepositoryMock';
import SocialMediaManagerMock from '../../test-toolkit/unit/deprecated-mocks/SocialMediaManagerMock';
import TestObjects from '../../test-toolkit/shared/TestObjects';
import { UserState } from '../../users/accounts/domain/UserState';
import { CourseOnboardingConnection } from '../../education/courses/domain/CourseOnboardingConnection';
import { CorrectionStatus } from '../../corrections/domain/CorrectionStatus';
import { CorrectionType } from '../../corrections/domain/CorrectionType';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';
import { MentorProfileFinder } from '../../users/mentors/services/MentorProfileFinder';
import { StudentSource } from '../../users/students/settings/domain/StudentSource';
import Username from '../../core/domain/value-objects/user/Username';
import { StudentSettingsFinder } from '../../users/students/settings/services/StudentSettingsFinder';
import { StudentSettingsRepository } from '../../users/students/settings/db/StudentSettingsRepository';

describe(ResumeManager.name, () => {
    const resumeSettingsRepository = new ResumeSettingsRepositoryMock();
    const hiringProfileManager = new HiringProfileManagerMock();
    const socialMediaManager = new SocialMediaManagerMock();
    const studentSkillsFinder = mock(StudentSkillsFinder);
    const correctionFinder = new CorrectionFinderMock();
    const userAccountFinder = mock(UserAccountFinder);
    const batchFinder = new BatchFinderMock();
    const studentSettingsFinder = mock(StudentSettingsFinder);
    const mentorProfileFinder = mock(MentorProfileFinder);
    const studentSettingsRepository = mock(StudentSettingsRepository);
    const resumeStudentSettingsHelper = new ResumeStudentSettingsHelper(instance(studentSettingsRepository));
    const resumeManager = new ResumeManager(
        resumeSettingsRepository,
        hiringProfileManager,
        socialMediaManager,
        instance(studentSkillsFinder),
        correctionFinder,
        instance(userAccountFinder),
        batchFinder,
        instance(studentSettingsFinder),
        instance(mentorProfileFinder),
        resumeStudentSettingsHelper,
    );
    const user = new UserAccount({
        role: Role.USER,
        email: TestObjects.email(),
        state: UserState.ACTIVE,
        calendarToken: 'test',
        createdAt: new Date(),
        username: TestObjects.uniqueUsername(),
        name: TestObjects.fullName(),
        id: TestObjects.uniqueId(),
        permissions: [],
    });
    const batch = new Batch({
        course: new Course(
            { name: faker.word.noun(), onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)] },
            TestObjects.uniqueId(),
        ),
        name: new BatchName(faker.word.noun()),
        startDate: new Date(),
        endDate: moment().add(1, 'year').toDate(),
        mentors: [],
        id: TestObjects.uniqueId(),
    });
    const studentProfile = new StudentSettings({
        id: TestObjects.uniqueId(),
        studentId: user.getIdOrThrow(),
        batchId: batch.getIdOrThrow(),
        billingType: BillingType.SCHOLARSHIP,
        isDeadlineMandatory: false,
        startDate: new Date(),
        endDate: moment().add(1, 'year').toDate(),
        source: faker.helpers.enumValue(StudentSource),
    });
    const settings = new ResumeSettings(
        user.getIdOrThrow(),
        ResumePublicId.generate(user.username as Username),
        true,
        true,
    );

    beforeEach(() => {
        reset(studentSkillsFinder);
        when(userAccountFinder.findByUserId(anything())).thenResolve(user);
        batchFinder.findByIdMock.mockResolvedValue(batch);
        when(studentSettingsFinder.findByUserId(anything())).thenResolve(studentProfile);
        when(studentSettingsRepository.didStudentHaveType(anything(), anything(), anything())).thenResolve(false);
        resumeSettingsRepository.getByUserMock.mockResolvedValue(settings);
        resumeSettingsRepository.getByPublicIdMock.mockResolvedValue(settings);
        hiringProfileManager.getMock.mockResolvedValue(
            new HiringProfile(user.getIdOrThrow(), [new CompletedStageState()], []),
        );
        socialMediaManager.getMock.mockResolvedValue(
            new SocialMedia(
                user.getIdOrThrow(),
                [new CompletedStageState()],
                [],
                new GithubProfileEndorsement([]),
                new LinkedInProfileEndorsement([]),
            ),
        );
        when(studentSkillsFinder.findStudentSkills(anything())).thenResolve([]);
        correctionFinder.findEvaluatorCorrectionsMock.mockResolvedValue([]);
        correctionFinder.findStudentCorrectionsMock.mockResolvedValue([]);
    });

    async function testResumeProfile(resumeFunction: () => Promise<Resume>, wasJTL: boolean = false): Promise<void> {
        const hiringProfile = new HiringProfile(
            user.getIdOrThrow(),
            [new CompletedStageState()],
            [],
            TestObjects.phoneNumber(),
            [new Language(TestObjects.uniqueWord()), new Language(TestObjects.uniqueWord())],
        );
        const socialMedia = new SocialMedia(
            user.getIdOrThrow(),
            [new CompletedStageState()],
            [],
            new GithubProfileEndorsement([], new GithubUrl(`https://github.com/${faker.word.noun()}`)),
            new LinkedInProfileEndorsement([], new LinkedInUrl(`https://linkedin.com/${faker.word.noun()}`)),
        );
        if (wasJTL) {
            when(studentSettingsRepository.didStudentHaveType(anything(), anything(), anything())).thenResolve(true);
        }

        hiringProfileManager.getMock.mockResolvedValueOnce(hiringProfile);
        socialMediaManager.getMock.mockResolvedValueOnce(socialMedia);

        const resume = await resumeFunction();
        expect(resume.profile).toEqual(
            new ResumeProfile(
                user.name as FullName,
                user.email,
                wasJTL,
                hiringProfile.phoneNumber,
                socialMedia.github.url,
                socialMedia.linkedIn.url,
                hiringProfile.languages,
            ),
        );
    }

    async function testResumeSkills(resumeFunction: () => Promise<Resume>): Promise<void> {
        const skillCategories = [
            new SkillCategory(new SkillCategoryName(faker.word.noun()), new Order(0), TestObjects.uniqueId()),
            new SkillCategory(new SkillCategoryName(faker.word.noun()), new Order(1), TestObjects.uniqueId()),
        ];
        const skills = [
            new Skill(
                skillCategories[0],
                new SkillName(faker.word.noun()),
                faker.datatype.boolean(),
                new ImageString(faker.word.noun()),
            ),
            new Skill(
                skillCategories[1],
                new SkillName(faker.word.noun()),
                faker.datatype.boolean(),
                new ImageString(faker.word.noun()),
            ),
            new Skill(
                skillCategories[0],
                new SkillName(faker.word.noun()),
                faker.datatype.boolean(),
                new ImageString(faker.word.noun()),
            ),
        ];

        when(studentSkillsFinder.findStudentSkills(anything())).thenResolve(skills);

        const resume = await resumeFunction();
        expect(resume.skills).toEqual([
            new ResumeSkillCategory(skillCategories[0], expect.arrayContaining([skills[0], skills[2]])),
            new ResumeSkillCategory(skillCategories[1], [skills[1]]),
        ]);
    }

    async function testResumeCorrections(resumeFunction: () => Promise<Resume>): Promise<void> {
        const mentor = new UserAccount({
            role: Role.MENTOR,
            email: TestObjects.email(),
            state: UserState.ACTIVE,
            calendarToken: 'test',
            createdAt: new Date(),
            username: TestObjects.uniqueUsername(),
            name: TestObjects.fullName(),
            id: TestObjects.uniqueId(),
            permissions: [],
        });
        const mentorProfile = new MentorProfile({
            userId: mentor.id!,
            employmentPosition: new EmploymentPosition(faker.word.words(3)),
            id: TestObjects.uniqueId(),
        });
        const givenCorrections = [
            new Correction({
                studentId: TestObjects.uniqueId(),
                evaluatorId: user.getIdOrThrow(),
                type: CorrectionType.STUDENT,
                time: new CorrectionTime(moment().subtract(1, 'week').toDate(), moment().subtract(1, 'week').toDate()),
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: TestObjects.uniqueId(),
                id: TestObjects.uniqueId(),
            }),
        ];
        const receivedCorrections = [
            new Correction({
                studentId: user.getIdOrThrow(),
                evaluatorId: mentorProfile.userId,
                type: CorrectionType.MENTOR,
                time: new CorrectionTime(moment().subtract(1, 'week').toDate(), moment().subtract(1, 'week').toDate()),
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: TestObjects.uniqueId(),
                id: TestObjects.uniqueId(),
            }),
            new Correction({
                studentId: user.getIdOrThrow(),
                evaluatorId: TestObjects.uniqueId(),
                type: CorrectionType.STUDENT,
                time: new CorrectionTime(moment().subtract(1, 'day').toDate(), moment().subtract(1, 'day').toDate()),
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: TestObjects.uniqueId(),
                id: TestObjects.uniqueId(),
            }),
        ];

        when(userAccountFinder.findByUserIds(anything())).thenResolve([mentor]);
        when(mentorProfileFinder.findByUserIds(anything())).thenResolve([mentorProfile]);
        correctionFinder.findEvaluatorCorrectionsMock.mockResolvedValueOnce([...givenCorrections]);
        correctionFinder.findStudentCorrectionsMock.mockResolvedValueOnce([...receivedCorrections]);

        const resume = await resumeFunction();
        expect(resume.corrections).toEqual(
            new ResumeCorrections(
                givenCorrections.length,
                receivedCorrections.length,
                receivedCorrections[1].time.start,
                [
                    new ResumeEvaluator(
                        mentor.name as FullName,
                        mentor.role,
                        mentor.image,
                        mentorProfile.employmentPosition,
                    ),
                ],
            ),
        );
    }

    async function testResumeBatchMentors(resumeFunction: () => Promise<Resume>): Promise<void> {
        const mentor = new UserAccount({
            role: Role.MENTOR,
            email: TestObjects.email(),
            state: UserState.ACTIVE,
            calendarToken: 'test',
            createdAt: new Date(),
            username: TestObjects.uniqueUsername(),
            name: TestObjects.fullName(),
            id: TestObjects.uniqueId(),
            permissions: [],
        });
        const mentorProfile = new MentorProfile({
            userId: mentor.id!,
            employmentPosition: new EmploymentPosition(faker.word.words(3)),
            id: TestObjects.uniqueId(),
        });

        when(userAccountFinder.findByUserIds(anything())).thenResolve([mentor]);
        when(mentorProfileFinder.findByUserIds(anything())).thenResolve([mentorProfile]);
        batchFinder.findByIdMock.mockResolvedValueOnce(batch.changeMentors([mentor.getIdOrThrow()]));

        const resume = await resumeFunction();
        expect(resume.batchMentors).toEqual([
            new ResumeBatchMentor(mentor.name as FullName, mentor.image, mentorProfile.employmentPosition),
        ]);
    }

    async function testResumeAccountExecutive(resumeFunction: () => Promise<Resume>): Promise<void> {
        const resume = await resumeFunction();
        expect(resume.accountExecutive).toEqual(
            new ResumeAccountExecutive(
                new FullName('Elena', 'Karmazaitė'),
                new Email('<EMAIL>'),
                new PhoneNumber('+***********'),
                new LinkedInUrl('https://www.linkedin.com/in/elena-karmazait%c4%97-b38a15199'),
                new ImageString(
                    'https://res.cloudinary.com/turingcollege/image/upload/w_150,h_150,q_100/TuringCollege/idgm53x2xplrdm4f5yk2.jpg',
                ),
            ),
        );
    }

    describe('getResume', () => {
        test('should not get if settings do not exist', async () => {
            resumeSettingsRepository.getByUserMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getUserResume(user.getIdOrThrow())).resolves.toBeUndefined();
        });

        test('should get if not enabled', async () => {
            resumeSettingsRepository.getByUserMock.mockResolvedValueOnce(settings.disable());

            await expect(resumeManager.getUserResume(user.getIdOrThrow())).resolves.toBeDefined();
        });

        test('should get if not authorized', async () => {
            resumeSettingsRepository.getByUserMock.mockResolvedValueOnce(settings.removeConsent());

            await expect(resumeManager.getUserResume(user.getIdOrThrow())).resolves.toBeDefined();
        });

        test('should not get without hiring profile', async () => {
            hiringProfileManager.getMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getUserResume(user.getIdOrThrow())).rejects.toBeInstanceOf(IllegalStateError);
        });

        test('should not get without social media', async () => {
            socialMediaManager.getMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getUserResume(user.getIdOrThrow())).rejects.toBeInstanceOf(IllegalStateError);
        });

        test('should include a not JTL profile', async () => {
            await testResumeProfile(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>, false);
        });

        test('should include a JTL profile', async () => {
            await testResumeProfile(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>, true);
        });

        test('should include skills', async () => {
            await testResumeSkills(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>);
        });

        test('should include corrections', async () => {
            await testResumeCorrections(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>);
        });

        test('should include batch mentors', async () => {
            await testResumeBatchMentors(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>);
        });

        test('should include account executive', async () => {
            await testResumeAccountExecutive(() => resumeManager.getUserResume(user.getIdOrThrow()) as Promise<Resume>);
        });
    });

    describe('getPublicResume', () => {
        test('should not get if settings do not exist', async () => {
            resumeSettingsRepository.getByPublicIdMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getPublicResume(settings.publicId)).resolves.toBeUndefined();
        });

        test('should not get if not enabled', async () => {
            resumeSettingsRepository.getByPublicIdMock.mockResolvedValueOnce(settings.disable());

            await expect(resumeManager.getPublicResume(settings.publicId)).resolves.toBeUndefined();
        });

        test('should not get if not authorized', async () => {
            resumeSettingsRepository.getByPublicIdMock.mockResolvedValueOnce(settings.removeConsent());

            await expect(resumeManager.getPublicResume(settings.publicId)).resolves.toBeUndefined();
        });

        test('should not get without hiring profile', async () => {
            hiringProfileManager.getMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getPublicResume(settings.publicId)).rejects.toBeInstanceOf(IllegalStateError);
        });

        test('should not get without social media', async () => {
            socialMediaManager.getMock.mockResolvedValueOnce(undefined);

            await expect(resumeManager.getPublicResume(settings.publicId)).rejects.toBeInstanceOf(IllegalStateError);
        });

        test('should include a not JTL profile', async () => {
            await testResumeProfile(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>, false);
        });

        test('should include a JTL profile', async () => {
            await testResumeProfile(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>, true);
        });

        test('should include skills', async () => {
            await testResumeSkills(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>);
        });

        test('should include corrections', async () => {
            await testResumeCorrections(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>);
        });

        test('should include batch mentors', async () => {
            await testResumeBatchMentors(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>);
        });

        test('should include account executive', async () => {
            await testResumeAccountExecutive(() => resumeManager.getPublicResume(settings.publicId) as Promise<Resume>);
        });
    });
});
