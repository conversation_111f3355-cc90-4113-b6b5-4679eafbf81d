import { LocalAuthenticationMethod, SocialAuthenticationMethod } from '../../../auth/domain/AuthenticationMethod';
import Email from '../../../core/domain/value-objects/Email';
import Id from '../../../core/domain/value-objects/Id';
import FullName from '../../../core/domain/value-objects/user/FullName';
import Password from '../../../core/domain/value-objects/user/Password';
import Transaction from '../../../core/infrastructure/Transaction';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import ApplicationEvent from '../../../events/application/ApplicationEvent';
import ApplicationEventBroker from '../../../events/application/ApplicationEventBroker';
import { QueueService } from '../../../queue/QueueService';
import User, { Role } from '../../shared/infrastructure/db/User';
import { UserRepository } from '../../shared/infrastructure/db/UserRepository';
import { BillingType } from '../../students/settings/domain/BillingType';
import { StudentSettingsFinder } from '../../students/settings/services/StudentSettingsFinder';
import SignupToken from '../domain/SignupToken';
import { SignupCompletedEvent } from '../events/dto/SignupCompletedEvent';
import { SignupIntegrationEventsPublisher } from './SignupEventsPublisher';

export type SignupUserData = {
    billingType: BillingType;
};

export interface ActiveSignup {
    role: Role;
    email: Email;
    userData?: SignupUserData;
}

export interface SignupResult {
    user?: User;
    event?: ApplicationEvent<any>;
}

export interface SignupFinisherStrategy {
    finish(
        userId: Id,
        name: FullName,
        password: Password | undefined,
        signupType: LocalAuthenticationMethod | SocialAuthenticationMethod,
        tx: Transaction,
    ): Promise<SignupResult>;
}

export class SignupFinisher {
    constructor(
        private readonly tm: TransactionManager,
        private readonly userRepository: UserRepository,
        private readonly studentSettingsFinder: StudentSettingsFinder,
        private readonly eventBroker: ApplicationEventBroker,
        private readonly integrationEventPublisher: SignupIntegrationEventsPublisher,
        private readonly queueService: QueueService,
        private readonly strategies: Map<Role, SignupFinisherStrategy>,
    ) {}

    async finish(
        emailOrToken: Email | SignupToken,
        name: FullName,
        password: Password | undefined,
        signupType: LocalAuthenticationMethod | SocialAuthenticationMethod,
    ): Promise<User | undefined> {
        // @ts-expect-error TS(2339) FIXME: Property 'user' does not exist on type 'SignupResu... Remove this comment to see the full error message
        const { user, event } = await this.tm.execute(async (tx) => {
            const registeringUser =
                emailOrToken instanceof Email
                    ? await this.userRepository.findByEmail(emailOrToken.value)
                    : await this.userRepository.findByRegistrationToken(emailOrToken.value);

            if (!registeringUser?.isRegistering() || !this.strategies.has(registeringUser.role)) {
                return {};
            }

            return this.strategies
                .get(registeringUser.role)
                ?.finish(new Id(registeringUser.id), name, password, signupType, tx);
        });

        if (event) {
            await this.eventBroker.publish(event);
        }

        if (user) {
            await this.integrationEventPublisher.signupCompleted(user);

            // Publish SignupCompletedEvent for immediate schedule creation
            await this.queueService.publish({
                event: new SignupCompletedEvent({
                    userId: new Id(user.id),
                    role: user.role,
                }),
            });
        }

        return user;
    }

    async getActive(token: SignupToken): Promise<ActiveSignup | undefined> {
        const user = await this.userRepository.findByRegistrationToken(token.value);
        if (!user?.isRegistering()) {
            return undefined;
        }

        if (user.role !== Role.USER) {
            return {
                role: user.role,
                email: new Email(user.email),
            };
        }

        const studentSettings = await this.studentSettingsFinder.findByUserId(new Id(user.id));
        if (!studentSettings) {
            return undefined;
        }

        return {
            role: user.role,
            email: new Email(user.email),
            userData: {
                billingType: studentSettings.billingType,
            },
        };
    }
}
