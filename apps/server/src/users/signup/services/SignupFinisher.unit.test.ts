import { faker } from '@faker-js/faker';
import { anything, deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import { LocalAuthenticationMethod, SocialAuthenticationMethod } from '../../../auth/domain/AuthenticationMethod';
import Email from '../../../core/domain/value-objects/Email';
import Id from '../../../core/domain/value-objects/Id';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import { getTestUser } from '../../../test-toolkit/shared/userHelper';
import CoreTransactionManagerMock from '../../../test-toolkit/unit/deprecated-mocks/CoreTransactionManagerMock';
import EventBrokerMock from '../../../test-toolkit/unit/deprecated-mocks/EventBrokerMock';
import IntegrationEventPublisherMock from '../../../test-toolkit/unit/deprecated-mocks/IntegrationEventPublisherMock';
import SignupFinisherStrategyMock from '../../../test-toolkit/unit/deprecated-mocks/SignupFinisherStrategyMock';
import { TestConfigurationService } from '../../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import { UserState } from '../../accounts/domain/UserState';
import Mentor from '../../mentors/infrastructure/db/Mentor';
import User, { Role } from '../../shared/infrastructure/db/User';
import { UserRepository } from '../../shared/infrastructure/db/UserRepository';
import Staff from '../../staff/infrastructure/db/Staff';
import Student from '../../students/management/infrastructure/db/Student';
import { BillingType } from '../../students/settings/domain/BillingType';
import StudentSettings from '../../students/settings/domain/StudentSettings';
import { StudentSettingsFinder } from '../../students/settings/services/StudentSettingsFinder';
import SignupCompletedEventPayload from '../domain/SignupCompletedEventPayload';
import SignupToken from '../domain/SignupToken';
import { SignupCompletedEvent } from '../events/dto/SignupCompletedEvent';
import { SignupIntegrationEventsPublisher } from './SignupEventsPublisher';
import { SignupFinisher } from './SignupFinisher';
import { QueueService } from '../../../queue/QueueService';

describe(SignupFinisher.name, () => {
    const configuration = TestConfigurationService.create().getConfigurationSync();
    const userRepository = mock(UserRepository);
    const studentSettingsFinder = mock(StudentSettingsFinder);
    const queueService = mock(QueueService);
    const eventBroker = new EventBrokerMock();
    const integrationEventPublisher = new IntegrationEventPublisherMock();
    const strategies = new Map([
        [Role.USER, new SignupFinisherStrategyMock()],
        [Role.MENTOR, new SignupFinisherStrategyMock()],
        [Role.STAFF, new SignupFinisherStrategyMock()],
        [Role.ADMIN, new SignupFinisherStrategyMock()],
    ]);
    const finisher = new SignupFinisher(
        new CoreTransactionManagerMock(),
        instance(userRepository),
        instance(studentSettingsFinder),
        eventBroker,
        new SignupIntegrationEventsPublisher(configuration, integrationEventPublisher),
        instance(queueService),
        strategies,
    );

    beforeEach(() => {
        reset(queueService);
    });

    describe('finish', () => {
        async function testNotFinishByEmail(user: User): Promise<void> {
            when(userRepository.findByEmail(anything())).thenResolve(user);

            const result = await finisher.finish(
                TestObjects.email(),
                TestObjects.fullName(),
                await TestObjects.password(),
                faker.helpers.arrayElement([
                    LocalAuthenticationMethod.EMAIL,
                    SocialAuthenticationMethod.GOOGLE,
                    SocialAuthenticationMethod.GITHUB,
                ]),
            );

            expect(result).toBeUndefined();
            strategies.forEach((strategy) => expect(strategy.finishMock).not.toHaveBeenCalled());
            expect(eventBroker.publishMock).not.toHaveBeenCalled();
            expect(integrationEventPublisher.publishMock).not.toHaveBeenCalled();
            verify(queueService.publish(anything())).never();
        }

        async function testNotFinishByToken(user: User): Promise<void> {
            when(userRepository.findByRegistrationToken(anything())).thenResolve(user);

            const result = await finisher.finish(
                new SignupToken(faker.word.noun()),
                TestObjects.fullName(),
                await TestObjects.password(),
                faker.helpers.arrayElement([
                    LocalAuthenticationMethod.EMAIL,
                    SocialAuthenticationMethod.GOOGLE,
                    SocialAuthenticationMethod.GITHUB,
                ]),
            );
            expect(result).toBeUndefined();

            strategies.forEach((strategy) => expect(strategy.finishMock).not.toHaveBeenCalled());
            expect(eventBroker.publishMock).not.toHaveBeenCalled();
            expect(integrationEventPublisher.publishMock).not.toHaveBeenCalled();
            verify(queueService.publish(anything())).never();
        }

        async function testFinishByEmail(user: User): Promise<void> {
            const name = TestObjects.fullName();
            const pass = faker.helpers.arrayElement([await TestObjects.password(), undefined]);
            const type = faker.helpers.arrayElement([
                LocalAuthenticationMethod.EMAIL,
                SocialAuthenticationMethod.GOOGLE,
                SocialAuthenticationMethod.GITHUB,
            ]);

            when(userRepository.findByEmail(anything())).thenResolve(user);
            strategies.forEach((strategy) => strategy.finishMock.mockResolvedValueOnce({ user, event: {} }));

            const result = await finisher.finish(new Email(user.email), name, pass, type);

            expect(result).toEqual(user);
            strategies.forEach((strategy, role) => {
                if (role === user.role) {
                    expect(strategy.finishMock).toHaveBeenCalledWith(
                        new Id(user.id),
                        name,
                        pass,
                        type,
                        expect.anything(),
                    );
                } else {
                    expect(strategy.finishMock).not.toHaveBeenCalled();
                }
            });
            expect(eventBroker.publishMock).toHaveBeenCalledWith({});
            expect(integrationEventPublisher.publishMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    topic: configuration.topics.userEvents,
                    event: 'signup_completed',
                    message: new SignupCompletedEventPayload(user.role, user.id, user.email),
                }),
            );

            verify(
                queueService.publish(
                    deepEqual({
                        event: new SignupCompletedEvent({
                            userId: new Id(user.id),
                            role: user.role,
                        }),
                    }),
                ),
            ).once();
        }

        async function testFinishByToken(user: User): Promise<void> {
            const token = new SignupToken(faker.word.noun());
            const name = TestObjects.fullName();
            const pass = faker.helpers.arrayElement([await TestObjects.password(), undefined]);
            const type = faker.helpers.arrayElement([
                LocalAuthenticationMethod.EMAIL,
                SocialAuthenticationMethod.GOOGLE,
                SocialAuthenticationMethod.GITHUB,
            ]);

            when(userRepository.findByRegistrationToken(anything())).thenResolve(user);
            strategies.forEach((strategy) => strategy.finishMock.mockResolvedValueOnce({ user, event: {} }));

            const result = await finisher.finish(token, name, pass, type);

            expect(result).toEqual(user);
            strategies.forEach((strategy, role) => {
                if (role === user.role) {
                    expect(strategy.finishMock).toHaveBeenCalledWith(
                        new Id(user.id),
                        name,
                        pass,
                        type,
                        expect.anything(),
                    );
                } else {
                    expect(strategy.finishMock).not.toHaveBeenCalled();
                }
            });
            expect(eventBroker.publishMock).toHaveBeenCalledWith({});
            expect(integrationEventPublisher.publishMock).toHaveBeenCalledWith(
                expect.objectContaining({
                    topic: configuration.topics.userEvents,
                    event: 'signup_completed',
                    message: new SignupCompletedEventPayload(user.role, user.id, user.email),
                }),
            );

            verify(
                queueService.publish(
                    deepEqual({
                        event: new SignupCompletedEvent({
                            userId: new Id(user.id),
                            role: user.role,
                        }),
                    }),
                ),
            ).once();
        }

        describe('given user does not exist', () => {
            test('should not finish by email', async () => {
                await testNotFinishByEmail(undefined as unknown as User);
            });

            test('should not finish by token', async () => {
                await testNotFinishByToken(undefined as unknown as User);
            });
        });

        describe('given registration is inactive', () => {
            const user = new Student();
            user.email = TestObjects.email().value;
            user.state = UserState.ACTIVE;

            test('should not finish by email', async () => {
                await testNotFinishByEmail(user);
            });

            test('should not finish by token', async () => {
                await testNotFinishByToken(user);
            });
        });

        describe('given user is a student', () => {
            const user = new Student();
            user.id = TestObjects.uniqueId().value;
            user.role = Role.USER;
            user.email = TestObjects.email().value;
            user.state = UserState.REGISTERING;

            test('should finish by email', async () => {
                await testFinishByEmail(user);
            });

            test('should finish by token', async () => {
                await testFinishByToken(user);
            });
        });

        describe('given user is a mentor', () => {
            const user = new Mentor();
            user.id = TestObjects.uniqueId().value;
            user.role = Role.MENTOR;
            user.email = TestObjects.email().value;
            user.state = UserState.REGISTERING;

            test('should finish by email', async () => {
                await testFinishByEmail(user);
            });

            test('should finish by token', async () => {
                await testFinishByToken(user);
            });
        });

        describe('given user is a staff', () => {
            const user = new Staff();
            user.id = TestObjects.uniqueId().value;
            user.role = Role.STAFF;
            user.email = TestObjects.email().value;
            user.state = UserState.REGISTERING;

            test('should finish by email', async () => {
                await testFinishByEmail(user);
            });

            test('should finish by token', async () => {
                await testFinishByToken(user);
            });
        });

        describe('given user is an admin', () => {
            const user = new Staff();
            user.id = TestObjects.uniqueId().value;
            user.role = Role.ADMIN;
            user.email = TestObjects.email().value;
            user.state = UserState.REGISTERING;

            test('should finish by email', async () => {
                await testFinishByEmail(user);
            });

            test('should finish by token', async () => {
                await testFinishByToken(user);
            });
        });
    });

    describe('getActive', () => {
        test('should not get if user does not exist', async () => {
            when(userRepository.findByRegistrationToken(anything())).thenResolve(undefined);
            const result = await finisher.getActive(new SignupToken(faker.string.sample()));
            expect(result).toBeUndefined();
        });

        test('should not get if registration is inactive', async () => {
            const user = new Student();
            user.state = UserState.ACTIVE;
            when(userRepository.findByRegistrationToken(anything())).thenResolve(user);

            const result = await finisher.getActive(new SignupToken(faker.string.sample()));

            expect(result).toBeUndefined();
        });

        test('should get if registration is active', async () => {
            const user = getTestUser({ state: UserState.REGISTERING });
            when(userRepository.findByRegistrationToken(anything())).thenResolve(user);
            when(studentSettingsFinder.findByUserId(anything())).thenResolve({
                billingType: BillingType.SCHOLARSHIP,
            } as StudentSettings);

            const result = await finisher.getActive(new SignupToken(faker.string.sample()));

            expect(result).toEqual({
                role: user.role,
                email: new Email(user.email),
                userData: {
                    billingType: BillingType.SCHOLARSHIP,
                },
            });
        });
    });
});
