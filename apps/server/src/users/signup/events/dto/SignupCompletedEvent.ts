import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { Serialized } from '../../../../core/utils/Serialized';
import Id from '../../../../core/domain/value-objects/Id';
import { Role } from '../../../shared/infrastructure/db/User';

export type SignupCompletedEventParams = NonFunctionProperties<SignupCompletedEvent>;
export type SignupCompletedEventDto = Serialized<SignupCompletedEventParams>;

export class SignupCompletedEvent {
    readonly userId: Id;
    readonly role: Role;

    constructor(params: SignupCompletedEventParams) {
        Object.assign(this, params);
    }
}

export class SignupCompletedEventMapper {
    serialize(event: SignupCompletedEvent): SignupCompletedEventDto {
        return {
            userId: event.userId.value,
            role: event.role,
        };
    }

    deserialize(data: SignupCompletedEventDto): SignupCompletedEvent {
        return new SignupCompletedEvent({
            userId: new Id(data.userId),
            role: data.role,
        });
    }
}
