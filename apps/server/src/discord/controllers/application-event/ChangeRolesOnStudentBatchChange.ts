import BatchFinder from '../../../batches/services/BatchFinder';
import { Configuration } from '../../../config/Configuration';
import ApplicationEventBroker from '../../../events/application/ApplicationEventBroker';
import ApplicationEventSubscriber from '../../../events/application/ApplicationEventSubscriber';
import StudentBatchChangedEvent from '../../../users/students/settings/application-events/StudentBatchChangedEvent';
import DiscordHttpClient from '../../services/DiscordHttpClient';
import DiscordGuildId from '../../domain/DiscordGuildId';
import DiscordProfileRepository from '../../../discord-profile/infrastructure/db/discord-profile/DiscordProfileRepository';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

export default class ChangeRolesOnStudentBatchChange implements ApplicationEventSubscriber<StudentBatchChangedEvent> {
    readonly name = StudentBatchChangedEvent.name;

    private readonly guildId: DiscordGuildId;

    private readonly studentRoleId: DiscordGuildId;

    constructor(
        configuration: Configuration,
        private readonly batchFinder: BatchFinder,
        private readonly discordHttpClient: DiscordHttpClient,
        private readonly discordProfileRepository: DiscordProfileRepository,
    ) {
        this.guildId = new DiscordGuildId(configuration.discord.guildId);
        this.studentRoleId = new DiscordGuildId(configuration.discord.studentRoleId);
    }

    async handle({ payload: { studentId, oldBatchId, newBatchId } }: StudentBatchChangedEvent): Promise<void> {
        if (oldBatchId.equals(newBatchId)) {
            return;
        }

        const profile = await this.discordProfileRepository.getByUserId(studentId);

        if (!profile?.isAuthorized()) {
            return;
        }

        const existingMember = await this.discordHttpClient.getGuildMember(this.guildId, profile.data!.discordUserId!);

        if (!existingMember) {
            return;
        }

        const newBatch = await this.batchFinder.findById(newBatchId);
        const oldBatch = await this.batchFinder.findById(oldBatchId);

        ArgumentValidation.assert.defined(oldBatch, 'Old batch not found');

        const keepRoles = existingMember.roles.filter((role) => !role.equals(oldBatch.course.studentDiscordRoleId));

        ArgumentValidation.assert.defined(newBatch, 'New batch not found');

        await this.discordHttpClient.updateGuildMember(this.guildId, profile.data!.discordUserId!, {
            rolesIds: [...keepRoles, this.studentRoleId, newBatch.course.studentDiscordRoleId].filter(
                ArgumentValidation.is.defined,
            ),
        });
    }

    subscribeTo(broker: ApplicationEventBroker): void {
        broker.subscribe(StudentBatchChangedEvent.NAME, this);
    }
}
