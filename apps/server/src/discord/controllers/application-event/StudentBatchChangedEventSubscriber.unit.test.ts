import { faker } from '@faker-js/faker';
import { anything, deepEqual, instance, mock, objectContaining, verify, when } from 'ts-mockito';
import Batch from '../../../batches/domain/Batch';
import BatchName from '../../../batches/domain/BatchName';
import ChangeRolesOnStudentBatchChange from './ChangeRolesOnStudentBatchChange';
import { DiscordGuildMember } from '../../domain/DiscordGuildMember';
import DiscordProfileRepository from '../../../discord-profile/infrastructure/db/discord-profile/DiscordProfileRepository';
import DiscordRoleId from '../../domain/DiscordRoleId';
import DiscordUserId from '../../domain/DiscordUserId';
import DiscordHttpClient from '../../services/DiscordHttpClient';
import Course from '../../../education/courses/domain/Course';
import StudentBatchChangedEvent from '../../../users/students/settings/application-events/StudentBatchChangedEvent';
import Arrays from '../../../utils/Arrays';
import BatchFinderMock from '../../../test-toolkit/unit/deprecated-mocks/BatchFinderMock';
import { TestConfigurationService } from '../../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import { CourseOnboardingConnection } from '../../../education/courses/domain/CourseOnboardingConnection';
import { OAuthProfile } from '../../../core/domain/oauth/OAuthProfile';
import { DiscordOAuthProfileData } from '../../../discord-profile/domain/DiscordOAuthProfile';

describe('StudentBatchChangedEventSubscriber IT', () => {
    const studentId = TestObjects.uniqueId();
    const batchFinder = new BatchFinderMock();
    const discordHttpClientMock = mock<DiscordHttpClient>();
    const discordProfileRepositoryMock = mock<DiscordProfileRepository>();
    const subscriber = new ChangeRolesOnStudentBatchChange(
        TestConfigurationService.create().getConfigurationSync(),
        batchFinder,
        instance(discordHttpClientMock),
        instance(discordProfileRepositoryMock),
    );

    describe('handle', () => {
        let batches: Batch[] = [];

        beforeEach(() => {
            batches = [];
            batchFinder.findByIdMock.mockImplementation((id) => batches.find((b) => b.getIdOrThrow().equals(id)));
        });

        test('should not change roles if batches are the same', async () => {
            const batchId = TestObjects.uniqueId();

            await expect(
                subscriber.handle(
                    new StudentBatchChangedEvent({
                        studentId,
                        oldBatchId: batchId,
                        newBatchId: batchId,
                    }),
                ),
            ).resolves.toBeUndefined();

            verify(discordHttpClientMock.updateGuildMember(anything(), anything(), anything())).never();
        });

        test('should change batch and course roles', async () => {
            Arrays.stream(2).forEach(() => {
                batches.push(
                    new Batch({
                        course: new Course(
                            {
                                name: faker.word.noun(),
                                isEndorsementEnabled: false,
                                studentDiscordRoleId: new DiscordRoleId(TestObjects.uniqueSnowflake()),
                                mentorDiscordRoleId: new DiscordRoleId(TestObjects.uniqueSnowflake()),
                                onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                            },
                            TestObjects.uniqueId(),
                        ),
                        name: new BatchName(faker.word.noun()),
                        startDate: faker.date.past(),
                        endDate: faker.date.future(),
                        mentors: [],
                        id: TestObjects.uniqueId(),
                    }),
                );
            });
            const discordUserId = new DiscordUserId(TestObjects.snowflake());
            when(discordProfileRepositoryMock.getByUserId(anything())).thenResolve(
                OAuthProfile.create<DiscordOAuthProfileData>(studentId).completeAuthorization({
                    data: new DiscordOAuthProfileData(discordUserId),
                }),
            );
            const existingRoles = DiscordRoleId.fromArray([TestObjects.snowflake(), TestObjects.snowflake()]);
            when(discordHttpClientMock.getGuildMember(anything(), anything())).thenResolve({
                roles: existingRoles,
            } as DiscordGuildMember);

            await expect(
                subscriber.handle(
                    new StudentBatchChangedEvent({
                        studentId,
                        oldBatchId: batches[0].getIdOrThrow(),
                        newBatchId: batches[1].getIdOrThrow(),
                    }),
                ),
            ).resolves.toBeUndefined();

            verify(
                discordHttpClientMock.updateGuildMember(
                    anything(),
                    discordUserId,
                    deepEqual({
                        rolesIds: objectContaining([
                            ...existingRoles,
                            anything(),
                            batches[1].course.studentDiscordRoleId,
                        ]),
                    }),
                ),
            ).once();
        });
    });
});
