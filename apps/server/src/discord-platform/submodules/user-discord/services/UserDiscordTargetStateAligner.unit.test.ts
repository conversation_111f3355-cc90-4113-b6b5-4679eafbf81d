import { deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import { OAuthAccessToken } from '../../../../core/domain/oauth/OAuthAccessToken';
import { OAuthProfile } from '../../../../core/domain/oauth/OAuthProfile';
import { OAuthRefreshToken } from '../../../../core/domain/oauth/OAuthRefreshToken';
import { DiscordOAuthProfileData } from '../../../../discord-profile/domain/DiscordOAuthProfile';
import { DiscordProfileService } from '../../../../discord-profile/service/DiscordProfileService';
import DiscordGuildId from '../../../../discord/domain/DiscordGuildId';
import { DiscordGuildMember } from '../../../../discord/domain/DiscordGuildMember';
import DiscordNick from '../../../../discord/domain/DiscordNick';
import DiscordRoleId from '../../../../discord/domain/DiscordRoleId';
import DiscordUserId from '../../../../discord/domain/DiscordUserId';
import { NotificationType } from '../../../../notifications/domain/Notification';
import { Ref } from '../../../../test-toolkit/shared/Ref';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { UserAccountTestDataGenerator } from '../../../../users/accounts/mock/UserAccountTestDataGenerator';
import { UserAccountFinder } from '../../../../users/accounts/services/UserAccountFinder';
import Logger from '../../../../utils/logger/Logger';
import { DiscordServerRole } from '../../discord-server-role/domain/DiscordServerRole';
import { DiscordServer } from '../../discord-server/domain/DiscordServer';
import { DiscordServerType } from '../../discord-server/domain/DiscordServerType';
import { DiscordServerWithRoles } from '../../discord-server/domain/DiscordServerWithRoles';
import { DiscordState } from '../domain/DiscordState';
import { UserDiscordTargetStateAligner } from './UserDiscordTargetStateAligner';
import DiscordHttpClient from '../../../../discord/services/DiscordHttpClient';
import { NotificationManager } from '../../../../notifications/services/NotificationManager';
import { EncryptedObjectTestDataGenerator } from '../../../../data-encryption/mock/EncryptedObjectTestDataGenerator';
import { DataEncryptionService } from '../../../../data-encryption/services/DataEncryptionService';

describe(UserDiscordTargetStateAligner.name, () => {
    const discordHttpClientMock = mock<DiscordHttpClient>();
    const discordProfileServiceMock = mock<DiscordProfileService>();
    const userAccountFinderMock = mock<UserAccountFinder>();
    const notificationManagerMock = mock<NotificationManager>();
    const loggerMock = mock<Logger>();
    const dataEncryptionServiceMock = mock<DataEncryptionService>();

    const refreshToken = new OAuthRefreshToken('refresh-token');
    const encryptedRefreshToken = EncryptedObjectTestDataGenerator.generate(refreshToken.value);
    const accessToken = new OAuthAccessToken('access-token');

    const service = new UserDiscordTargetStateAligner(
        instance(discordHttpClientMock),
        instance(discordProfileServiceMock),
        instance(userAccountFinderMock),
        instance(notificationManagerMock),
        instance(loggerMock),
        instance(dataEncryptionServiceMock),
    );

    afterEach(() => {
        reset(discordHttpClientMock);
        reset(discordProfileServiceMock);
        reset(userAccountFinderMock);
        reset(notificationManagerMock);
    });

    beforeAll(() => {
        when(dataEncryptionServiceMock.decrypt(deepEqual(encryptedRefreshToken))).thenReturn(refreshToken.value);
    });

    describe.only('encryption performance', () => {
        it('should measure encryption and decryption performance for 2100 strings', () => {
            // Generate 2100 random strings (11 characters to simulate personal codes)
            const testStrings: string[] = [];
            for (let i = 0; i < 2100; i++) {
                testStrings.push(Math.random().toString(36).substring(2, 13)); // Random 11-char string
            }

            const encryptionService = EncryptedObjectTestDataGenerator.ENCRYPTION_SERVICE;

            // Measure encryption performance
            const encryptStartTime = performance.now();
            const encryptedData = testStrings.map((str) => encryptionService.encrypt(str));
            const encryptEndTime = performance.now();

            // Measure decryption performance
            const decryptStartTime = performance.now();
            const decryptedData = encryptedData.map((encrypted) => encryptionService.decrypt(encrypted));
            const decryptEndTime = performance.now();

            // Calculate performance metrics
            const encryptTotalTime = encryptEndTime - encryptStartTime;
            const decryptTotalTime = decryptEndTime - decryptStartTime;
            const encryptAvgTime = encryptTotalTime / testStrings.length;
            const decryptAvgTime = decryptTotalTime / testStrings.length;

            // Log performance results
            console.log('\n=== Encryption/Decryption Performance Results ===');
            console.log(`Total strings processed: ${testStrings.length}`);
            console.log(
                `Encryption - Total: ${encryptTotalTime.toFixed(2)} ms, Avg: ${encryptAvgTime.toFixed(4)} ms, Rate: ${(1000 / encryptAvgTime).toFixed(0)}/sec`,
            );
            console.log(
                `Decryption - Total: ${decryptTotalTime.toFixed(2)} ms, Avg: ${decryptAvgTime.toFixed(4)} ms, Rate: ${(1000 / decryptAvgTime).toFixed(0)}/sec`,
            );

            // Verify all operations completed successfully
            expect(encryptedData).toHaveLength(2100);
            expect(decryptedData).toHaveLength(2100);
            expect(decryptedData).toEqual(testStrings); // Verify round-trip integrity
        });
    });
});
