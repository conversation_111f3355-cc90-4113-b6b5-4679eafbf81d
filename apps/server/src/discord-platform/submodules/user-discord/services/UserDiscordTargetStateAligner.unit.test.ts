import { deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import { OAuthAccessToken } from '../../../../core/domain/oauth/OAuthAccessToken';
import { OAuthProfile } from '../../../../core/domain/oauth/OAuthProfile';
import { OAuthRefreshToken } from '../../../../core/domain/oauth/OAuthRefreshToken';
import { DiscordOAuthProfileData } from '../../../../discord-profile/domain/DiscordOAuthProfile';
import { DiscordProfileService } from '../../../../discord-profile/service/DiscordProfileService';
import DiscordGuildId from '../../../../discord/domain/DiscordGuildId';
import { DiscordGuildMember } from '../../../../discord/domain/DiscordGuildMember';
import DiscordNick from '../../../../discord/domain/DiscordNick';
import DiscordRoleId from '../../../../discord/domain/DiscordRoleId';
import DiscordUserId from '../../../../discord/domain/DiscordUserId';
import { NotificationType } from '../../../../notifications/domain/Notification';
import { Ref } from '../../../../test-toolkit/shared/Ref';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { UserAccountTestDataGenerator } from '../../../../users/accounts/mock/UserAccountTestDataGenerator';
import { UserAccountFinder } from '../../../../users/accounts/services/UserAccountFinder';
import Logger from '../../../../utils/logger/Logger';
import { DiscordServerRole } from '../../discord-server-role/domain/DiscordServerRole';
import { DiscordServer } from '../../discord-server/domain/DiscordServer';
import { DiscordServerType } from '../../discord-server/domain/DiscordServerType';
import { DiscordServerWithRoles } from '../../discord-server/domain/DiscordServerWithRoles';
import { DiscordState } from '../domain/DiscordState';
import { UserDiscordTargetStateAligner } from './UserDiscordTargetStateAligner';
import DiscordHttpClient from '../../../../discord/services/DiscordHttpClient';
import { NotificationManager } from '../../../../notifications/services/NotificationManager';
import { EncryptedObjectTestDataGenerator } from '../../../../data-encryption/mock/EncryptedObjectTestDataGenerator';
import { DataEncryptionService } from '../../../../data-encryption/services/DataEncryptionService';

describe(UserDiscordTargetStateAligner.name, () => {
    const discordHttpClientMock = mock<DiscordHttpClient>();
    const discordProfileServiceMock = mock<DiscordProfileService>();
    const userAccountFinderMock = mock<UserAccountFinder>();
    const notificationManagerMock = mock<NotificationManager>();
    const loggerMock = mock<Logger>();
    const dataEncryptionServiceMock = mock<DataEncryptionService>();

    const refreshToken = new OAuthRefreshToken('refresh-token');
    const encryptedRefreshToken = EncryptedObjectTestDataGenerator.generate(refreshToken.value);
    const accessToken = new OAuthAccessToken('access-token');

    const service = new UserDiscordTargetStateAligner(
        instance(discordHttpClientMock),
        instance(discordProfileServiceMock),
        instance(userAccountFinderMock),
        instance(notificationManagerMock),
        instance(loggerMock),
        instance(dataEncryptionServiceMock),
    );

    afterEach(() => {
        reset(discordHttpClientMock);
        reset(discordProfileServiceMock);
        reset(userAccountFinderMock);
        reset(notificationManagerMock);
    });

    beforeAll(() => {
        when(dataEncryptionServiceMock.decrypt(deepEqual(encryptedRefreshToken))).thenReturn(refreshToken.value);
    });

    describe(Ref.method<UserDiscordTargetStateAligner>('alignToTargetState'), () => {
        it('should send notification and do nothing when Discord profile is not authorized', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const targetState = DiscordState.empty();
            const deletedState = DiscordState.empty();
            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: undefined,
                data: undefined,
            });

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                notificationManagerMock.createNotification(
                    deepEqual({
                        description: 'We need you to re-authorise your Discord & join our server',
                        title: 'Authorise Discord',
                        type: NotificationType.AUTHORIZE_DISCORD,
                        userId: user.getIdOrThrow(),
                    }),
                ),
            ).once();
        });

        it('should send notification if Discord authorized, but has no refresh token and adding to new server', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const discordUserId = new DiscordUserId(TestObjects.snowflake());
            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: undefined,
                data: new DiscordOAuthProfileData(discordUserId),
            });

            const discordServerId = TestObjects.id();
            const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId = TestObjects.id();
            const externalDiscordRoleId = new DiscordRoleId(TestObjects.snowflake());

            const discordServer = DiscordServer.fromParams({
                id: discordServerId,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId,
                name: 'Test Server',
            });

            const discordRole = DiscordServerRole.fromParams({
                id: discordServerRoleId,
                discordServerId,
                externalDiscordRoleId,
                name: 'Test Role',
            });

            const targetState = DiscordState.fromParams({
                servers: [
                    DiscordServerWithRoles.fromParams({
                        ...discordServer,
                        roles: [discordRole],
                    }),
                ],
            });

            const deletedState = DiscordState.empty();

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);
            when(discordHttpClientMock.getGuildMember(externalDiscordServerId, discordUserId)).thenResolve(null);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                notificationManagerMock.createNotification(
                    deepEqual({
                        description: 'We need you to re-authorise your Discord & join our server',
                        title: 'Authorise Discord',
                        type: NotificationType.AUTHORIZE_DISCORD,
                        userId: user.getIdOrThrow(),
                    }),
                ),
            ).once();
        });

        it('should add new member to server with correct roles and nickname', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const discordUserId = new DiscordUserId(TestObjects.snowflake());
            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: encryptedRefreshToken,
                data: new DiscordOAuthProfileData(discordUserId),
            });

            const discordServerId = TestObjects.id();
            const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId = TestObjects.id();
            const externalDiscordRoleId = new DiscordRoleId(TestObjects.snowflake());

            const discordServer = DiscordServer.fromParams({
                id: discordServerId,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId,
                name: 'Test Server',
            });

            const discordRole = DiscordServerRole.fromParams({
                id: discordServerRoleId,
                discordServerId,
                externalDiscordRoleId,
                name: 'Test Role',
            });

            const targetState = DiscordState.fromParams({
                servers: [
                    DiscordServerWithRoles.fromParams({
                        ...discordServer,
                        roles: [discordRole],
                    }),
                ],
            });

            const deletedState = DiscordState.empty();

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);
            when(discordHttpClientMock.getGuildMember(externalDiscordServerId, discordUserId)).thenResolve(null);
            when(discordHttpClientMock.refreshAccessToken(deepEqual(refreshToken))).thenResolve(accessToken);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                discordHttpClientMock.addToGuild(
                    externalDiscordServerId,
                    discordUserId,
                    accessToken,
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [externalDiscordRoleId],
                    }),
                ),
            ).once();
        });

        it('should handle multiple servers in target state for new member', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const discordUserId = new DiscordUserId(TestObjects.snowflake());

            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: encryptedRefreshToken,
                data: new DiscordOAuthProfileData(discordUserId),
            });

            // First server setup
            const discordServerId1 = TestObjects.id();
            const externalDiscordServerId1 = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId1 = TestObjects.id();
            const externalDiscordRoleId1 = new DiscordRoleId(TestObjects.snowflake());

            const discordServer1 = DiscordServer.fromParams({
                id: discordServerId1,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId: externalDiscordServerId1,
                name: 'Test Server 1',
            });

            const discordRole1 = DiscordServerRole.fromParams({
                id: discordServerRoleId1,
                discordServerId: discordServerId1,
                externalDiscordRoleId: externalDiscordRoleId1,
                name: 'Test Role 1',
            });

            // Second server setup
            const discordServerId2 = TestObjects.id();
            const externalDiscordServerId2 = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId2 = TestObjects.id();
            const externalDiscordRoleId2 = new DiscordRoleId(TestObjects.snowflake());

            const discordServer2 = DiscordServer.fromParams({
                id: discordServerId2,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId: externalDiscordServerId2,
                name: 'Test Server 2',
            });

            const discordRole2 = DiscordServerRole.fromParams({
                id: discordServerRoleId2,
                discordServerId: discordServerId2,
                externalDiscordRoleId: externalDiscordRoleId2,
                name: 'Test Role 2',
            });

            const targetState = DiscordState.fromParams({
                servers: [
                    DiscordServerWithRoles.fromParams({
                        ...discordServer1,
                        roles: [discordRole1],
                    }),
                    DiscordServerWithRoles.fromParams({
                        ...discordServer2,
                        roles: [discordRole2],
                    }),
                ],
            });

            const deletedState = DiscordState.empty();

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);
            when(discordHttpClientMock.getGuildMember(externalDiscordServerId1, discordUserId)).thenResolve(null);
            when(discordHttpClientMock.getGuildMember(externalDiscordServerId2, discordUserId)).thenResolve(null);
            when(discordHttpClientMock.refreshAccessToken(deepEqual(refreshToken))).thenResolve(accessToken);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                discordHttpClientMock.addToGuild(
                    externalDiscordServerId1,
                    discordUserId,
                    accessToken,
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [externalDiscordRoleId1],
                    }),
                ),
            ).once();

            verify(
                discordHttpClientMock.addToGuild(
                    externalDiscordServerId2,
                    discordUserId,
                    accessToken,
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [externalDiscordRoleId2],
                    }),
                ),
            ).once();
        });

        it('should handle platform roles removal when delete state is provided', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const discordUserId = new DiscordUserId(TestObjects.snowflake());
            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: EncryptedObjectTestDataGenerator.generate(),
                data: new DiscordOAuthProfileData(discordUserId),
            });

            const discordServerId = TestObjects.id();
            const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId = TestObjects.id();
            const externalDiscordRoleId = new DiscordRoleId(TestObjects.snowflake());

            const discordServer = DiscordServer.fromParams({
                id: discordServerId,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId,
                name: 'Test Server',
            });

            const discordRole = DiscordServerRole.fromParams({
                id: discordServerRoleId,
                discordServerId,
                externalDiscordRoleId,
                name: 'Test Role',
            });

            const targetState = DiscordState.empty();
            const deletedState = DiscordState.fromParams({
                servers: [
                    DiscordServerWithRoles.fromParams({
                        ...discordServer,
                        roles: [discordRole],
                    }),
                ],
            });

            const serverMember = new DiscordGuildMember({
                id: discordUserId,
                nick: new DiscordNick('old-nick'),
                roles: [externalDiscordRoleId],
            });

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);
            when(
                discordHttpClientMock.getGuildMember(deepEqual(externalDiscordServerId), deepEqual(discordUserId)),
            ).thenResolve(serverMember);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                discordHttpClientMock.updateGuildMember(
                    deepEqual(externalDiscordServerId),
                    deepEqual(discordUserId),
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [],
                    }),
                ),
            ).once();
        });

        it('should handle multiple servers in target state for existing member', async () => {
            // Arrange
            const user = UserAccountTestDataGenerator.activeLearner();
            const discordUserId = new DiscordUserId(TestObjects.snowflake());
            const discordProfile = new OAuthProfile<DiscordOAuthProfileData>({
                id: TestObjects.id(),
                userId: user.getIdOrThrow(),
                refreshToken: EncryptedObjectTestDataGenerator.generate(),
                data: new DiscordOAuthProfileData(discordUserId),
            });

            // First server setup
            const discordServerId1 = TestObjects.id();
            const externalDiscordServerId1 = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId1 = TestObjects.id();
            const externalDiscordRoleId1 = new DiscordRoleId(TestObjects.snowflake());

            const discordServer1 = DiscordServer.fromParams({
                id: discordServerId1,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId: externalDiscordServerId1,
                name: 'Test Server 1',
            });

            const discordRole1 = DiscordServerRole.fromParams({
                id: discordServerRoleId1,
                discordServerId: discordServerId1,
                externalDiscordRoleId: externalDiscordRoleId1,
                name: 'Test Role 1',
            });

            // Second server setup
            const discordServerId2 = TestObjects.id();
            const externalDiscordServerId2 = new DiscordGuildId(TestObjects.snowflake());
            const discordServerRoleId2 = TestObjects.id();
            const externalDiscordRoleId2 = new DiscordRoleId(TestObjects.snowflake());

            const discordServer2 = DiscordServer.fromParams({
                id: discordServerId2,
                type: DiscordServerType.TURING_COLLEGE,
                externalDiscordServerId: externalDiscordServerId2,
                name: 'Test Server 2',
            });

            const discordRole2 = DiscordServerRole.fromParams({
                id: discordServerRoleId2,
                discordServerId: discordServerId2,
                externalDiscordRoleId: externalDiscordRoleId2,
                name: 'Test Role 2',
            });

            const targetState = DiscordState.fromParams({
                servers: [
                    DiscordServerWithRoles.fromParams({
                        ...discordServer1,
                        roles: [discordRole1],
                    }),
                    DiscordServerWithRoles.fromParams({
                        ...discordServer2,
                        roles: [discordRole2],
                    }),
                ],
            });

            const deletedState = DiscordState.empty();

            const customRoleId1 = new DiscordRoleId(TestObjects.snowflake());
            const serverMember1 = new DiscordGuildMember({
                id: discordUserId,
                nick: new DiscordNick('old-nick'),
                roles: [customRoleId1],
            });

            const customRoleId2 = new DiscordRoleId(TestObjects.snowflake());
            const serverMember2 = new DiscordGuildMember({
                id: discordUserId,
                nick: new DiscordNick('old-nick'),
                roles: [customRoleId2],
            });

            when(userAccountFinderMock.findByUserId(user.getIdOrThrow())).thenResolve(user);
            when(discordProfileServiceMock.getByUserId(user.getIdOrThrow())).thenResolve(discordProfile);
            when(
                discordHttpClientMock.getGuildMember(deepEqual(externalDiscordServerId1), deepEqual(discordUserId)),
            ).thenResolve(serverMember1);
            when(
                discordHttpClientMock.getGuildMember(deepEqual(externalDiscordServerId2), deepEqual(discordUserId)),
            ).thenResolve(serverMember2);

            // Act
            await service.alignToTargetState({
                targetState,
                deletedState,
                userId: user.getIdOrThrow(),
            });

            // Assert
            verify(
                discordHttpClientMock.updateGuildMember(
                    deepEqual(externalDiscordServerId1),
                    deepEqual(discordUserId),
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [customRoleId1, externalDiscordRoleId1],
                    }),
                ),
            ).once();

            verify(
                discordHttpClientMock.updateGuildMember(
                    deepEqual(externalDiscordServerId2),
                    deepEqual(discordUserId),
                    deepEqual({
                        nick: DiscordNick.fromUserAccount(user),
                        rolesIds: [customRoleId2, externalDiscordRoleId2],
                    }),
                ),
            ).once();
        });
    });
});
