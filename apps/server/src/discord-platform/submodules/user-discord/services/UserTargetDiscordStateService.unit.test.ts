import { UserTargetDiscordStateService } from './UserTargetDiscordStateService';
import { Ref } from '../../../../test-toolkit/shared/Ref';
import { UserAccountFinder } from '../../../../users/accounts/services/UserAccountFinder';
import { CourseDiscordServerService } from '../../course-discord/services/CourseDiscordServerService';
import { CourseDiscordServerRoleService } from '../../course-discord/services/CourseDiscordServerRoleService';
import { BatchDiscordServerRoleService } from '../../batch-discord/services/BatchDiscordServerRoleService';
import { BatchDiscordServerService } from '../../batch-discord/services/BatchDiscordServerService';
import { MentorAssignedCourseService } from '../../../../mentor-assigned-course/services/MentorAssingedCourseService';
import { DiscordServerService } from '../../discord-server/services/DiscordServerService';
import { DiscordServerRoleService } from '../../discord-server-role/services/DiscordServerRoleService';
import BatchFinder from '../../../../batches/services/BatchFinder';
import { StudentSettingsFinder } from '../../../../users/students/settings/services/StudentSettingsFinder';
import { deepEqual, instance, mock, reset, when } from 'ts-mockito';
import { DiscordState } from '../domain/DiscordState';
import { UserAccountTestDataGenerator } from '../../../../users/accounts/mock/UserAccountTestDataGenerator';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { MentorAssignedCourse } from '../../../../users/mentors/domain/MentorAssignedCourse';
import { CourseDiscordServerRole } from '../../course-discord/domain/CourseDiscordServerRole';
import { CourseDiscordServer } from '../../course-discord/domain/CourseDiscordServer';
import { DiscordServer } from '../../discord-server/domain/DiscordServer';
import { DiscordServerRole } from '../../discord-server-role/domain/DiscordServerRole';
import { DiscordServerWithRoles } from '../../discord-server/domain/DiscordServerWithRoles';
import { DiscordServerType } from '../../discord-server/domain/DiscordServerType';
import DiscordGuildId from '../../../../discord/domain/DiscordGuildId';
import DiscordRoleId from '../../../../discord/domain/DiscordRoleId';
import { getTestStudentSettings } from '../../../../test-toolkit/shared/studentSettingsHelper';
import { CourseTestDataGenerator } from '../../../../education/courses/mock/CourseTestDataGenerator';
import { BatchTestDataGenerator } from '../../../../batches/mock/BatchTestDataGenerator';
import { BatchDiscordServerRole } from '../../batch-discord/domain/BatchDiscordServerRole';
import { BatchDiscordServerRoleType } from '../../batch-discord/domain/BatchDiscordServerRoleType';
import { BillingType } from '../../../../users/students/settings/domain/BillingType';
import { BatchDiscordServer } from '../../batch-discord/domain/BatchDiscordServer';

describe(UserTargetDiscordStateService.name, () => {
    const userAccountFinderMock = mock<UserAccountFinder>();
    const courseDiscordServerServiceMock = mock<CourseDiscordServerService>();
    const courseDiscordServerRoleServiceMock = mock<CourseDiscordServerRoleService>();
    const batchDiscordServerRoleServiceMock = mock<BatchDiscordServerRoleService>();
    const batchDiscordServerServiceMock = mock<BatchDiscordServerService>();
    const mentorAssignedCourseServiceMock = mock<MentorAssignedCourseService>();
    const discordServerServiceMock = mock<DiscordServerService>();
    const discordServerRoleServiceMock = mock<DiscordServerRoleService>();
    const batchFinderMock = mock<BatchFinder>();
    const studentSettingsFinderMock = mock<StudentSettingsFinder>();

    const service = new UserTargetDiscordStateService(
        instance(userAccountFinderMock),
        instance(courseDiscordServerServiceMock),
        instance(courseDiscordServerRoleServiceMock),
        instance(batchDiscordServerServiceMock),
        instance(batchDiscordServerRoleServiceMock),
        instance(mentorAssignedCourseServiceMock),
        instance(discordServerServiceMock),
        instance(discordServerRoleServiceMock),
        instance(studentSettingsFinderMock),
    );

    afterEach(() => {
        reset(userAccountFinderMock);
        reset(courseDiscordServerServiceMock);
        reset(courseDiscordServerRoleServiceMock);
        reset(batchDiscordServerRoleServiceMock);
        reset(batchDiscordServerServiceMock);
        reset(mentorAssignedCourseServiceMock);
        reset(discordServerServiceMock);
        reset(discordServerRoleServiceMock);
        reset(batchFinderMock);
        reset(studentSettingsFinderMock);
    });

    describe(Ref.method<UserTargetDiscordStateService>('getUserTargetDiscordState'), () => {
        it('should return empty state for admin user', async () => {
            // Arrange
            const adminAccount = UserAccountTestDataGenerator.admin();
            when(userAccountFinderMock.findByUserId(adminAccount.getIdOrThrow())).thenResolve(adminAccount);

            // Act
            const result = await service.getUserTargetDiscordState(adminAccount.getIdOrThrow());

            // Assert
            expect(result).toEqual(DiscordState.empty());
        });

        it('should return empty state for staff user', async () => {
            // Arrange
            const staffAccount = UserAccountTestDataGenerator.staff();
            when(userAccountFinderMock.findByUserId(staffAccount.getIdOrThrow())).thenResolve(staffAccount);

            // Act
            const result = await service.getUserTargetDiscordState(staffAccount.getIdOrThrow());

            // Assert
            expect(result).toEqual(DiscordState.empty());
        });

        describe('for mentor user', () => {
            it('should return empty state when mentor has no assigned courses', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve([]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                expect(result).toEqual(DiscordState.empty());
            });

            it('should return empty state when assigned courses have no Discord servers or roles', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId = TestObjects.id();
                const assignedCourse = new MentorAssignedCourse({
                    id: TestObjects.id(),
                    userId: mentorAccount.getIdOrThrow(),
                    courseId,
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve([
                    assignedCourse,
                ]);
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId]))).thenResolve(
                    [],
                );
                when(
                    courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId])),
                ).thenResolve([]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                expect(result).toEqual(DiscordState.empty());
            });

            it('should throw error when courses have roles but no servers', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId = TestObjects.id();
                const assignedCourse = new MentorAssignedCourse({
                    id: TestObjects.id(),
                    userId: mentorAccount.getIdOrThrow(),
                    courseId,
                });
                const discordServerRoleId = TestObjects.id();
                const courseDiscordRole = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId,
                    discordServerRoleId: discordServerRoleId,
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve([
                    assignedCourse,
                ]);
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId]))).thenResolve(
                    [],
                );
                when(
                    courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId])),
                ).thenResolve([courseDiscordRole]);

                // Act & Assert
                await expect(service.getUserTargetDiscordState(mentorAccount.getIdOrThrow())).rejects.toThrow(
                    'Discord roles linked to course but no servers linked to course',
                );
            });

            it('should return correct Discord state when mentor has assigned courses with servers and roles', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId = TestObjects.id();
                const assignedCourse = new MentorAssignedCourse({
                    id: TestObjects.id(),
                    userId: mentorAccount.getIdOrThrow(),
                    courseId,
                });

                const discordServerId = TestObjects.id();
                const discordServerRoleId = TestObjects.id();
                const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
                const externalDiscordRoleId = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId,
                    discordServerId,
                });

                const courseDiscordRole = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId,
                    discordServerRoleId: discordServerRoleId,
                });

                const discordServer = DiscordServer.fromParams({
                    id: discordServerId,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId,
                    name: 'Test Server',
                });

                const discordRole = DiscordServerRole.fromParams({
                    id: discordServerRoleId,
                    discordServerId,
                    externalDiscordRoleId,
                    name: 'Test Role',
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve([
                    assignedCourse,
                ]);
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId]))).thenResolve([
                    courseDiscordServer,
                ]);
                when(
                    courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([assignedCourse.courseId])),
                ).thenResolve([courseDiscordRole]);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId]))).thenResolve([discordServer]);
                when(discordServerRoleServiceMock.getByIds(deepEqual([discordServerRoleId]))).thenResolve([
                    discordRole,
                ]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                const expectedState = DiscordState.fromParams({
                    servers: [
                        DiscordServerWithRoles.fromParams({
                            ...discordServer,
                            roles: [discordRole],
                        }),
                    ],
                });
                expect(result).toEqual(expectedState);
            });

            it('should handle multiple assigned courses with different Discord servers and roles', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId1 = TestObjects.id();
                const courseId2 = TestObjects.id();
                const assignedCourses = [
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId1,
                    }),
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId2,
                    }),
                ];

                // First course's Discord server and role
                const discordServerId1 = TestObjects.id();
                const discordServerRoleId1 = TestObjects.id();
                const externalDiscordServerId1 = new DiscordGuildId(TestObjects.snowflake());
                const externalDiscordRoleId1 = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer1 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerId: discordServerId1,
                });

                const courseDiscordRole1 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerRoleId: discordServerRoleId1,
                });

                const discordServer1 = DiscordServer.fromParams({
                    id: discordServerId1,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId: externalDiscordServerId1,
                    name: 'Test Server 1',
                });

                const discordRole1 = DiscordServerRole.fromParams({
                    id: discordServerRoleId1,
                    discordServerId: discordServerId1,
                    externalDiscordRoleId: externalDiscordRoleId1,
                    name: 'Test Role 1',
                });

                // Second course's Discord server and role
                const discordServerId2 = TestObjects.id();
                const discordServerRoleId2 = TestObjects.id();
                const externalDiscordServerId2 = new DiscordGuildId(TestObjects.snowflake());
                const externalDiscordRoleId2 = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer2 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerId: discordServerId2,
                });

                const courseDiscordRole2 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerRoleId: discordServerRoleId2,
                });

                const discordServer2 = DiscordServer.fromParams({
                    id: discordServerId2,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId: externalDiscordServerId2,
                    name: 'Test Server 2',
                });

                const discordRole2 = DiscordServerRole.fromParams({
                    id: discordServerRoleId2,
                    discordServerId: discordServerId2,
                    externalDiscordRoleId: externalDiscordRoleId2,
                    name: 'Test Role 2',
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(
                    assignedCourses,
                );
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordServer1,
                    courseDiscordServer2,
                ]);
                when(courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordRole1,
                    courseDiscordRole2,
                ]);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId1, discordServerId2]))).thenResolve([
                    discordServer1,
                    discordServer2,
                ]);
                when(
                    discordServerRoleServiceMock.getByIds(deepEqual([discordServerRoleId1, discordServerRoleId2])),
                ).thenResolve([discordRole1, discordRole2]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                const expectedState = DiscordState.fromParams({
                    servers: [
                        DiscordServerWithRoles.fromParams({
                            ...discordServer1,
                            roles: [discordRole1],
                        }),
                        DiscordServerWithRoles.fromParams({
                            ...discordServer2,
                            roles: [discordRole2],
                        }),
                    ],
                });
                expect(result).toEqual(expectedState);
            });

            it('should handle multiple assigned courses with same Discord servers, but different roles', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId1 = TestObjects.id();
                const courseId2 = TestObjects.id();
                const assignedCourses = [
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId1,
                    }),
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId2,
                    }),
                ];

                // Shared Discord server
                const discordServerId = TestObjects.id();
                const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());

                const discordServer = DiscordServer.fromParams({
                    id: discordServerId,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId,
                    name: 'Test Server',
                });

                // First course's Discord role
                const discordServerRoleId1 = TestObjects.id();
                const externalDiscordRoleId1 = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer1 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerId,
                });

                const courseDiscordRole1 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerRoleId: discordServerRoleId1,
                });

                const discordRole1 = DiscordServerRole.fromParams({
                    id: discordServerRoleId1,
                    discordServerId,
                    externalDiscordRoleId: externalDiscordRoleId1,
                    name: 'Test Role 1',
                });

                // Second course's Discord role
                const discordServerRoleId2 = TestObjects.id();
                const externalDiscordRoleId2 = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer2 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerId,
                });

                const courseDiscordRole2 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerRoleId: discordServerRoleId2,
                });

                const discordRole2 = DiscordServerRole.fromParams({
                    id: discordServerRoleId2,
                    discordServerId,
                    externalDiscordRoleId: externalDiscordRoleId2,
                    name: 'Test Role 2',
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(
                    assignedCourses,
                );
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordServer1,
                    courseDiscordServer2,
                ]);
                when(courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordRole1,
                    courseDiscordRole2,
                ]);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId]))).thenResolve([discordServer]);
                when(
                    discordServerRoleServiceMock.getByIds(deepEqual([discordServerRoleId1, discordServerRoleId2])),
                ).thenResolve([discordRole1, discordRole2]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                const expectedState = DiscordState.fromParams({
                    servers: [
                        DiscordServerWithRoles.fromParams({
                            ...discordServer,
                            roles: [discordRole1, discordRole2],
                        }),
                    ],
                });
                expect(result).toEqual(expectedState);
            });

            it('should handle multiple assigned courses with same Discord servers and same roles', async () => {
                // Arrange
                const mentorAccount = UserAccountTestDataGenerator.mentor();
                const courseId1 = TestObjects.id();
                const courseId2 = TestObjects.id();
                const assignedCourses = [
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId1,
                    }),
                    new MentorAssignedCourse({
                        id: TestObjects.id(),
                        userId: mentorAccount.getIdOrThrow(),
                        courseId: courseId2,
                    }),
                ];

                // Shared Discord server
                const discordServerId = TestObjects.id();
                const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());

                const discordServer = DiscordServer.fromParams({
                    id: discordServerId,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId,
                    name: 'Test Server',
                });

                // Shared Discord role
                const discordServerRoleId = TestObjects.id();
                const externalDiscordRoleId = new DiscordRoleId(TestObjects.snowflake());

                const courseDiscordServer1 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerId,
                });

                const courseDiscordServer2 = CourseDiscordServer.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerId,
                });

                const courseDiscordRole1 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId1,
                    discordServerRoleId: discordServerRoleId,
                });

                const courseDiscordRole2 = CourseDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    courseId: courseId2,
                    discordServerRoleId: discordServerRoleId,
                });

                const discordRole = DiscordServerRole.fromParams({
                    id: discordServerRoleId,
                    discordServerId,
                    externalDiscordRoleId,
                    name: 'Test Role',
                });

                when(userAccountFinderMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(mentorAccount);
                when(mentorAssignedCourseServiceMock.findByUserId(mentorAccount.getIdOrThrow())).thenResolve(
                    assignedCourses,
                );
                when(courseDiscordServerServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordServer1,
                    courseDiscordServer2,
                ]);
                when(courseDiscordServerRoleServiceMock.getByCourseIds(deepEqual([courseId1, courseId2]))).thenResolve([
                    courseDiscordRole1,
                    courseDiscordRole2,
                ]);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId]))).thenResolve([discordServer]);
                when(discordServerRoleServiceMock.getByIds(deepEqual([discordServerRoleId]))).thenResolve([
                    discordRole,
                ]);

                // Act
                const result = await service.getUserTargetDiscordState(mentorAccount.getIdOrThrow());

                // Assert
                const expectedState = DiscordState.fromParams({
                    servers: [
                        DiscordServerWithRoles.fromParams({
                            ...discordServer,
                            roles: [discordRole],
                        }),
                    ],
                });
                expect(result).toEqual(expectedState);
            });
        });

        describe('for learner user', () => {
            it('should throw error when learner settings are not found', async () => {
                // Arrange
                const learnerAccount = UserAccountTestDataGenerator.activeLearner();
                when(userAccountFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(learnerAccount);
                when(studentSettingsFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(undefined);

                // Act & Assert
                await expect(service.getUserTargetDiscordState(learnerAccount.getIdOrThrow())).rejects.toThrow(
                    'Learner settings not found',
                );
            });

            it('should return empty state when batch has no server', async () => {
                // Arrange
                const learnerAccount = UserAccountTestDataGenerator.activeLearner();
                const batchId = TestObjects.id();
                const courseId = TestObjects.id();
                const studentSettings = getTestStudentSettings({
                    studentId: learnerAccount.getIdOrThrow(),
                    batchId,
                });

                const course = CourseTestDataGenerator.create({
                    id: courseId,
                });
                const batch = BatchTestDataGenerator.create({
                    id: batchId,
                    course,
                });

                when(userAccountFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(learnerAccount);
                when(studentSettingsFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(
                    studentSettings,
                );
                when(batchFinderMock.findById(batchId)).thenResolve(batch);
                when(batchDiscordServerServiceMock.getByBatchId(batchId)).thenResolve(undefined);
                when(
                    batchDiscordServerRoleServiceMock.getByBatchAndTypes(
                        batchId,
                        deepEqual([BatchDiscordServerRoleType.DFE, BatchDiscordServerRoleType.EVERYONE]),
                    ),
                ).thenResolve([]);
                when(discordServerRoleServiceMock.getByIds(deepEqual([]))).thenResolve([]);

                // Act
                const result = await service.getUserTargetDiscordState(learnerAccount.getIdOrThrow());

                // Assert
                expect(result).toEqual(
                    DiscordState.fromParams({
                        servers: [],
                    }),
                );
            });

            it('should return state with single server when batch has no roles', async () => {
                // Arrange
                const learnerAccount = UserAccountTestDataGenerator.activeLearner();
                const batchId = TestObjects.id();
                const courseId = TestObjects.id();
                const studentSettings = getTestStudentSettings({
                    studentId: learnerAccount.getIdOrThrow(),
                    batchId,
                    billingType: BillingType.DFE,
                });

                const course = CourseTestDataGenerator.create({
                    id: courseId,
                });
                const batch = BatchTestDataGenerator.create({
                    id: batchId,
                    course,
                });

                const discordServerId = TestObjects.id();
                const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
                const batchDiscordServer = BatchDiscordServer.fromParams({
                    id: TestObjects.id(),
                    batchId,
                    discordServerId,
                });

                const discordServer = DiscordServer.fromParams({
                    id: discordServerId,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId,
                    name: 'Test Server',
                });

                when(userAccountFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(learnerAccount);
                when(studentSettingsFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(
                    studentSettings,
                );
                when(batchFinderMock.findById(batchId)).thenResolve(batch);
                when(batchDiscordServerServiceMock.getByBatchId(batchId)).thenResolve(batchDiscordServer);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId]))).thenResolve([discordServer]);
                when(
                    batchDiscordServerRoleServiceMock.getByBatchAndTypes(
                        batchId,
                        deepEqual([BatchDiscordServerRoleType.DFE, BatchDiscordServerRoleType.EVERYONE]),
                    ),
                ).thenResolve([]);
                when(discordServerRoleServiceMock.getByIds(deepEqual([]))).thenResolve([]);

                // Act
                const result = await service.getUserTargetDiscordState(learnerAccount.getIdOrThrow());

                // Assert
                expect(result).toEqual(
                    DiscordState.fromParams({
                        servers: [
                            DiscordServerWithRoles.fromParams({
                                ...discordServer,
                                roles: [],
                            }),
                        ],
                    }),
                );
            });

            it('should return correct Discord state when batch has server and roles', async () => {
                // Arrange
                const learnerAccount = UserAccountTestDataGenerator.activeLearner();
                const batchId = TestObjects.id();
                const courseId = TestObjects.id();
                const studentSettings = getTestStudentSettings({
                    studentId: learnerAccount.getIdOrThrow(),
                    batchId,
                    billingType: BillingType.UZT,
                });

                const course = CourseTestDataGenerator.create({ id: courseId });
                const batch = BatchTestDataGenerator.create({
                    id: batchId,
                    course,
                });

                const discordServerId = TestObjects.id();
                const externalDiscordServerId = new DiscordGuildId(TestObjects.snowflake());
                const batchDiscordServer = BatchDiscordServer.fromParams({
                    id: TestObjects.id(),
                    batchId,
                    discordServerId,
                });

                const discordServer = DiscordServer.fromParams({
                    id: discordServerId,
                    type: DiscordServerType.TURING_COLLEGE,
                    externalDiscordServerId,
                    name: 'Test Server',
                });

                const discordRoleA = DiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    discordServerId,
                    externalDiscordRoleId: new DiscordRoleId(TestObjects.snowflake()),
                    name: 'Test Role',
                });

                const batchDiscordRoleForEveryone = BatchDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    batchId,
                    discordServerRoleId: discordRoleA.getIdOrThrow(),
                    type: BatchDiscordServerRoleType.EVERYONE,
                });

                const discordRoleB = DiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    discordServerId,
                    externalDiscordRoleId: new DiscordRoleId(TestObjects.snowflake()),
                    name: 'Test Role',
                });

                const batchDiscordRoleForUzt = BatchDiscordServerRole.fromParams({
                    id: TestObjects.id(),
                    batchId,
                    discordServerRoleId: discordRoleB.getIdOrThrow(),
                    type: BatchDiscordServerRoleType.EVERYONE,
                });

                when(userAccountFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(learnerAccount);
                when(studentSettingsFinderMock.findByUserId(learnerAccount.getIdOrThrow())).thenResolve(
                    studentSettings,
                );
                when(batchFinderMock.findById(batchId)).thenResolve(batch);
                when(batchDiscordServerServiceMock.getByBatchId(batchId)).thenResolve(batchDiscordServer);
                when(discordServerServiceMock.getByIds(deepEqual([discordServerId]))).thenResolve([discordServer]);
                when(
                    batchDiscordServerRoleServiceMock.getByBatchAndTypes(
                        batchId,
                        deepEqual([BatchDiscordServerRoleType.UZT, BatchDiscordServerRoleType.EVERYONE]),
                    ),
                ).thenResolve([batchDiscordRoleForEveryone, batchDiscordRoleForUzt]);
                when(
                    discordServerRoleServiceMock.getByIds(
                        deepEqual([discordRoleA.getIdOrThrow(), discordRoleB.getIdOrThrow()]),
                    ),
                ).thenResolve([discordRoleA, discordRoleB]);

                // Act
                const result = await service.getUserTargetDiscordState(learnerAccount.getIdOrThrow());

                // Assert
                expect(result).toEqual(
                    DiscordState.fromParams({
                        servers: [
                            DiscordServerWithRoles.fromParams({
                                ...discordServer,
                                roles: [discordRoleA, discordRoleB],
                            }),
                        ],
                    }),
                );
            });
        });
    });
});
