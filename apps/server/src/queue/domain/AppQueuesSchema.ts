import {
    SendTemplatedEmailCommand,
    SendTemplatedEmailCommandMapper,
} from '../../mailer/controllers/commands/dto/SendTemplatedEmailCommand';
import {
    SuspendLearnerCommand,
    SuspendLearnerCommandMapper,
} from '../../users/students/management/controllers/commands/dto/SuspendLearnerCommand';
import {
    UserStateChangedEvent,
    UserStateChangedEventMapper,
} from '../../users/accounts/controllers/events/dto/UserStateChangedEvent';
import { QueuesSchemaCommandQueueDefinition, QueuesSchemaEventQueueDefinition } from './QueuesSchemaDefinition';
import {
    LearnerSuspendedNotifyByEmailCommand,
    LearnerSuspendedNotifyByEmailCommandMapper,
} from '../../users/students/management/controllers/commands/dto/LearnerSuspendedNotifyByEmailCommand';
import {
    LearnerSuspendedNotifyByPlatformCommand,
    LearnerSuspendedNotifyByPlatformCommandMapper,
} from '../../users/students/management/controllers/commands/dto/LearnerSuspendedNotifyByPlatformCommand';
import {
    LearnerUpcomingSuspensionNotifyByEmailCommand,
    LearnerUpcomingSuspensionNotifyByEmailCommandMapper,
} from '../../users/students/management/controllers/commands/dto/LearnerUpcomingSuspensionNotifyByEmailCommand';
import {
    LearnerUpcomingSuspensionNotifyByPlatformCommand,
    LearnerUpcomingSuspensionNotifyByPlatformCommandMapper,
} from '../../users/students/management/controllers/commands/dto/LearnerUpcomingSuspensionNotifyByPlatformCommand';
import {
    NotifyLearnerAboutUpcomingSuspensionCommand,
    NotifyLearnerAboutUpcomingSuspensionCommandMapper,
} from '../../users/students/management/controllers/commands/dto/NotifyLearnerAboutUpcomingSuspensionCommand';
import {
    RescheduleLearnerSuspensionCommand,
    RescheduleLearnerSuspensionCommandMapper,
} from '../../users/students/management/controllers/commands/dto/RescheduleLearnerSuspensionCommandWorker';
import {
    UserLinkedGithubAccountChangedEvent,
    UserLinkedGithubAccountChangedEventMapper,
} from '../../github/controllers/events/dto/UserLinkedGithubAccountChangedEvent';
import {
    RefreshPendingPreconditionsCommand,
    RefreshPendingPreconditionsCommandMapper,
} from '../../learning/submodules/roadmap/controllers/commands/dto/RefreshPendingPreconditionsCommand';
import {
    GithubRemoveFromOrganizationCommand,
    GithubRemoveFromOrganizationCommandMapper,
} from '../../github/controllers/commands/dto/GithubRemoveFromOrganizationCommand';
import {
    GrantAccessToSubmissionCommand,
    GrantAccessToSubmissionMapper,
} from '../../learning/submodules/roadmap/controllers/commands/dto/GrantAccessToSubmissionCommand';
import {
    GrantAccessToAllSubmissionsCommand,
    GrantAccessToAllSubmissionsCommandMapper,
} from '../../learning/submodules/roadmap/controllers/commands/dto/GrantAccessToAllSubmissionsCommand';
import {
    UserLinkedGoogleAccountChangedEvent,
    UserLinkedGoogleAccountChangedEventMapper,
} from '../../google-account/controllers/events/dto/UserLinkedGoogleAccountChangedEvent';
import {
    GoogleDriveRemoveAccessToAllEntriesCommand,
    GoogleDriveRemoveAccessToAllEntriesCommandMapper,
} from '../../google-drive/controllers/commands/dto/GoogleDriveRemoveAccessToAllEntriesCommand';
import {
    GoogleDriveRemovePermissionCommand,
    GoogleDriveRemovePermissionCommandMapper,
} from '../../google-drive/controllers/commands/dto/GoogleDriveRemovePermissionCommand';
import {
    UserLinkedDiscordAccountChangedEvent,
    UserLinkedDiscordAccountChangedEventMapper,
} from '../../discord/controllers/events/dto/UserLinkedDiscordAccountChangedEvent';
import {
    DiscordRemoveFromGuildCommand,
    DiscordRemoveFromGuildCommandMapper,
} from '../../discord/controllers/commands/dto/DiscordRemoveFromGuildCommand';
import {
    GrantAccessToAllSuggestedSolutionsCommand,
    GrantAccessToAllSuggestedSolutionsCommandMapper,
} from '../../learning/submodules/roadmap/controllers/commands/dto/GrantAccessToAllSuggestedSolutionsCommand';
import {
    GrantAccessToSuggestedSolutionCommand,
    GrantAccessToSuggestedSolutionCommandMapper,
} from '../../learning/submodules/roadmap/controllers/commands/dto/GrantAccessToSuggestedSolutionCommand';
import {
    MentorAssignedCoursesChangedEvent,
    MentorAssignedCoursesChangedEventMapper,
} from '../../mentor-assigned-course/controllers/events/dto/MentorAssignedCoursesChangedEvent';
import {
    DiscordChangeRolesCommand,
    DiscordChangeRolesCommandMapper,
} from '../../discord/controllers/commands/dto/DiscordChangeRolesCommand';
import {
    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand,
    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandMapper,
} from '../../mentor-assigned-course/controllers/commands/dto/ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand';
import {
    SprintPartUpdatedEvent,
    SprintPartUpdatedEventMapper,
} from '../../education/sprints/controllers/events/dto/SprintPartUpdatedEvent';
import {
    CostTrackingCreateMonthlyWorklogsCommand,
    CostTrackingCreateMonthlyWorklogsCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-worklog/controllers/commands/dto/CostTrackingCreateMonthlyWorklogsCommand';
import {
    CostTrackingMoveWorklogsFromOngoingCommand,
    CostTrackingMoveWorklogsFromOngoingCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-worklog/controllers/commands/dto/CostTrackingMoveWorklogsFromOngoingCommand';
import {
    CostTrackingProfileCostTrackingEnabledEvent,
    CostTrackingProfileCostTrackingEnabledEventMapper,
} from '../../cost-tracking/submodules/cost-tracking-profile/controllers/events/dto/CostTrackingProfileCostTrackingEnabledEvent';
import {
    RefreshPendingSprintPartReviewerPreconditionsCommand,
    RefreshPendingSprintPartReviewerPreconditionsCommandMapper,
} from '../../education/sprints/controllers/commands/dto/RefreshPendingSprintPartReviewerPreconditionsCommand';
import {
    SprintPartReviewerPermissionsUpdatedEvent,
    SprintPartReviewerPermissionsUpdatedEventMapper,
} from '../../education/sprints/controllers/events/dto/SprintPartReviewerPermissionsUpdatedEvent';
import {
    CostTrackingMoveSubmittedWorklogsToReceivedCommand,
    CostTrackingMoveSubmittedWorklogsToReceivedCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-worklog/controllers/commands/dto/CostTrackingMoveSubmittedWorklogsToReceivedCommand';
import {
    CostTrackingNotifyAboutPendingWorklogsCommand,
    CostTrackingNotifyAboutPendingWorklogsCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-worklog/controllers/commands/dto/CostTrackingNotifyAboutPendingWorklogsCommand';
import {
    RecordDiscordOAuthProfileChangeCommand,
    RecordDiscordOAuthProfileChangeCommandMapper,
} from '../../discord-profile/controllers/commands/dto/RecordDiscordOAuthProfileChangeCommand';
import {
    CostTrackingProfileHourlyRateUpdatedEvent,
    CostTrackingProfileHourlyRateUpdatedEventMapper,
} from '../../cost-tracking/submodules/cost-tracking-profile/controllers/events/dto/CostTrackingProfileHourlyRateUpdatedEvent';
import {
    CostTrackingRecalculateWorklogsStatesCommand,
    CostTrackingRecalculateWorklogsStatesCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-worklog/controllers/commands/dto/CostTrackingRecalculateWorklogsStatesCommand';
import {
    CostTrackingProfileCostTrackingDisabledEvent,
    CostTrackingProfileCostTrackingDisabledEventMapper,
} from '../../cost-tracking/submodules/cost-tracking-profile/controllers/events/dto/CostTrackingProfileCostTrackingDisabledEvent';
import {
    CostTrackingCloseLogTimePromptsCommand,
    CostTrackingCloseLogTimePromptsCommandMapper,
} from '../../cost-tracking/submodules/cost-tracking-auto-tracking/controllers/commands/dto/CostTrackingCloseLogTimePromptsCommand';
import {
    EvaluateAndProgressPriedasDocumentCommand,
    EvaluateAndProgressPriedasDocumentCommandMapper,
} from '../../uzt/submodules/uzt-document/controllers/commands/dto/EvaluateAndProgressPriedasDocumentCommand';
import {
    DocumentNewStateEvent,
    DocumentNewStateEventMapper,
} from '../../document/controllers/events/dto/DocumentNewStateEvent';
import {
    AutoSendUztBundleCommand,
    AutoSendUztBundleCommandMapper,
} from '../../uzt/submodules/uzt-document/controllers/commands/dto/AutoSendUztBundleCommand';
import {
    UserOnboardedEvent,
    UserOnboardedEventMapper,
} from '../../profile-setup-steps/controllers/events/dto/UserOnboardedEvent';
import {
    SyncParticipationInfoForActiveLearnersForDayCommand,
    SyncParticipationInfoForActiveLearnersForDayCommandMapper,
} from '../../gov/submodules/gov-participation-auto-aggregation/controllers/commands/dto/SyncParticipationInfoForActiveLearnersForDayCommand';
import {
    SyncLearnerInfoForDayCommand,
    SyncLearnerInfoForDayCommandMapper,
} from '../../gov/submodules/gov-participation-auto-aggregation/controllers/commands/dto/SyncLearnerInfoForDayCommand';
import {
    SyncInvalidScheduleNotificationCommand,
    SyncInvalidScheduleNotificationCommandMapper,
} from '../../gov/submodules/gov-week-schedule/submodules/invalid-week-schedule-notification/controllers/command/dto/SyncInvalidScheduleNotificationCommand';

import {
    SetDefaultScheduleCommand,
    SetDefaultScheduleCommandMapper,
} from '../../gov/submodules/gov-week-schedule/submodules/default-week-schedule/controllers/command/dto/SetDefaultScheduleCommand';
import {
    SendUztInfoNotificationCommand,
    SendUztInfoNotificationCommandMapper,
} from '../../uzt/submodules/uzt-notification/controllers/commands/dto/SendUztInfoNotificationCommand';
import {
    RewriteMeetingParticipantsLogCommand,
    RewriteMeetingParticipantsLogCommandMapper,
} from '../../meetings/controllers/commands/dto/RewriteMeetingParticipantsLogCommand';
import {
    SyncParticipationInfoForMonthCommand,
    SyncParticipationInfoForMonthCommandMapper,
} from '../../gov/submodules/gov-participation-auto-aggregation/controllers/commands/dto/SyncParticipationInfoForMonthCommand';
import {
    AlertOnMissedUztDaysCommand,
    AlertOnMissedUztDaysCommandMapper,
} from '../../uzt/submodules/uzt-alerts/controllers/commands/dto/AlertOnMissedUztDaysCommand';
import {
    AlertOnMissedUztHoursCommand,
    AlertOnMissedUztHoursCommandMapper,
} from '../../uzt/submodules/uzt-alerts/controllers/commands/dto/AlertOnMissedUztHoursCommand';
import {
    ScheduleUZTProofOfCompletionCommand,
    ScheduleUZTProofOfCompletionCommandMapper,
} from '../../uzt/submodules/uzt-proof-of-completion/controllers/commands/dto/ScheduleUZTProofOfCompletionCommand';
import {
    SendUZTProofOfCompletionCommand,
    SendUZTProofOfCompletionCommandMapper,
} from '../../uzt/submodules/uzt-proof-of-completion/controllers/commands/dto/SendUZTProofOfCompletionCommand';
import {
    SyncMissingContractTermNotificationCommand,
    SyncMissingContractTermNotificationCommandMapper,
} from '../../uzt/submodules/uzt-profile/controllers/command/dto/SyncMissingContractTermNotificationCommand';
import { BatchStartedEvent, BatchStartedEventMapper } from '../../batches/controllers/events/dto/BatchStartedEvent';
import {
    ActivateBatchOnboardingLearnersCommand,
    ActivateBatchOnboardingLearnersCommandMapper,
} from '../../onboarding/controllers/commands/dto/ActivateBatchOnboardingLearnersCommand';
import {
    SyncDiscordServersInfoToPlatformCommand,
    SyncDiscordServersInfoToPlatformCommandMapper,
} from '../../discord-platform/submodules/discord-server-info-sync/controllers/commands/dto/SyncDiscordServersInfoToPlatformCommand';
import {
    CompletedReviewCommand,
    CompletedReviewCommandMapper,
} from '../../corrections/controllers/commands/dto/CompletedReviewCommand';
import {
    UpdateUserDiscordStateCommand,
    UpdateUserDiscordStateCommandMapper,
} from '../../discord-platform/submodules/user-discord/controllers/commands/dto/UpdateUserDiscordStateCommand';
import { SignupCompletedEvent, SignupCompletedEventMapper } from '../../users/signup/events/dto/SignupCompletedEvent';

export const APP_QUEUES_SCHEMA = {
    core: {
        email: {
            templated: {
                send: new QueuesSchemaCommandQueueDefinition(
                    SendTemplatedEmailCommand,
                    SendTemplatedEmailCommandMapper,
                ),
            },
        },
    },
    education: {
        sprintPart: {
            updated: new QueuesSchemaEventQueueDefinition(SprintPartUpdatedEvent, SprintPartUpdatedEventMapper),
            reviewerPermissionsUpdated: new QueuesSchemaEventQueueDefinition(
                SprintPartReviewerPermissionsUpdatedEvent,
                SprintPartReviewerPermissionsUpdatedEventMapper,
            ),
            refreshPendingReviewerPreconditions: new QueuesSchemaCommandQueueDefinition(
                RefreshPendingSprintPartReviewerPreconditionsCommand,
                RefreshPendingSprintPartReviewerPreconditionsCommandMapper,
            ),
        },
    },
    learning: {
        refreshPendingPreconditions: new QueuesSchemaCommandQueueDefinition(
            RefreshPendingPreconditionsCommand,
            RefreshPendingPreconditionsCommandMapper,
        ),
        submission: {
            access: {
                grantToAll: new QueuesSchemaCommandQueueDefinition(
                    GrantAccessToAllSubmissionsCommand,
                    GrantAccessToAllSubmissionsCommandMapper,
                ),
                grant: new QueuesSchemaCommandQueueDefinition(
                    GrantAccessToSubmissionCommand,
                    GrantAccessToSubmissionMapper,
                ),
            },
        },
        suggestedSolution: {
            access: {
                grantToAll: new QueuesSchemaCommandQueueDefinition(
                    GrantAccessToAllSuggestedSolutionsCommand,
                    GrantAccessToAllSuggestedSolutionsCommandMapper,
                ),
                grant: new QueuesSchemaCommandQueueDefinition(
                    GrantAccessToSuggestedSolutionCommand,
                    GrantAccessToSuggestedSolutionCommandMapper,
                ),
            },
        },
    },
    discord: {
        guild: {
            removeMember: new QueuesSchemaCommandQueueDefinition(
                DiscordRemoveFromGuildCommand,
                DiscordRemoveFromGuildCommandMapper,
            ),
            changeRoles: new QueuesSchemaCommandQueueDefinition(
                DiscordChangeRolesCommand,
                DiscordChangeRolesCommandMapper,
            ),
        },
        servers: {
            sync: new QueuesSchemaCommandQueueDefinition(
                SyncDiscordServersInfoToPlatformCommand,
                SyncDiscordServersInfoToPlatformCommandMapper,
            ),
        },
    },
    github: {
        organization: {
            removeFromOrganization: new QueuesSchemaCommandQueueDefinition(
                GithubRemoveFromOrganizationCommand,
                GithubRemoveFromOrganizationCommandMapper,
            ),
        },
    },
    google: {
        drive: {
            removeAccessToAllEntries: new QueuesSchemaCommandQueueDefinition(
                GoogleDriveRemoveAccessToAllEntriesCommand,
                GoogleDriveRemoveAccessToAllEntriesCommandMapper,
            ),
            removePermission: new QueuesSchemaCommandQueueDefinition(
                GoogleDriveRemovePermissionCommand,
                GoogleDriveRemovePermissionCommandMapper,
            ),
        },
    },
    user: {
        onboarded: new QueuesSchemaEventQueueDefinition(UserOnboardedEvent, UserOnboardedEventMapper),
        signup: {
            completed: new QueuesSchemaEventQueueDefinition(SignupCompletedEvent, SignupCompletedEventMapper),
        },
        state: {
            changed: new QueuesSchemaEventQueueDefinition(UserStateChangedEvent, UserStateChangedEventMapper),
        },
        google: {
            account: {
                changed: new QueuesSchemaEventQueueDefinition(
                    UserLinkedGoogleAccountChangedEvent,
                    UserLinkedGoogleAccountChangedEventMapper,
                ),
            },
        },
        github: {
            account: {
                changed: new QueuesSchemaEventQueueDefinition(
                    UserLinkedGithubAccountChangedEvent,
                    UserLinkedGithubAccountChangedEventMapper,
                ),
            },
        },
        discord: {
            account: {
                changed: new QueuesSchemaEventQueueDefinition(
                    UserLinkedDiscordAccountChangedEvent,
                    UserLinkedDiscordAccountChangedEventMapper,
                ),
                recordChangeHistory: new QueuesSchemaCommandQueueDefinition(
                    RecordDiscordOAuthProfileChangeCommand,
                    RecordDiscordOAuthProfileChangeCommandMapper,
                ),
            },
            state: {
                update: new QueuesSchemaCommandQueueDefinition(
                    UpdateUserDiscordStateCommand,
                    UpdateUserDiscordStateCommandMapper,
                ),
            },
        },
        mentor: {
            assignedCourse: {
                changed: new QueuesSchemaEventQueueDefinition(
                    MentorAssignedCoursesChangedEvent,
                    MentorAssignedCoursesChangedEventMapper,
                ),
                changeDiscordRolesAccordingToAssignedCourses: new QueuesSchemaCommandQueueDefinition(
                    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand,
                    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandMapper,
                ),
            },
        },
        learner: {
            suspension: {
                reschedule: new QueuesSchemaCommandQueueDefinition(
                    RescheduleLearnerSuspensionCommand,
                    RescheduleLearnerSuspensionCommandMapper,
                ),
                suspend: new QueuesSchemaCommandQueueDefinition(SuspendLearnerCommand, SuspendLearnerCommandMapper),
                notify: {
                    email: new QueuesSchemaCommandQueueDefinition(
                        LearnerSuspendedNotifyByEmailCommand,
                        LearnerSuspendedNotifyByEmailCommandMapper,
                    ),
                    platform: new QueuesSchemaCommandQueueDefinition(
                        LearnerSuspendedNotifyByPlatformCommand,
                        LearnerSuspendedNotifyByPlatformCommandMapper,
                    ),
                },
                upcoming: {
                    notify: {
                        all: new QueuesSchemaCommandQueueDefinition(
                            NotifyLearnerAboutUpcomingSuspensionCommand,
                            NotifyLearnerAboutUpcomingSuspensionCommandMapper,
                        ),
                        email: new QueuesSchemaCommandQueueDefinition(
                            LearnerUpcomingSuspensionNotifyByEmailCommand,
                            LearnerUpcomingSuspensionNotifyByEmailCommandMapper,
                        ),
                        platform: new QueuesSchemaCommandQueueDefinition(
                            LearnerUpcomingSuspensionNotifyByPlatformCommand,
                            LearnerUpcomingSuspensionNotifyByPlatformCommandMapper,
                        ),
                    },
                },
            },
        },
    },
    costTracking: {
        autoTracking: {
            closeLogTimePrompts: new QueuesSchemaCommandQueueDefinition(
                CostTrackingCloseLogTimePromptsCommand,
                CostTrackingCloseLogTimePromptsCommandMapper,
            ),
        },
        worklog: {
            createMonthly: new QueuesSchemaCommandQueueDefinition(
                CostTrackingCreateMonthlyWorklogsCommand,
                CostTrackingCreateMonthlyWorklogsCommandMapper,
            ),
            moveFromOngoing: new QueuesSchemaCommandQueueDefinition(
                CostTrackingMoveWorklogsFromOngoingCommand,
                CostTrackingMoveWorklogsFromOngoingCommandMapper,
            ),
            moveSubmittedToReceived: new QueuesSchemaCommandQueueDefinition(
                CostTrackingMoveSubmittedWorklogsToReceivedCommand,
                CostTrackingMoveSubmittedWorklogsToReceivedCommandMapper,
            ),
            notifyAboutPendingWorklogs: new QueuesSchemaCommandQueueDefinition(
                CostTrackingNotifyAboutPendingWorklogsCommand,
                CostTrackingNotifyAboutPendingWorklogsCommandMapper,
            ),
            recalculateWorklogStates: new QueuesSchemaCommandQueueDefinition(
                CostTrackingRecalculateWorklogsStatesCommand,
                CostTrackingRecalculateWorklogsStatesCommandMapper,
            ),
        },
        profile: {
            costTrackingEnabled: new QueuesSchemaEventQueueDefinition(
                CostTrackingProfileCostTrackingEnabledEvent,
                CostTrackingProfileCostTrackingEnabledEventMapper,
            ),
            costTrackingDisabled: new QueuesSchemaEventQueueDefinition(
                CostTrackingProfileCostTrackingDisabledEvent,
                CostTrackingProfileCostTrackingDisabledEventMapper,
            ),
            hourlyRateUpdated: new QueuesSchemaEventQueueDefinition(
                CostTrackingProfileHourlyRateUpdatedEvent,
                CostTrackingProfileHourlyRateUpdatedEventMapper,
            ),
        },
    },
    document: {
        newState: new QueuesSchemaEventQueueDefinition(DocumentNewStateEvent, DocumentNewStateEventMapper),
    },
    uzt: {
        document: {
            autoSendBundle: new QueuesSchemaCommandQueueDefinition(
                AutoSendUztBundleCommand,
                AutoSendUztBundleCommandMapper,
            ),
            priedas: {
                evaluateAndProgress: new QueuesSchemaCommandQueueDefinition(
                    EvaluateAndProgressPriedasDocumentCommand,
                    EvaluateAndProgressPriedasDocumentCommandMapper,
                ),
            },
        },
        schedule: {
            syncInvalidScheduleNotification: new QueuesSchemaCommandQueueDefinition(
                SyncInvalidScheduleNotificationCommand,
                SyncInvalidScheduleNotificationCommandMapper,
            ),
            setDefaultSchedule: new QueuesSchemaCommandQueueDefinition(
                SetDefaultScheduleCommand,
                SetDefaultScheduleCommandMapper,
            ),
        },
        uztPresence: {
            syncForActiveLearnersForDay: new QueuesSchemaCommandQueueDefinition(
                SyncParticipationInfoForActiveLearnersForDayCommand,
                SyncParticipationInfoForActiveLearnersForDayCommandMapper,
            ),
            syncLearnerInfoForDay: new QueuesSchemaCommandQueueDefinition(
                SyncLearnerInfoForDayCommand,
                SyncLearnerInfoForDayCommandMapper,
            ),
            syncLearnerInfoForMonth: new QueuesSchemaCommandQueueDefinition(
                SyncParticipationInfoForMonthCommand,
                SyncParticipationInfoForMonthCommandMapper,
            ),
            alertOnMissedUztDaysCommand: new QueuesSchemaCommandQueueDefinition(
                AlertOnMissedUztDaysCommand,
                AlertOnMissedUztDaysCommandMapper,
            ),
            alertOnMissedUztHoursCommand: new QueuesSchemaCommandQueueDefinition(
                AlertOnMissedUztHoursCommand,
                AlertOnMissedUztHoursCommandMapper,
            ),
        },
        notification: {
            sendUztInfo: new QueuesSchemaCommandQueueDefinition(
                SendUztInfoNotificationCommand,
                SendUztInfoNotificationCommandMapper,
            ),
        },
        uztProofOfCompletion: {
            scheduleCommand: new QueuesSchemaCommandQueueDefinition(
                ScheduleUZTProofOfCompletionCommand,
                ScheduleUZTProofOfCompletionCommandMapper,
            ),
            sendCommand: new QueuesSchemaCommandQueueDefinition(
                SendUZTProofOfCompletionCommand,
                SendUZTProofOfCompletionCommandMapper,
            ),
        },
        profile: {
            syncMissingContractTermNotification: new QueuesSchemaCommandQueueDefinition(
                SyncMissingContractTermNotificationCommand,
                SyncMissingContractTermNotificationCommandMapper,
            ),
        },
    },
    batch: {
        started: new QueuesSchemaEventQueueDefinition(BatchStartedEvent, BatchStartedEventMapper),
    },
    onboarding: {
        activateBatchOnboardingLearners: new QueuesSchemaCommandQueueDefinition(
            ActivateBatchOnboardingLearnersCommand,
            ActivateBatchOnboardingLearnersCommandMapper,
        ),
    },
    meetings: {
        rewriteParticipantsLog: new QueuesSchemaCommandQueueDefinition(
            RewriteMeetingParticipantsLogCommand,
            RewriteMeetingParticipantsLogCommandMapper,
        ),
    },
    review: {
        completed: new QueuesSchemaCommandQueueDefinition(CompletedReviewCommand, CompletedReviewCommandMapper),
    },
} as const;

export type AppQueuesSchema = typeof APP_QUEUES_SCHEMA;
