import { faker } from '@faker-js/faker';
import moment from 'moment';
import Id from '../../../../core/domain/value-objects/Id';
import IllegalArgumentError from '../../../../core/errors/IllegalArgumentError';
import LearningComponentAbbreviation from '../../../../education/common/LearningComponentAbbreviation';
import Module from '../../../../education/modules/domain/Module';
import Sprint from '../../../../education/sprints/domain/Sprint';
import SprintPart from '../../../../education/sprints/domain/SprintPart';
import AssignedCourse from './AssignedCourse';
import AssignedModule, { AssignedModuleState } from './AssignedModule';
import AssignedModuleSlot from './AssignedModuleSlot';
import AssignedSprint, { AssignedSprintState } from './AssignedSprint';
import AssignedSprintPart, { AssignedSprintPartPrecondition, AssignedSprintPartState } from './AssignedSprintPart';
import { DeadlineCategory } from '../../../../users/students/settings/domain/DeadlineCategory';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import {
    getRandomDomainCourse,
    getRandomDomainModule,
    getRandomDomainSprint,
    getRandomDomainSprintPart,
} from '../../../../test-toolkit/shared/education';
import {
    getRandomAssignedCourse,
    getRandomAssignedModule,
    getRandomAssignedModuleSlot,
    getRandomAssignedSprint,
    getRandomAssignedSprintPart,
} from '../../../../test-toolkit/shared/learning';
import { SprintPartContentType } from '../../../../education/sprints/domain/SprintPartContentType';

function assertAssignedSprintPart(
    assignedPart: AssignedSprintPart,
    part: SprintPart,
    abbreviation: LearningComponentAbbreviation,
): void {
    expect(assignedPart.sprintPartId).toEqual(part.id);
    expect(assignedPart.name).toEqual(part.name);
    expect(assignedPart.description).toEqual(part.description);
    expect(assignedPart.contentType).toEqual(part.contentType);
    expect(assignedPart.evaluationProperties).toEqual(part.evaluationProperties);
    expect(assignedPart.abbreviation).toEqual(abbreviation);
    expect(assignedPart.state).toEqual(AssignedSprintPartState.PENDING);

    const preconditions: AssignedSprintPartPrecondition[] = [];

    if (part.contentType !== SprintPartContentType.TEMPLATE) {
        preconditions.push(AssignedSprintPartPrecondition.GITHUB);
    }

    const actualPreconditions = assignedPart.preconditions;

    expect(actualPreconditions.length).toEqual(preconditions.length);
    expect(actualPreconditions).toEqual(expect.arrayContaining(preconditions));
}

function assertAssignedSprint(
    assignedSprint: AssignedSprint,
    sprint: Sprint,
    sprintIndex: number,
    module: AssignedModule,
    deadlineCategory: DeadlineCategory,
    scheduledStart: Date,
): void {
    expect(assignedSprint.sprintId).toEqual(sprint.id);
    expect(assignedSprint.name).toEqual(sprint.name);
    expect(assignedSprint.duration).toEqual(sprint.deadlineProperties.getDeadlineDays(deadlineCategory));
    expect(assignedSprint.deadline).toEqual(
        moment(scheduledStart).add(sprint.deadlineProperties.getDeadlineDays(deadlineCategory), 'days').toDate(),
    );
    expect(assignedSprint.state).toEqual(AssignedSprintState.PENDING);
    expect(assignedSprint.parts).toHaveLength(sprint.parts.length);
    assignedSprint.parts.forEach((part, index) =>
        assertAssignedSprintPart(
            part,
            sprint.parts[index],
            new LearningComponentAbbreviation(`${module.abbreviation.value}.${sprintIndex + 1}.${index + 1}`),
        ),
    );
    expect(assignedSprint.skills).toEqual(sprint.skills);
}

function assertAssignedModule(
    assignedModule: AssignedModule,
    module: Module,
    deadlineCategory: DeadlineCategory,
    scheduledStart: Date,
): void {
    expect(assignedModule.name).toEqual(module.name);
    expect(assignedModule.abbreviation).toEqual(module.abbreviation);
    expect(assignedModule.moduleId).toEqual(module.id);
    expect(assignedModule.sprints).toHaveLength(module.sprints.length);
    assignedModule.sprints.forEach((sprint, index) =>
        assertAssignedSprint(
            sprint,
            module.sprints[index],
            index,
            assignedModule,
            deadlineCategory,
            index === 0 ? scheduledStart : assignedModule.sprints[index - 1].deadline,
        ),
    );
}

describe('AssignedCourse', () => {
    const course = getRandomDomainCourse();

    describe('addModuleSlot', () => {
        it('should add slot', () => {
            const courseBefore = getRandomAssignedCourse();
            const modules = [TestObjects.uniqueId(), TestObjects.uniqueId()];

            const courseAfter = courseBefore.addModuleSlot(new AssignedModuleSlot({ moduleIds: modules }));

            expect(courseAfter.moduleSlots.length).toEqual(courseBefore.moduleSlots.length + 1);
            expect(courseAfter.moduleSlots[courseAfter.moduleSlots.length - 1].moduleIds).toEqual(modules);
        });

        it('should not add slot without modules', () => {
            const courseBefore = getRandomAssignedCourse();

            expect(() => courseBefore.addModuleSlot(new AssignedModuleSlot({ moduleIds: [] }))).toThrow(
                new IllegalArgumentError(
                    `Assigned module slot at position '${
                        courseBefore.moduleSlots.length + 1
                    }' might not have a valid module option`,
                ),
            );
        });

        it('should not add slot without modules', () => {
            const courseBefore = getRandomAssignedCourse();

            expect(() =>
                courseBefore.addModuleSlot(
                    new AssignedModuleSlot({ moduleIds: [courseBefore.moduleSlots[0].moduleIds[0]] }),
                ),
            ).toThrow(
                new IllegalArgumentError(
                    `Assigned module slot at position '${
                        courseBefore.moduleSlots.length + 1
                    }' might not have a valid module option`,
                ),
            );
        });
    });

    describe('removeModuleSlot', () => {
        it('should remove last remaining slot ', () => {
            const courseBefore = getRandomAssignedCourse(true, [getRandomAssignedModuleSlot()]);

            const courseAfter = courseBefore.removeModuleSlot(courseBefore.moduleSlots[0].id as Id);

            expect(courseAfter.moduleSlots).toHaveLength(0);
        });

        it('should remove not last remaining slot', () => {
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(),
                getRandomAssignedModuleSlot(),
                getRandomAssignedModuleSlot(),
            ]);
            const slot = courseBefore.moduleSlots[1];

            const courseAfter = courseBefore.removeModuleSlot(slot.id as Id);

            expect(courseAfter.moduleSlots).toHaveLength(courseBefore.moduleSlots.length - 1);
            expect(courseAfter.moduleSlots).not.toContain(slot);
        });

        it('should not remove non existing module slot', () => {
            const courseBefore = getRandomAssignedCourse();

            expect(() => courseBefore.removeModuleSlot(TestObjects.uniqueId())).toThrow(
                new IllegalArgumentError('Module slot not found'),
            );
        });

        it('should not remove module slot with assigned module', () => {
            const assignedModule = getRandomAssignedModule();
            const assignedModuleSlot = getRandomAssignedModuleSlot(true, [assignedModule.moduleId as Id]).assignModule(
                assignedModule,
            );
            const courseBefore = getRandomAssignedCourse(true, [assignedModuleSlot]);

            expect(() => courseBefore.removeModuleSlot(assignedModuleSlot.id as Id)).toThrow(
                new IllegalArgumentError('Module slot with an assigned module cannot be removed'),
            );
        });
    });

    describe('changeModuleSlotModules', () => {
        it('should remove one module', () => {
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(true, [
                    TestObjects.uniqueId(),
                    TestObjects.uniqueId(),
                    TestObjects.uniqueId(),
                ]),
            ]);

            const courseAfter = courseBefore.updateModuleSlot(
                new AssignedModuleSlot({
                    id: courseBefore.moduleSlots[0].id as Id,
                    moduleIds: [courseBefore.moduleSlots[0].moduleIds[0], courseBefore.moduleSlots[0].moduleIds[2]],
                }),
            );

            expect(courseAfter.moduleSlots[0].moduleIds).toHaveLength(2);
            expect(courseAfter.moduleSlots[0].moduleIds).not.toContain(courseBefore.moduleSlots[0].moduleIds[1]);
        });

        it('should add one module', () => {
            const courseBefore = getRandomAssignedCourse();
            const modules = [...courseBefore.moduleSlots[0].moduleIds, TestObjects.uniqueId()];

            const courseAfter = courseBefore.updateModuleSlot(
                new AssignedModuleSlot({
                    id: courseBefore.moduleSlots[0].id as Id,
                    moduleIds: modules,
                }),
            );

            expect(courseAfter.moduleSlots[0].moduleIds).toHaveLength(modules.length);
            expect(courseAfter.moduleSlots[0].moduleIds).toEqual(modules);
        });

        it('should replace module', () => {
            const courseBefore = getRandomAssignedCourse();
            const modules = [...courseBefore.moduleSlots[0].moduleIds];
            modules[0] = TestObjects.uniqueId();

            const courseAfter = courseBefore.updateModuleSlot(
                new AssignedModuleSlot({
                    id: courseBefore.moduleSlots[0].id as Id,
                    moduleIds: modules,
                }),
            );

            expect(courseAfter.moduleSlots[0].moduleIds).toHaveLength(modules.length);
            expect(courseAfter.moduleSlots[0].moduleIds).toEqual(modules);
        });

        it('should not remove all modules', () => {
            const courseBefore = getRandomAssignedCourse(true, [getRandomAssignedModuleSlot()]);

            expect(() =>
                courseBefore.updateModuleSlot(
                    new AssignedModuleSlot({ id: courseBefore.moduleSlots[0].id, moduleIds: [] }),
                ),
            ).toThrow("Assigned module slot at position '1' might not have a valid module option");
        });

        it('should not change modules if it makes another module slot invalid', () => {
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(true, [TestObjects.uniqueId()]),
                getRandomAssignedModuleSlot(true, [TestObjects.uniqueId()]),
            ]);

            expect(() =>
                courseBefore.updateModuleSlot(
                    new AssignedModuleSlot({
                        id: courseBefore.moduleSlots[0].id as Id,
                        moduleIds: courseBefore.moduleSlots[1].moduleIds,
                    }),
                ),
            ).toThrow("Assigned module slot at position '2' might not have a valid module option");
        });

        it('should not change modules of non existing module slot', () => {
            const courseBefore = getRandomAssignedCourse();

            expect(() =>
                courseBefore.updateModuleSlot(
                    new AssignedModuleSlot({
                        id: TestObjects.uniqueId(),
                        moduleIds: [],
                    }),
                ),
            ).toThrow(new IllegalArgumentError('Module slot not found'));
        });
    });

    describe('assignModule', () => {
        it('should not assign module to a non existing module slot', () => {
            const module = getRandomDomainModule();
            const properties = {
                deadlineCategory: DeadlineCategory.B,
                courseStartDate: new Date(),
                userHasGoogleAccount: true,
            };
            const courseBefore = getRandomAssignedCourse();

            expect(() => courseBefore.assignModule(TestObjects.uniqueId(), module, properties, course)).toThrow(
                new IllegalArgumentError('Module slot not found'),
            );
        });

        it('should assign first module', () => {
            const module = getRandomDomainModule(true, [
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                ]),
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                ]),
            ]);
            const courseBefore = getRandomAssignedCourse(true, [getRandomAssignedModuleSlot(true, [module.id as Id])]);
            const properties = {
                deadlineCategory: faker.helpers.objectValue(DeadlineCategory),
                courseStartDate: new Date(),
                userHasGoogleAccount: true,
            };

            const courseAfter = courseBefore.assignModule(
                courseBefore.moduleSlots[0].id as Id,
                module,
                properties,
                course,
            );

            expect(courseAfter.moduleSlots[0].hasAssignedModule()).toBeTruthy();
            assertAssignedModule(
                courseAfter.moduleSlots[0].assignedModule as AssignedModule,
                module,
                properties.deadlineCategory,
                properties.courseStartDate,
            );
        });

        it('should assign second module', () => {
            const firstAssignedModule = getRandomAssignedModule();
            const secondModule = getRandomDomainModule(true, [
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                ]),
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                ]),
            ]);
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [firstAssignedModule.moduleId],
                    assignedModule: firstAssignedModule,
                }),
                getRandomAssignedModuleSlot(true, [secondModule.id as Id]),
            ]);
            const properties = {
                deadlineCategory: faker.helpers.objectValue(DeadlineCategory),
                courseStartDate: new Date(),
                userHasGoogleAccount: true,
            };

            const courseAfter = courseBefore.assignModule(
                courseBefore.moduleSlots[1].id as Id,
                secondModule,
                properties,
                course,
            );

            expect(courseAfter.moduleSlots[1].hasAssignedModule()).toBeTruthy();
            assertAssignedModule(
                courseAfter.moduleSlots[1].assignedModule as AssignedModule,
                secondModule,
                properties.deadlineCategory,
                firstAssignedModule.deadline,
            );
        });

        it('should assign module with github precondition', () => {
            const module = getRandomDomainModule(true, [
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.GITHUB }),
                ]),
            ]);
            const courseBefore = getRandomAssignedCourse(true, [getRandomAssignedModuleSlot(true, [module.id as Id])]);
            const properties = {
                deadlineCategory: faker.helpers.objectValue(DeadlineCategory),
                courseStartDate: new Date(),
                userHasGoogleAccount: true,
            };

            const courseAfter = courseBefore.assignModule(
                courseBefore.moduleSlots[0].id as Id,
                module,
                properties,
                course,
            );

            expect(courseAfter.moduleSlots[0].hasAssignedModule()).toBeTruthy();
            assertAssignedModule(
                courseAfter.moduleSlots[0].assignedModule as AssignedModule,
                module,
                properties.deadlineCategory,
                properties.courseStartDate,
            );
        });

        it('should assign module with discord precondition', () => {
            const module = getRandomDomainModule(true, [
                getRandomDomainSprint(true, [
                    getRandomDomainSprintPart().update({ contentType: SprintPartContentType.TEMPLATE }),
                ]),
            ]);
            const courseBefore = getRandomAssignedCourse(true, [getRandomAssignedModuleSlot(true, [module.id as Id])]);
            const properties = {
                deadlineCategory: faker.helpers.objectValue(DeadlineCategory),
                courseStartDate: new Date(),
                userHasGoogleAccount: true,
            };

            const courseAfter = courseBefore.assignModule(
                courseBefore.moduleSlots[0].id as Id,
                module,
                properties,
                course,
            );

            expect(courseAfter.moduleSlots[0].hasAssignedModule()).toBeTruthy();
            assertAssignedModule(
                courseAfter.moduleSlots[0].assignedModule as AssignedModule,
                module,
                properties.deadlineCategory,
                properties.courseStartDate,
            );
        });
    });

    describe('startNextModule', () => {
        it('should start first module', () => {
            const firstAssignedModule = getRandomAssignedModule();
            const secondAssignedModule = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.PENDING,
                firstAssignedModule.deadline,
            );
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [firstAssignedModule.moduleId],
                    assignedModule: firstAssignedModule,
                }),
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),

                    moduleIds: [secondAssignedModule.moduleId],
                    assignedModule: secondAssignedModule,
                }),
            ]);

            const courseAfter = courseBefore.startNextModule();

            expect(courseAfter.moduleSlots[0].assignedModule?.isInProgress()).toBeTruthy();
            expect(courseAfter.moduleSlots[0].assignedModule?.sprints[0].isInProgress()).toBeTruthy();
        });

        it('should start second module', () => {
            const firstAssignedModule = getRandomAssignedModule(true, TestObjects.id(), AssignedModuleState.COMPLETED);
            const secondAssignedModule = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.PENDING,
                firstAssignedModule.deadline,
            );
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [firstAssignedModule.moduleId],
                    assignedModule: firstAssignedModule,
                }),
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [secondAssignedModule.moduleId],
                    assignedModule: secondAssignedModule,
                }),
            ]);

            const courseAfter = courseBefore.startNextModule();

            expect(courseAfter.moduleSlots[1].assignedModule?.isInProgress()).toBeTruthy();
            expect(courseAfter.moduleSlots[1].assignedModule?.sprints[0].isInProgress()).toBeTruthy();
        });

        it('should not start if slot does not have assigned module', () => {
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(),
                getRandomAssignedModuleSlot(),
            ]);

            const courseAfter = courseBefore.startNextModule();

            expect(courseAfter.moduleSlots[0].hasAssignedModule()).toBeFalsy();
            expect(courseAfter.moduleSlots[1].hasAssignedModule()).toBeFalsy();
        });

        it('should not start if slot has a module in progress', () => {
            const firstAssignedModule = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.IN_PROGRESS,
            );
            const secondAssignedModule = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.PENDING,
                firstAssignedModule.deadline,
            );
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [firstAssignedModule.moduleId],
                    assignedModule: firstAssignedModule,
                }),
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    moduleIds: [secondAssignedModule.moduleId],
                    assignedModule: secondAssignedModule,
                }),
            ]);

            const courseAfter = courseBefore.startNextModule();

            expect(courseAfter.moduleSlots[0].assignedModule?.isInProgress()).toBeTruthy();
            expect(courseAfter.moduleSlots[1].assignedModule?.isPending()).toBeTruthy();
        });
    });

    describe('startPart', () => {
        it('should start sprint part', () => {
            const assignedModule = getRandomAssignedModule();
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    assignedModule,
                    moduleIds: [assignedModule.moduleId],
                }),
            ]).startNextModule();

            const courseAfter = courseBefore.startPart(
                courseBefore.moduleSlots[0].assignedModule?.sprints[0].parts[0].id as Id,
            );

            expect(courseAfter.moduleSlots[0].assignedModule?.sprints[0].parts[0].isInProgress()).toBeTruthy();
        });
    });

    describe('submitPart', () => {
        it('should submit part', () => {
            const assignedModule = getRandomAssignedModule();
            const courseBefore = getRandomAssignedCourse(true, [
                new AssignedModuleSlot({
                    id: TestObjects.uniqueId(),
                    assignedModule,
                    moduleIds: [assignedModule.moduleId],
                }),
            ]).startNextModule();

            const courseAfter = courseBefore
                .startPart(courseBefore.moduleSlots[0].assignedModule?.sprints[0].parts[0].id as Id)
                .submitPart(courseBefore.moduleSlots[0].assignedModule?.sprints[0].parts[0].id as Id);

            expect(courseAfter.moduleSlots[0].assignedModule?.sprints[0].parts[0].isSubmitted()).toBeTruthy();
        });
    });

    describe('completePart', () => {
        it('should complete not module part', () => {
            const module = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.IN_PROGRESS,
                new Date(),
                [
                    getRandomAssignedSprint(true, TestObjects.id(), AssignedSprintState.IN_PROGRESS, new Date(), [
                        getRandomAssignedSprintPart(true, TestObjects.id(), AssignedSprintPartState.SUBMITTED),
                        getRandomAssignedSprintPart(),
                    ]),
                ],
            );
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(true, [module.moduleId], module),
            ]);

            const courseAfter = courseBefore.completePart(module.sprints[0].parts[0].id as Id);

            expect(courseAfter.moduleSlots[0].assignedModule?.isInProgress()).toBeTruthy();
            expect(courseAfter.moduleSlots[0].assignedModule?.sprints[0].parts[0].isCompleted()).toBeTruthy();
        });

        it('should complete last module part', () => {
            const firstModule = getRandomAssignedModule(
                true,
                TestObjects.id(),
                AssignedModuleState.IN_PROGRESS,
                new Date(),
                [
                    getRandomAssignedSprint(true, TestObjects.id(), AssignedSprintState.IN_PROGRESS, new Date(), [
                        getRandomAssignedSprintPart(true, TestObjects.id(), AssignedSprintPartState.SUBMITTED),
                    ]),
                ],
            );
            const secondModule = getRandomAssignedModule();
            const courseBefore = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(true, [firstModule.moduleId], firstModule),
                getRandomAssignedModuleSlot(true, [secondModule.moduleId], secondModule),
            ]);

            const courseAfter = courseBefore.completePart(firstModule.sprints[0].parts[0].id as Id);

            expect(courseAfter.moduleSlots[0].assignedModule?.isCompleted()).toBeTruthy();
            expect(courseAfter.moduleSlots[1].assignedModule?.isPending()).toBeTruthy();
        });
    });

    describe('changeSprintDuration', () => {
        let course: AssignedCourse;

        beforeEach(() => {
            const startDate = faker.date.recent();
            const userId = TestObjects.id();
            const modules: AssignedModule[] = [];
            modules.push(getRandomAssignedModule(true, userId, AssignedModuleState.PENDING, startDate));
            modules.push(getRandomAssignedModule(true, userId, AssignedModuleState.PENDING, modules[0].deadline));
            modules.push(getRandomAssignedModule(true, userId, AssignedModuleState.PENDING, modules[1].deadline));
            course = getRandomAssignedCourse(true, [
                getRandomAssignedModuleSlot(true, [modules[0].moduleId], modules[0]),
                getRandomAssignedModuleSlot(true, [modules[1].moduleId], modules[1]),
                getRandomAssignedModuleSlot(true, [modules[2].moduleId], modules[2]),
            ]);
        });

        it('should change first module sprint duration', () => {
            const delta = faker.number.int({ min: 1, max: 10 });
            const newDuration = delta + (course.moduleSlots[0].assignedModule?.sprints[0].duration as number);

            const courseAfter = course.changeSprintDuration(
                course.moduleSlots[0].assignedModule?.sprints[0].id as Id,
                newDuration,
            );

            expect(courseAfter.moduleSlots[0].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[0].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
            expect(courseAfter.moduleSlots[1].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[1].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
            expect(courseAfter.moduleSlots[2].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[2].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
        });

        it('should change middle module sprint duration', () => {
            const delta = faker.number.int({ min: 1, max: 10 });
            const newDuration = delta + (course.moduleSlots[1].assignedModule?.sprints[0].duration as number);

            const courseAfter = course.changeSprintDuration(
                course.moduleSlots[1].assignedModule?.sprints[0].id as Id,
                newDuration,
            );

            expect(courseAfter.moduleSlots[0].assignedModule?.deadline).toEqual(
                course.moduleSlots[0].assignedModule?.deadline,
            );
            expect(courseAfter.moduleSlots[1].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[1].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
            expect(courseAfter.moduleSlots[2].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[2].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
        });

        it('should change last module sprint duration', () => {
            const delta = faker.number.int({ min: 1, max: 10 });
            const newDuration = delta + (course.moduleSlots[2].assignedModule?.sprints[0].duration as number);

            const courseAfter = course.changeSprintDuration(
                course.moduleSlots[2].assignedModule?.sprints[0].id as Id,
                newDuration,
            );

            expect(courseAfter.moduleSlots[0].assignedModule?.deadline).toEqual(
                course.moduleSlots[0].assignedModule?.deadline,
            );
            expect(courseAfter.moduleSlots[1].assignedModule?.deadline).toEqual(
                course.moduleSlots[1].assignedModule?.deadline,
            );
            expect(courseAfter.moduleSlots[2].assignedModule?.deadline).toEqual(
                moment(course.moduleSlots[2].assignedModule?.deadline).add(delta, 'days').toDate(),
            );
        });

        it('should throw error if sprint is not found', () => {
            expect(() => course.changeSprintDuration(TestObjects.uniqueId(), 1)).toThrow(
                new IllegalArgumentError('Sprint not found'),
            );
        });
    });

    describe('getUnassignedModuleSlotWithAvailableOptions', () => {
        const moduleA = getRandomAssignedModule(true);
        const moduleB = getRandomAssignedModule(true);
        const moduleC = getRandomAssignedModule(true);
        const moduleD = getRandomAssignedModule(true);

        it('should return only unassigned modules as available option for empty slot', () => {
            const slotA = getRandomAssignedModuleSlot(
                true,
                [moduleA.moduleId, moduleB.moduleId, moduleD.moduleId],
                moduleA,
            );
            const slotB = getRandomAssignedModuleSlot(true, [
                moduleA.moduleId,
                moduleC.moduleId,
                moduleD.moduleId,
                moduleB.moduleId,
            ]);
            const slotC = getRandomAssignedModuleSlot(true, [moduleA.moduleId, moduleB.moduleId], moduleB);

            const course = getRandomAssignedCourse(true, [slotA, slotB, slotC]);

            const unassignedSlot = course.getModuleSlotWithAvailableOptions(slotB.getIdOrThrow());

            expect(unassignedSlot).toBeDefined();
            expect(unassignedSlot.moduleIds.length).toBe(2);
            expect(unassignedSlot.moduleIds).toEqual(expect.arrayContaining([moduleC.moduleId, moduleD.moduleId]));
        });

        it('should return all available modules for a single unassigned slot', () => {
            const slotA = getRandomAssignedModuleSlot(true, [moduleA.moduleId, moduleB.moduleId, moduleD.moduleId]);

            const course = getRandomAssignedCourse(true, [slotA]);

            const unassignedSlot = course.getModuleSlotWithAvailableOptions(slotA.getIdOrThrow());

            expect(unassignedSlot).toBeDefined();
            expect(unassignedSlot.moduleIds.length).toBe(3);
            expect(unassignedSlot.moduleIds).toEqual(
                expect.arrayContaining([moduleA.moduleId, moduleB.moduleId, moduleD.moduleId]),
            );
        });

        it('should return empty modules array if all slot modules already assinged to other slots', () => {
            const slotA = getRandomAssignedModuleSlot(true, [moduleA.moduleId, moduleB.moduleId], moduleA);
            const slotB = getRandomAssignedModuleSlot(true, [moduleA.moduleId, moduleB.moduleId], moduleB);
            const slotC = getRandomAssignedModuleSlot(true, [moduleA.moduleId, moduleB.moduleId]);

            const course = getRandomAssignedCourse(true, [slotA, slotB, slotC]);

            const unassignedSlot = course.getModuleSlotWithAvailableOptions(slotC.getIdOrThrow());

            expect(unassignedSlot).toBeDefined();
            expect(unassignedSlot.moduleIds.length).toBe(0);
        });

        it('should return empty modules array for assigned module', () => {
            const slotA = getRandomAssignedModuleSlot(
                true,
                [moduleA.moduleId, moduleB.moduleId, moduleD.moduleId],
                moduleA,
            );

            const course = getRandomAssignedCourse(true, [slotA]);

            const assignedSlot = course.getModuleSlotWithAvailableOptions(slotA.getIdOrThrow());

            expect(assignedSlot).toBeDefined();
            expect(assignedSlot.moduleIds.length).toBe(0);
        });

        it('should throw an error if slot not exists', () => {
            const slotA = getRandomAssignedModuleSlot(true, [moduleA.moduleId]);

            const course = getRandomAssignedCourse(true, [slotA]);

            expect(course.getModuleSlotWithAvailableOptions.bind(course, TestObjects.uniqueId())).toThrow(
                new IllegalArgumentError('Slot not found'),
            );
        });
    });

    describe('getSprintPositionInCourse', () => {
        const firstModule = getRandomAssignedModule();
        const secondModule = getRandomAssignedModule();
        const course = getRandomAssignedCourse(true, [
            new AssignedModuleSlot({
                id: TestObjects.uniqueId(),
                assignedModule: firstModule,
                moduleIds: [firstModule.moduleId],
            }),
            new AssignedModuleSlot({
                id: TestObjects.uniqueId(),
                assignedModule: secondModule,
                moduleIds: [secondModule.moduleId],
            }),
        ]).startNextModule();

        it('should get first module first sprint position', () => {
            expect(course.getSprintPositionInCourse(firstModule.sprints[0].sprintId)).toEqual(0);
        });

        it('should get first module second sprint position', () => {
            expect(course.getSprintPositionInCourse(firstModule.sprints[1].sprintId)).toEqual(1);
        });

        it('should get second module first sprint position', () => {
            expect(course.getSprintPositionInCourse(secondModule.sprints[0].sprintId)).toEqual(
                firstModule.sprints.length,
            );
        });

        it('should get second module second sprint position', () => {
            expect(course.getSprintPositionInCourse(secondModule.sprints[1].sprintId)).toEqual(
                firstModule.sprints.length + 1,
            );
        });

        it('should not get unknown sprint position', () => {
            expect(course.getSprintPositionInCourse(TestObjects.uniqueId())).toEqual(-1);
        });
    });
});
