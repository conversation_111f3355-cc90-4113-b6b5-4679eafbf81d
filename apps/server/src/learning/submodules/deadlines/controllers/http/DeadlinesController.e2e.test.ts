import { faker } from '@faker-js/faker';
import moment from 'moment';
import 'reflect-metadata';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../../../config/Configuration';
import { ConfigurationModule } from '../../../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../../../core/infrastructure/di/tokens';
import LearningComponentAbbreviation from '../../../../../education/common/LearningComponentAbbreviation';
import LearningComponentName from '../../../../../education/common/LearningComponentName';
import ModuleSlot from '../../../../../education/courses/domain/ModuleSlot';
import CourseTypeormRepository from '../../../../../education/courses/infrastructure/db/CourseTypeormRepository';
import Module from '../../../../../education/modules/domain/Module';
import ModuleTypeormRepository from '../../../../../education/modules/infrastructure/db/ModuleTypeormRepository';
import Sprint from '../../../../../education/sprints/domain/Sprint';
import SprintPart from '../../../../../education/sprints/domain/SprintPart';
import SprintTypeormRepository from '../../../../../education/sprints/infrastructure/db/SprintTypeormRepository';
import DeadlineChangeRecordRepository from '../../infrastructure/db/audit/DeadlineChangeRecordRepository';
import { DeadlineManager } from '../../services/DeadlineManager';
import { AssignedSprintState } from '../../../roadmap/domain/AssignedSprint';
import Mentor from '../../../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../../../users/staff/infrastructure/db/Staff';
import Student from '../../../../../users/students/management/infrastructure/db/Student';
import StudentType from '../../../../../users/students/settings/domain/StudentType';
import TestObjects from '../../../../../test-toolkit/shared/TestObjects';
import { getRandomSprint, getRandomSprintPart } from '../../../../../education/sprints/controllers/http/e2e-helpers';
import { IntegrationTestBatch } from '../../../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestUser } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { SprintPartContentType } from '../../../../../education/sprints/domain/SprintPartContentType';
import { IntegrationTestGithubProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGithubProfile';
import { IntegrationTestDiscordProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestDiscordProfile';
import { StudentSource } from '../../../../../users/students/settings/domain/StudentSource';
import RoadmapManager from '../../../roadmap/services/RoadmapManager';
import AssignedCourse from '../../../roadmap/domain/AssignedCourse';
import { StudentProfileManager } from '../../../../../users/students/profile/services/StudentProfileManager';
import { StudentSettingsCreator } from '../../../../../users/students/settings/services/StudentSettingsCreator';

async function setupRoadmap(): Promise<IntegrationTestBatch> {
    const sprintRepository = new SprintTypeormRepository(Container.get(TransactionManagerToken));
    const moduleRepository = new ModuleTypeormRepository(Container.get(TransactionManagerToken));
    const courseRepository = new CourseTypeormRepository(Container.get(TransactionManagerToken));

    const sprints = await sprintRepository.saveAll([
        new Sprint({
            ...getRandomSprint({ withId: false, numberOfParts: 1, numberOfSkills: 0 }),
            parts: [
                new SprintPart({
                    ...getRandomSprintPart(),
                    contentType: SprintPartContentType.TEMPLATE,
                }),
            ],
        }),
        new Sprint({
            ...getRandomSprint({ withId: false, numberOfParts: 1, numberOfSkills: 0 }),
            parts: [
                new SprintPart({
                    ...getRandomSprintPart(),
                    contentType: SprintPartContentType.TEMPLATE,
                }),
            ],
        }),
    ]);
    const module = await moduleRepository.save(
        new Module({
            sprints,
            abbreviation: new LearningComponentAbbreviation(TestObjects.uniqueAlpha()),
            name: new LearningComponentName(faker.word.noun(3)),
        }),
    );
    const testCourse = await IntegrationTestCourse.create();
    const course = await courseRepository.save(
        testCourse.params.addModuleSlot(new ModuleSlot({ moduleIds: [module.id!] })),
    );

    return await IntegrationTestBatch.create({ courseId: course.id! });
}

async function completeCourse(userId: Id): Promise<void> {
    const roadmapManager = Container.get(RoadmapManager);
    while (true) {
        const course = await roadmapManager.getCourse(userId);
        const sprint = course?.getCurrentSprint();

        if (!sprint) {
            return;
        }

        for (const part of sprint.parts) {
            await roadmapManager.startCurrentModule(course as AssignedCourse);
            await roadmapManager.submitPart(userId, part.sprintPartId);
            await roadmapManager.completePartById(part.id!);
        }
    }
}

describe('DeadlinesController IT', () => {
    let configuration: Configuration;
    let deadlineManager: DeadlineManager;
    let deadlineChangeRecordRepository: DeadlineChangeRecordRepository;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let activeStudent: IntegrationTestUser<Student>;
    let alumniStudent: IntegrationTestUser<Student>;

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        deadlineManager = Container.get(DeadlineManager);
        deadlineChangeRecordRepository = new DeadlineChangeRecordRepository(Container.get(TransactionManagerToken));

        admin = await IntegrationTestUser.createFakeAdmin();
        staff = await IntegrationTestUser.createFakeStaff();
        mentor = await IntegrationTestUser.createFakeMentor();
        activeStudent = await IntegrationTestUser.createFakeStudent();
        alumniStudent = await IntegrationTestUser.createFakeStudent();
        await admin.authorize();
        await staff.authorize();
        await mentor.authorize();
        await activeStudent.authorize();
        await alumniStudent.authorize();

        const batch = await setupRoadmap();

        const studentSettingsCreator = Container.get(StudentSettingsCreator);
        await studentSettingsCreator.create({
            studentId: activeStudent.id as Id,
            batchId: batch.id as Id,
            startDate: batch.params.startDate,
            endDate: batch.params.endDate,
            studentType: StudentType.regular(),
            source: faker.helpers.enumValue(StudentSource),
        });
        await studentSettingsCreator.create({
            studentId: alumniStudent.id as Id,
            batchId: batch.id as Id,
            startDate: batch.params.startDate,
            endDate: batch.params.endDate,
            studentType: StudentType.regular(),
            source: faker.helpers.enumValue(StudentSource),
        });
        const studentProfileManager = Container.get(StudentProfileManager);
        await studentProfileManager.create(activeStudent.id!);
        await studentProfileManager.create(alumniStudent.id!);

        await IntegrationTestGithubProfile.createCompleted(activeStudent.id as Id);
        await IntegrationTestDiscordProfile.createCompleted(activeStudent.id as Id);
        await IntegrationTestGithubProfile.createCompleted(alumniStudent.id as Id);
        await IntegrationTestDiscordProfile.createCompleted(alumniStudent.id as Id);

        const roadmapManager = Container.get(RoadmapManager);
        await roadmapManager.beginCourse(activeStudent.id!, batch.params.course.id!);
        await roadmapManager.beginCourse(alumniStudent.id!, batch.params.course.id!);
        await completeCourse(alumniStudent.id!);
    });

    describe('POST /deadlines/update-current', () => {
        async function executeRequest({
            deltaDays,
            expectedCode,
            user,
        }: {
            deltaDays: number;
            expectedCode: number;
            user?: IntegrationTestUser<any>;
        }): Promise<void> {
            await request(configuration.server)
                .post('/deadlines/update-current')
                .set('Cookie', `token=${user?.getAuthTokenString()}`)
                .send({ deltaDays })
                .expect(expectedCode);
        }

        it('should extend all current deadlines', async () => {
            const deadlinesBefore = await deadlineManager.getDeadlines(activeStudent.id as Id);

            const deltaDays = faker.number.int({ min: 1, max: 10 });
            await executeRequest({ deltaDays, user: admin, expectedCode: 204 });
            const deadlinesAfter = await deadlineManager.getDeadlines(activeStudent.id as Id);

            expect(deadlinesAfter).toHaveLength(deadlinesBefore.length);
            deadlinesAfter.forEach(({ sprint: { state, deadline, duration } }, i) => {
                expect(deadline).toEqual(moment(deadlinesBefore[i].sprint.deadline).add(deltaDays, 'days').toDate());
                expect(duration).toEqual(
                    deadlinesBefore[i].sprint.duration + (state === AssignedSprintState.IN_PROGRESS ? deltaDays : 0),
                );
            });
        });

        it('should shorten all current deadlines', async () => {
            const deadlinesBefore = await deadlineManager.getDeadlines(activeStudent.id as Id);

            const deltaDays = faker.number.int({ min: -10, max: -1 });
            await executeRequest({ deltaDays, user: admin, expectedCode: 204 });

            const deadlinesAfter = await deadlineManager.getDeadlines(activeStudent.id as Id);

            expect(deadlinesAfter).toHaveLength(deadlinesBefore.length);
            deadlinesAfter.forEach(({ sprint: { state, deadline, duration } }, i) => {
                expect(deadline).toEqual(moment(deadlinesBefore[i].sprint.deadline).add(deltaDays, 'days').toDate());
                expect(duration).toEqual(
                    deadlinesBefore[i].sprint.duration + (state === AssignedSprintState.IN_PROGRESS ? deltaDays : 0),
                );
            });
        });

        it('should create change records for all current sprints', async () => {
            const changeRecordsBefore = await deadlineChangeRecordRepository.getAll();

            const deltaDays = faker.number.int({ min: 1, max: 10 });
            await executeRequest({ deltaDays, user: admin, expectedCode: 204 });

            const changeRecordsAfter = await deadlineChangeRecordRepository.getAll();

            expect(changeRecordsAfter).toHaveLength(changeRecordsBefore.length + 1);
            const latestChangeRecord = changeRecordsAfter.sort((a, b) => b.id!.value - a.id!.value)[0];
            const currentDeadline = await deadlineManager.getCurrentDeadline(activeStudent.id as Id);

            expect(latestChangeRecord).toEqual(
                expect.objectContaining({
                    durationBefore: (currentDeadline?.sprint.duration as number) - deltaDays,
                    durationAfter: currentDeadline?.sprint.duration,
                    creatorId: admin.id!,
                }),
            );
        });

        it('should not change deadlines for alumni students', async () => {
            const deadlinesBefore = await deadlineManager.getDeadlines(alumniStudent.id as Id);

            const deltaDays = faker.number.int({ min: 1, max: 10 });
            await executeRequest({ deltaDays, user: admin, expectedCode: 204 });

            const deadlinesAfter = await deadlineManager.getDeadlines(alumniStudent.id as Id);

            expect(deadlinesAfter).toEqual(deadlinesBefore);
        });

        it('should reject unauthorized requests', async () => {
            await executeRequest({ deltaDays: 1, user: staff, expectedCode: 403 });
            await executeRequest({ deltaDays: 1, user: mentor, expectedCode: 403 });
            await executeRequest({ deltaDays: 1, user: activeStudent, expectedCode: 403 });
            await executeRequest({ deltaDays: 1, expectedCode: 403 });
        });
    });
});
