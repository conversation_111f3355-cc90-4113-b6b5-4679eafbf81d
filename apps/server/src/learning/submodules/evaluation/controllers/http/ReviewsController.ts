import { Request, Response } from 'express';
import {
    Authorized,
    Body,
    ContentType,
    CurrentUser,
    Get,
    HttpCode,
    JsonController,
    Param,
    Put,
    Req,
    Res,
} from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import { Configuration } from '../../../../../config/Configuration';
import { ConfigurationModule } from '../../../../../config/infrastructure/di/ConfigurationModule';
import responses from '../../../../../core/controllers/docs/responses';
import { executeWithRedirect } from '../../../../../core/controllers/redirect';
import Id from '../../../../../core/domain/value-objects/Id';
import Url from '../../../../../core/domain/value-objects/Url';
import NotFoundError from '../../../../../core/errors/NotFoundError';
import { LoggingModule } from '../../../../../logging/infrastructure/di/LoggingModule';
import { LoggingService } from '../../../../../logging/services/LoggingService';
import User, { Role } from '../../../../../users/shared/infrastructure/db/User';
import Logger from '../../../../../utils/logger/Logger';
import ReviewEvaluationManager from '../../services/ReviewEvaluationManager';
import ReviewManager from '../../services/ReviewManager';
import QuestionEvaluationDTO from './QuestionEvaluationDTO';
import ReviewAccessControl from './ReviewAccessControl';
import ReviewCancelDTO from './dto/ReviewCancelDTO';
import ReviewCompleteDTO from './dto/ReviewCompleteDTO';
import ReviewCriticalErrorDTO from './dto/ReviewCriticalErrorDTO';
import ReviewDTO from './dto/ReviewDTO';
import ReviewFeedbackQuestionDTO from './dto/ReviewFeedbackQuestionDTO';
import ReviewQuestionDTO from './dto/ReviewQuestionDTO';
import ReviewSubmitDTO from './dto/ReviewSubmitDTO';
import SubmissionManager from '../../../roadmap/services/submission/SubmissionManager';
import { SprintPartLearningMaterialsAccessor } from '../../../roadmap/services/SprintPartLearningMaterialsAccessor';
import { ReviewFeedback } from '../../domain/ReviewFeedback';

@Service()
@JsonController('/reviews/:id')
export default class ReviewsController {
    private readonly logger: Logger;

    private readonly clientUrl: Url;

    constructor(
        @Inject(ConfigurationModule.CONFIGURATION_TOKEN)
        configuration: Configuration,
        @Inject(LoggingModule.LOGGING_SERVICE_TOKEN)
        loggingService: LoggingService,
        @Inject()
        private readonly reviewManager: ReviewManager,
        @Inject()
        private readonly reviewEvaluationManager: ReviewEvaluationManager,
        @Inject()
        private readonly submissionManager: SubmissionManager,
        @Inject()
        private readonly sprintPartLearningMaterialsAccessor: SprintPartLearningMaterialsAccessor,
    ) {
        this.logger = loggingService.createLogger(ReviewsController.name);
        this.clientUrl = new Url(configuration.client.url);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Get()
    @ResponseSchema(ReviewDTO)
    @OpenAPI({
        responses,
        summary: 'Get a review',
        security: [{ cookieAuth: [] }],
    })
    async getReview(@Param('id') id: number, @CurrentUser() user: User): Promise<ReviewDTO> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyAccessAllowed(user, review);
        return ReviewDTO.fromReview(review, ReviewAccessControl.doShowSensitiveData(user, review));
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Get('/repo')
    @OpenAPI({
        responses,
        summary: 'Redirects to a sprint part repo',
        security: [{ cookieAuth: [] }],
    })
    async redirectToSprintPartRepo(
        @Param('id') id: number,
        @CurrentUser() user: User,
        @Req() req: Request,
        @Res() res: Response,
    ): Promise<Response> {
        return await executeWithRedirect({
            req,
            res,
            defaultUrl: this.clientUrl,
            action: async () => {
                const review = await this.reviewManager.get(new Id(id));
                if (!review) {
                    throw new NotFoundError('Review not found');
                }

                ReviewAccessControl.verifyAccessAllowed(user, review);

                const url = await this.submissionManager.ensureClonedAndGetUrl(review.sprintPart);

                if (!url) {
                    throw new NotFoundError('Submission not found');
                }

                return url;
            },
        });
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Put('/begin')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Begin a review',
        security: [{ cookieAuth: [] }],
    })
    async begin(@Param('id') id: number, @CurrentUser() user: User): Promise<void> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyBeginAllowed(user, review);

        this.logger.debug(`User '${user.id}' is starting review '${id}'`);
        await this.reviewManager.beginReview(new Id(id));
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR])
    @Put('/critical-error')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Update a critical error flag',
        security: [{ cookieAuth: [] }],
    })
    async criticalError(
        @Param('id') id: number,
        @Body() data: ReviewCriticalErrorDTO,
        @CurrentUser() user: User,
    ): Promise<void> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyCriticalErrorAccessAllowed(user, review);

        this.logger.debug(`User '${user.id}' is marking review '${id}' hasCriticalError=${data.hasCriticalError}`);
        await this.reviewManager.changeCriticalErrorFlag(new Id(id), data.hasCriticalError);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Put('/submit')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Submit a review',
        security: [{ cookieAuth: [] }],
    })
    async submitReview(
        @Param('id') id: number,
        @Body() data: ReviewSubmitDTO,
        @CurrentUser() evaluator: User,
    ): Promise<void> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifySubmitAllowed(evaluator, review);

        this.logger.debug(`User '${evaluator.id}' is submitting review '${id}'`);
        await this.reviewManager.submitReview(new Id(id), data.feedback, data.note);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Put('/cancel')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Cancel a review',
        security: [{ cookieAuth: [] }],
    })
    async cancel(@Param('id') id: number, @Body() data: ReviewCancelDTO, @CurrentUser() user: User): Promise<void> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyCancelationAllowed(user, review, data.status);

        this.logger.debug(`User '${user.id}' is canceling review '${id}'`);
        await this.reviewManager.cancelReview(new Id(id), data.status, data.note);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Put('/complete')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Complete a review',
        security: [{ cookieAuth: [] }],
    })
    async complete(@Param('id') id: number, @Body() data: ReviewCompleteDTO, @CurrentUser() user: User): Promise<void> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyCompletetionAllowed(user, review);

        this.logger.debug(`User '${user.id}' is completing review '${id}'`);
        await this.reviewEvaluationManager.complete(
            new Id(id),
            ReviewFeedback.fromParams({
                feedback: data.feedback,
                internalFeedback: data.internalFeedback,
                evaluatorFutureReviewPreference: data.evaluatorFutureReviewPreference,
            }),
        );
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Get('/questions')
    @ResponseSchema(ReviewQuestionDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Get review questions',
        security: [{ cookieAuth: [] }],
    })
    async getReviewQuestions(@Param('id') id: number, @CurrentUser() user: User): Promise<ReviewQuestionDTO[]> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyQuestionAccessAllowed(user, review);

        const questions = await this.reviewManager.getReviewQuestions(new Id(id));

        return questions.map((question) => ReviewQuestionDTO.fromReviewQuestion(question));
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR, Role.USER])
    @Put('/questions/:questionId/evaluation')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Submit review question evaluation',
        security: [{ cookieAuth: [] }],
    })
    async submitQuestionEvaluation(
        @Param('id') reviewId: number,
        @Param('questionId') questionId: number,
        @Body() data: QuestionEvaluationDTO,
        @CurrentUser() user: User,
    ): Promise<void> {
        const review = await this.reviewManager.get(new Id(reviewId));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyQuestionEvaluationAllowed(user, review);

        this.logger.debug(`User '${user.id}' is submitting review question '${questionId}'`);
        await this.reviewManager.submitQuestionEvaluation(new Id(reviewId), new Id(questionId), data.evaluation);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get('/feedback-questions')
    @ResponseSchema(ReviewFeedbackQuestionDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Get feedback questions',
        security: [{ cookieAuth: [] }],
    })
    async getReviewFeedbackQuestions(
        @Param('id') id: number,
        @CurrentUser() user: User,
    ): Promise<ReviewFeedbackQuestionDTO[]> {
        const review = await this.reviewManager.get(new Id(id));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyFeedbackQuestionAccessAllowed(user, review);

        const questions = await this.reviewManager.getReviewFeedbackQuestions(new Id(id));

        return questions.map((question) => ReviewFeedbackQuestionDTO.fromReviewFeedbackQuestion(question));
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Put('/feedback-questions/:questionId/evaluation')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Submit feedback question evaluation',
        security: [{ cookieAuth: [] }],
    })
    async submitFeedbackQuestionEvaluation(
        @Param('id') reviewId: number,
        @Param('questionId') questionId: number,
        @Body() data: QuestionEvaluationDTO,
        @CurrentUser() user: User,
    ): Promise<void> {
        const review = await this.reviewManager.get(new Id(reviewId));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyFeedbackQuestionAccessAllowed(user, review);

        await this.reviewManager.submitFeedbackQuestionEvaluation(
            new Id(reviewId),
            new Id(questionId),
            data.evaluation,
        );
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER, Role.MENTOR])
    @Get('/sprint-part/content')
    @ContentType('text/html')
    @OpenAPI({
        responses,
        summary: 'Returns the sprint part content',
        security: [{ cookieAuth: [] }],
    })
    async getSprintPartContent(@Param('id') reviewId: number, @CurrentUser() user: User): Promise<string> {
        const review = await this.reviewManager.get(new Id(reviewId));
        if (!review) {
            throw new NotFoundError('Review not found');
        }

        ReviewAccessControl.verifyAccessAllowed(user, review);

        const content = await this.sprintPartLearningMaterialsAccessor.downloadNotebook(review.sprintPart.slug);

        if (!content) {
            throw new NotFoundError('Sprint part content not found');
        }

        return content;
    }
}
