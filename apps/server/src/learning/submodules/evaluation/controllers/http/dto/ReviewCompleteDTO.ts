import { IsE<PERSON>, IsOptional, IsString, MinLength } from 'class-validator';
import { ReviewEvaluatorFuturePreference } from '../../../domain/ReviewEvaluatorFuturePreference';

export default class ReviewCompleteDTO {
    @IsString()
    @MinLength(1)
    feedback: string;

    @IsOptional()
    @IsString()
    internalFeedback?: string;

    @IsEnum(ReviewEvaluatorFuturePreference)
    evaluatorFutureReviewPreference: ReviewEvaluatorFuturePreference;
}
