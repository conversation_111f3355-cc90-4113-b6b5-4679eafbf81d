import { ReviewEvaluatorFuturePreference } from './ReviewEvaluatorFuturePreference';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';

export type ReviewFeedbackParams = NonFunctionProperties<ReviewFeedback>;

export class ReviewFeedback {
    feedback: string;

    internalFeedback?: string;

    evaluatorFutureReviewPreference: ReviewEvaluatorFuturePreference;

    protected constructor(params: ReviewFeedbackParams) {
        Object.assign(this, params);
    }

    static fromParams(params: ReviewFeedbackParams): ReviewFeedback {
        return new ReviewFeedback(params);
    }
}
