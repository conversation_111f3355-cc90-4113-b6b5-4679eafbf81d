import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import StateValidation from '../../../../core/utils/validation/StateValidation';
import ReviewProperties from '../../../../education/sprints/domain/ReviewProperties';
import CorrectionRepository from '../../../../corrections/infrastructure/db/CorrectionRepository';
import CorrectionService from '../../../../corrections/services/CorrectionService';
import RoadmapManager from '../../roadmap/services/RoadmapManager';
import { CorrectionStatus } from '../../../../corrections/domain/CorrectionStatus';
import { CorrectionType } from '../../../../corrections/domain/CorrectionType';
import IllegalArgumentError from '../../../../core/errors/IllegalArgumentError';
import { ReviewFeedback } from '../domain/ReviewFeedback';

export default class ReviewEvaluationManager {
    constructor(
        private readonly tm: TransactionManager,
        private readonly roadmapManager: RoadmapManager,
        private readonly correctionService: CorrectionService,
        private readonly correctionRepository: CorrectionRepository,
    ) {}

    async complete(reviewId: Id, feedback: ReviewFeedback): Promise<void> {
        await this.tm.execute(async (tx) => {
            const correction = await this.correctionService.submitFeedback(reviewId.value, feedback, tx);
            if (!correction) {
                throw new IllegalArgumentError('Correction not found');
            }
            const isPartComplete = await this.isEvaluationComplete(new Id(correction.assignedSprintPartId), tx);

            if (isPartComplete) {
                await this.roadmapManager.completePartById(new Id(correction.assignedSprintPartId), tx);
            }
        });
    }

    /**
     * Part is completed if all are true:
     *   - minimum number of successful mentor reviews have been performed
     *   - minimum number of any successful reviews have been performed
     */
    private async isEvaluationComplete(id: Id, tx: Transaction): Promise<boolean> {
        const part = await this.roadmapManager.getPart(id, tx);
        // @ts-expect-error TS(2345) FIXME: Argument of type 'boolean | undefined' is not assi... Remove this comment to see the full error message
        StateValidation.isValid(part?.evaluationProperties.isReview(), 'Invalid review part');

        const reviewProperties = part.evaluationProperties as ReviewProperties;
        const requiredMentorReviews = reviewProperties.numberOfStlReviews;
        const requiredTotalReviews = reviewProperties.numberOfStlReviews + reviewProperties.numberOfPeerReviews;

        const reviews = await this.correctionRepository.findByAssignedSprintPart(id.value, tx);
        const successfulReviews = reviews.filter((c) => c.status === CorrectionStatus.SUCCESS);
        const successfulMentorReviews = successfulReviews.filter((c) => c.type === CorrectionType.MENTOR);

        return (
            successfulReviews.length >= requiredTotalReviews && successfulMentorReviews.length >= requiredMentorReviews
        );
    }
}
