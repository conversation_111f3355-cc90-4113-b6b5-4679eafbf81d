import { faker } from '@faker-js/faker';
import 'reflect-metadata';
import request from 'supertest';
import Container from 'typedi';
import AccessToken from '../../../../auth/domain/AccessToken';
import { Configuration } from '../../../../config/Configuration';
import Arrays from '../../../../core/collections/Arrays';
import { StandupAttendanceRequirement } from '../../../../core/domain/types/StandupAttendance';
import Id from '../../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../../core/infrastructure/di/tokens';
import DiscordRoleId from '../../../../discord/domain/DiscordRoleId';
import LearningComponentName from '../../../common/LearningComponentName';
import CoursesController from './CoursesController';
import CourseCreateDTO from './dto/CourseCreateDTO';
import CourseDTO from './dto/CourseDTO';
import CourseUpdateDTO from './dto/CourseUpdateDTO';
import ModuleSlotDTO from './dto/ModuleSlotDTO';
import Course from '../../domain/Course';
import CourseRepository from '../../domain/CourseRepository';
import ModuleSlot from '../../domain/ModuleSlot';
import CourseTypeormRepository from '../../infrastructure/db/CourseTypeormRepository';
import { ModuleFinderToken } from '../../../modules/infrastructure/di/tokens';
import ModuleFinder from '../../../modules/services/ModuleFinder';
import Mentor from '../../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../../users/staff/infrastructure/db/Staff';
import { TestConfigurationService } from '../../../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { IntegrationTestModule } from '../../../../test-toolkit/e2e/entities/IntegrationTestModule';
import { IntegrationTestUser } from '../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { CourseOnboardingConnection } from '../../domain/CourseOnboardingConnection';

describe(CoursesController.name, () => {
    let configuration: Configuration;
    let courseRepository: CourseRepository;
    let moduleFinder: ModuleFinder;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;

    let moduleA: IntegrationTestModule;
    let moduleB: IntegrationTestModule;

    beforeAll(async () => {
        configuration = TestConfigurationService.create().getConfigurationSync();
        courseRepository = new CourseTypeormRepository(Container.get(TransactionManagerToken));
        moduleFinder = Container.get(ModuleFinderToken);

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());

        moduleA = await IntegrationTestModule.create({ abbreviation: 'COURSECONTA' });
        moduleB = await IntegrationTestModule.create({ abbreviation: 'COURSECONTB' });
    });

    describe('GET /courses', () => {
        async function testGet(token?: AccessToken, code = 200): Promise<void> {
            const courses = await courseRepository.getAll();

            const res = await request(configuration.server)
                .get('/courses')
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            if (code === 200) {
                expect(res.body).toHaveLength(courses.length);
                expect(res.body).toEqual(expect.arrayContaining(courses.map(CourseDTO.fromCourse)));
            }
        }

        it('should get with authorized users', async () => {
            await testGet(admin.authToken, 200);
            await testGet(staff.authToken, 200);
            await testGet(mentor.authToken, 200);
        });

        it('should not get with unauthorized users', async () => {
            await testGet(student.authToken, 403);
            await testGet(undefined, 403);
        });
    });

    describe('POST /courses', () => {
        async function testPost(data: CourseCreateDTO, token?: AccessToken, code = 200): Promise<void> {
            const res = await request(configuration.server)
                .post('/courses')
                .set('Cookie', `token=${token?.token}`)
                .send(data)
                .expect(code);

            if (code === 200) {
                expect(res.body).toEqual(expect.objectContaining(data));

                const course = await courseRepository.getOrFail(new Id(res.body.id));
                expect(course.name).toEqual(new LearningComponentName(data.name, data.internalName));
                expect(course.isEndorsementEnabled).toEqual(data.isEndorsementEnabled);
                expect(course.studentDiscordRoleId).toEqual(
                    data.studentDiscordRoleId ? new DiscordRoleId(data.studentDiscordRoleId) : undefined,
                );
                expect(course.mentorDiscordRoleId).toEqual(
                    data.mentorDiscordRoleId ? new DiscordRoleId(data.mentorDiscordRoleId) : undefined,
                );
            }
        }

        it('should create with authorized users', async () => {
            const properties: CourseCreateDTO = {
                name: TestObjects.uniqueWords(),
                internalName: TestObjects.uniqueWords(),
                isEndorsementEnabled: faker.datatype.boolean(),
                studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
            };

            await testPost(properties, admin.authToken, 200);
        });

        it('should create without discord roles', async () => {
            const properties: CourseCreateDTO = {
                name: TestObjects.uniqueWords(),
                internalName: TestObjects.uniqueWords(),
                isEndorsementEnabled: faker.datatype.boolean(),
                onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
            };

            await testPost(properties, admin.authToken, 200);
        });

        it('should not create with unauthorized users', async () => {
            const properties: CourseCreateDTO = {
                name: TestObjects.uniqueWords(),
                internalName: TestObjects.uniqueWords(),
                isEndorsementEnabled: faker.datatype.boolean(),
                studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
            };

            await testPost(properties, staff.authToken, 403);
            await testPost(properties, mentor.authToken, 403);
            await testPost(properties, student.authToken, 403);
            await testPost(properties, undefined, 403);
        });

        it('should not create with duplicate name', async () => {
            const properties: CourseCreateDTO = {
                name: TestObjects.uniqueWords(),
                internalName: TestObjects.uniqueWords(),
                isEndorsementEnabled: faker.datatype.boolean(),
                studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
            };

            await testPost(properties, admin.authToken, 200);
            await testPost(properties, admin.authToken, 400);
        });

        it('should not create with empty connections', async () => {
            const properties: CourseCreateDTO = {
                name: TestObjects.uniqueWords(),
                internalName: TestObjects.uniqueWords(),
                isEndorsementEnabled: faker.datatype.boolean(),
                studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                onboardingConnections: [] as CourseOnboardingConnection[],
            };

            await testPost(properties, admin.authToken, 400);
        });
    });

    describe('PATCH /courses/:id', () => {
        let courseId: Id;

        async function testPatch(id: Id, data: CourseUpdateDTO, token?: AccessToken, code = 200): Promise<void> {
            const courseBefore = await courseRepository.getOrFail(id);

            const res = await request(configuration.server)
                .patch(`/courses/${id.value}`)
                .set('Cookie', `token=${token?.token}`)
                .send(data)
                .expect(code);

            if (code === 200) {
                expect(res.body).toEqual(expect.objectContaining(data));

                const courseAfter = await courseRepository.getOrFail(id);
                expect(courseAfter.name.name).toEqual(data.name ?? courseBefore.name.name);
                expect(courseAfter.name.internalName).toEqual(data.internalName ?? courseBefore.name.internalName);
                expect(courseAfter.isEndorsementEnabled).toEqual(
                    data.isEndorsementEnabled ?? courseBefore.isEndorsementEnabled,
                );
                expect(courseAfter.studentDiscordRoleId).toEqual(
                    data.studentDiscordRoleId
                        ? new DiscordRoleId(data.studentDiscordRoleId)
                        : courseBefore.studentDiscordRoleId,
                );
            } else {
                const courseAfter = await courseRepository.get(id);
                expect(courseAfter).toEqual(courseBefore);
            }
        }

        beforeAll(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWord()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            courseId = course.id as Id;
        });

        it('should update with authorized users', async () => {
            await testPatch(
                courseId,
                {
                    name: TestObjects.uniqueWords(),
                    internalName: TestObjects.uniqueWords(),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                },
                admin.authToken,
                200,
            );
            // await testPatch(courseId, { name: TestObjects.uniqueWords() }, admin.authToken, 200);
            // await testPatch(courseId, { isEndorsementEnabled: faker.datatype.boolean() }, admin.authToken, 200);
        });

        it('should not update with unauthorized users', async () => {
            const properties = { name: TestObjects.uniqueWords() };

            await testPatch(courseId, properties, staff.authToken, 403);
            await testPatch(courseId, properties, mentor.authToken, 403);
            await testPatch(courseId, properties, student.authToken, 403);
            await testPatch(courseId, properties, undefined, 403);
        });

        it('should not update with duplicate internal name', async () => {
            const anotherCourse = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWord()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            await testPatch(courseId, { internalName: anotherCourse.name.internalName }, admin.authToken, 400);
        });

        it('should not update with empty connections', async () => {
            const anotherCourse = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWord()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            await testPatch(anotherCourse.getIdOrThrow(), { onboardingConnections: [] }, admin.authToken, 400);
        });
    });

    describe('DELETE /courses/:id', () => {
        let courseId: Id;

        async function testDelete(id: Id, token?: AccessToken, code = 204): Promise<void> {
            await request(configuration.server)
                .delete(`/courses/${id.value}`)
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            if (code === 204) {
                await expect(courseRepository.get(id)).resolves.toBeUndefined();
            } else {
                await expect(courseRepository.get(id)).resolves.toBeDefined();
            }
        }

        beforeEach(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWords()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            courseId = course.id as Id;
        });

        it('should delete with authorized users', async () => {
            await testDelete(courseId, admin.authToken, 204);
        });

        it('should not delete with unauthorized users', async () => {
            await testDelete(courseId, staff.authToken, 403);
            await testDelete(courseId, mentor.authToken, 403);
            await testDelete(courseId, student.authToken, 403);
            await testDelete(courseId, undefined, 403);
        });
    });

    describe('GET /courses/:id/slots', () => {
        let courseId: Id;

        async function testGet(token?: AccessToken, code = 200): Promise<void> {
            const res = await request(configuration.server)
                .get(`/courses/${courseId.value}/slots`)
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            if (code === 200) {
                const course = await courseRepository.getOrFail(courseId);
                const modules = await moduleFinder.findByIds(
                    Arrays.unique(course.slots.flatMap((slot) => slot.moduleIds)),
                );

                expect(res.body).toHaveLength(course.slots.length);
                expect(res.body).toEqual(expect.arrayContaining(ModuleSlotDTO.fromCourseAndModules(course, modules)));
            }
        }

        beforeEach(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWords()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            courseId = course.id as Id;
        });

        it('should get with authorized users', async () => {
            await testGet(admin.authToken, 200);
            await testGet(staff.authToken, 200);
        });

        it('should not get with unauthorized users', async () => {
            await testGet(mentor.authToken, 403);
            await testGet(student.authToken, 403);
            await testGet(undefined, 403);
        });
    });

    describe('POST /courses/:id/slots', () => {
        let courseId: Id;

        async function testPost(
            moduleIds: Id[],
            standupAttendanceRequirement: StandupAttendanceRequirement = StandupAttendanceRequirement.RECOMMENDED,
            token?: AccessToken,
            code = 200,
        ): Promise<void> {
            const courseBefore = await courseRepository.getOrFail(courseId);

            const res = await request(configuration.server)
                .post(`/courses/${courseId.value}/slots`)
                .set('Cookie', `token=${token?.token}`)
                .send({
                    moduleIds: moduleIds.map((m) => m.value),
                    standupAttendanceRequirement,
                });

            const courseAfter = await courseRepository.getOrFail(courseId);

            if (code === 200) {
                expect(courseAfter.slots.length).toEqual(courseBefore.slots.length + 1);
                expect(courseAfter.slots[courseAfter.slots.length - 1].moduleIds).toEqual(moduleIds);
                expect(courseAfter.slots[courseAfter.slots.length - 1].standupAttendanceRequirement).toEqual(
                    standupAttendanceRequirement,
                );
                expect(res.body).toHaveLength(courseAfter.slots.length);
            } else {
                expect(courseAfter).toEqual(courseBefore);
            }
        }

        beforeAll(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWords()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }),
            );

            courseId = course.id as Id;
        });

        it('should add a slot', async () => {
            await testPost([moduleB.getIdOrThrow()], undefined, admin.authToken, 200);
        });

        it('should not add a slot with unauthorized users', async () => {
            await testPost([moduleB.getIdOrThrow()], undefined, staff.authToken, 403);
            await testPost([moduleB.getIdOrThrow()], undefined, mentor.authToken, 403);
            await testPost([moduleB.getIdOrThrow()], undefined, student.authToken, 403);
            await testPost([moduleB.getIdOrThrow()], undefined, undefined, 403);
        });
    });

    describe('PUT /courses/:id/slots/:id', () => {
        let courseId: Id;

        async function testPut(
            moduleIds: Id[],
            standupAttendanceRequirement: StandupAttendanceRequirement = StandupAttendanceRequirement.RECOMMENDED,
            token?: AccessToken,
            code = 200,
        ): Promise<void> {
            const courseBefore = await courseRepository.getOrFail(courseId);

            const res = await request(configuration.server)
                .put(`/courses/${courseId.value}/slots/${courseBefore.slots[0].getIdOrThrow().value}`)
                .set('Cookie', `token=${token?.token}`)
                .send({ moduleIds: moduleIds.map((m) => m.value), standupAttendanceRequirement })
                .expect(code);

            const courseAfter = await courseRepository.getOrFail(courseId);

            if (code === 200) {
                expect(courseAfter.slots.length).toEqual(courseBefore.slots.length);
                expect(courseAfter.slots[0].moduleIds).toEqual(expect.arrayContaining(moduleIds));
                expect(courseAfter.slots[0].standupAttendanceRequirement).toEqual(standupAttendanceRequirement);
                expect(res.body).toHaveLength(courseAfter.slots.length);
            } else {
                expect(courseAfter).toEqual(courseBefore);
            }
        }

        beforeAll(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWords()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }).addModuleSlot(
                    new ModuleSlot({
                        moduleIds: [moduleA.getIdOrThrow()],
                    }),
                ),
            );

            courseId = course.id as Id;
        });

        it('should update a slot', async () => {
            await testPut(
                [moduleA.params.getIdOrThrow(), moduleB.params.getIdOrThrow()],
                undefined,
                admin.authToken,
                200,
            );
        });

        it('should not update with unauthorized users', async () => {
            await testPut([moduleA.getIdOrThrow()], undefined, staff.authToken, 403);
            await testPut([moduleA.getIdOrThrow()], undefined, mentor.authToken, 403);
            await testPut([moduleA.getIdOrThrow()], undefined, student.authToken, 403);
            await testPut([moduleA.getIdOrThrow()], undefined, undefined, 403);
        });
    });

    describe('DELETE /courses/:id/slots/:id', () => {
        let courseId: Id;

        async function testDelete(token?: AccessToken, code = 204): Promise<void> {
            const courseBefore = await courseRepository.getOrFail(courseId);

            await request(configuration.server)
                .delete(`/courses/${courseId.value}/slots/${courseBefore.slots[0].getIdOrThrow().value}`)
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            const courseAfter = await courseRepository.getOrFail(courseId);

            if (code === 204) {
                expect(courseAfter.slots.length).toEqual(courseBefore.slots.length - 1);
                expect(courseAfter.slots.some((s) => s.getIdOrThrow().equals(courseBefore.slots[0].id))).toBeFalsy();
            } else {
                expect(courseAfter).toEqual(courseBefore);
                expect(courseAfter.slots.some((s) => s.getIdOrThrow().equals(courseBefore.slots[0].id))).toBeTruthy();
            }
        }

        beforeEach(async () => {
            const course = await courseRepository.save(
                new Course({
                    name: new LearningComponentName(TestObjects.uniqueWords(), TestObjects.uniqueWords()),
                    isEndorsementEnabled: faker.datatype.boolean(),
                    studentDiscordRoleId: TestObjects.uniqueSnowflake(),
                    mentorDiscordRoleId: TestObjects.uniqueSnowflake(),
                    onboardingConnections: [faker.helpers.enumValue(CourseOnboardingConnection)],
                }).addModuleSlot(
                    new ModuleSlot({
                        moduleIds: [moduleA.getIdOrThrow()],
                    }),
                ),
            );

            courseId = course.id as Id;
        });

        it('should delete a slot', async () => {
            await testDelete(admin.authToken);
        });

        it('should not delete with unauthorized users', async () => {
            await testDelete(staff.authToken, 403);
            await testDelete(mentor.authToken, 403);
            await testDelete(student.authToken, 403);
            await testDelete(undefined, 403);
        });
    });
});
